import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { NavigationProvider } from './contexts/NavigationContext';
import { PulseProvider } from './contexts/PulseContext';
import { ConfigProvider as AntdConfigProvider } from 'antd';
import { ANTD_THEME } from './lib/AntdTheme';
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

const queryClient = new QueryClient();

const Providers: React.FC<{ children: JSX.Element }> = ({ children }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <PulseProvider>
        <BrowserRouter>
          <NavigationProvider>
            <AuthProvider>
              <AntdConfigProvider theme={ANTD_THEME}>{children}</AntdConfigProvider>
              {/* <ReactQueryDevtools initialIsOpen={true} /> */}
            </AuthProvider>
          </NavigationProvider>
        </BrowserRouter>
      </PulseProvider>
    </QueryClientProvider>
  );
};

export default Providers;
