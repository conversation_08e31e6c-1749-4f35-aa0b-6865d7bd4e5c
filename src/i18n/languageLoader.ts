import EN from './languages/en';
import FR from './languages/fr';

/** Type representing the structure of language strings */
export type LanguageStrings = typeof EN;

/** Supported language codes */
export type SupportedLanguage = keyof typeof languageData;

/** Object containing all supported language data */
export const languageData = {
  EN,
  FR,
} as const;

/** Type for nested keys in the LanguageStrings object */
type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`;
}[keyof ObjectType & (string | number)];

/** Type representing a valid path in the language strings */
export type LanguagePath = NestedKeyOf<LanguageStrings>;

/** Type for variables passed to the translator function */
export type TranslationVariables = Record<string, string | number | boolean>;

/**
 * Checks if a given key is a supported language
 * @param key - The language key to check
 * @returns True if the language is supported, false otherwise
 */
function isSupportedLanguage(key: string): key is SupportedLanguage {
  return key in languageData;
}

/**
 * Loads language strings for a given language
 * @param lang - The language to load
 * @param fallbackLang - The fallback language to use if the requested language is not found
 * @returns The loaded language strings
 * @throws Error if both the requested language and fallback language are not supported
 */
export function loadLanguage(
  lang: string,
  fallbackLang: SupportedLanguage = 'EN'
): LanguageStrings {
  if (isSupportedLanguage(lang)) {
    return languageData[lang];
  }
  if (isSupportedLanguage(fallbackLang)) {
    console.warn(`Language ${lang} not found. Falling back to ${fallbackLang}.`);
    return languageData[fallbackLang];
  }
  throw new Error(`Unsupported language: ${lang} and fallback language: ${fallbackLang}`);
}

/**
 * Gets the current language from local storage or returns the default
 * @param defaultLang - The default language to use if no language is set
 * @returns The current language code
 */
export function getCurrentLanguage(defaultLang: SupportedLanguage = 'EN'): SupportedLanguage {
  const storedLang = localStorage.getItem('language');
  return storedLang && isSupportedLanguage(storedLang) ? storedLang : defaultLang;
}

/**
 * Sets the current language in local storage
 * @param lang - The language code to set
 */
export function setCurrentLanguage(lang: SupportedLanguage): void {
  localStorage.setItem('language', lang);
}

/**
 * Retrieves a nested value from an object based on a dot-notated path
 * @param obj - The object to retrieve the value from
 * @param path - The dot-notated path to the desired value
 * @returns The value at the specified path
 * @throws Error if the path is invalid or doesn't lead to a string
 */
export function getNestedValue<T extends object>(obj: T, path: NestedKeyOf<T>): string {
  const value = path?.split('.').reduce((acc: any, part: string) => acc && acc[part], obj);
  if (typeof value !== 'string') {
    throw new Error(`Invalid path or non-string value: ${path}`);
  }
  return value;
}

/**
 * Interpolates variables into a string
 * @param str - The string to interpolate
 * @param vars - The variables to interpolate
 * @returns The interpolated string
 */
function interpolateString(str: string, vars: TranslationVariables): string {
  return str.replace(/\{\{(\w+)\}\}/g, (_, key) => {
    if (Object.prototype.hasOwnProperty.call(vars, key)) {
      const value = vars[key];
      if (typeof value === 'boolean') {
        return value ? 'true' : 'false';
      }
      return String(value);
    }
    return `{{${key}}}`;
  });
}

/**
 * Creates a translator function for a given set of language strings
 * @param strings - The language strings to use for translation
 * @returns A function that translates based on the given language strings
 */
export function createTranslator(strings: LanguageStrings) {
  return (path: LanguagePath, vars?: TranslationVariables) => {
    const value = getNestedValue(strings, path);
    return vars ? interpolateString(value, vars) : value;
  };
}

export const translator = (path: LanguagePath, vars?: TranslationVariables) => {
  const string = loadLanguage(getCurrentLanguage());
  return createTranslator(string)(path, vars);
};
