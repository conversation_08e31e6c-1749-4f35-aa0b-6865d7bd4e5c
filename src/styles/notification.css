@import '../styles/theme.css';

.ant-notification-notice-wrapper:has(> .ant-notification-notice-success) {
  border: 1px solid var(--success-500);
  background-color: var(--success-50);
  height: auto !important;
}

.ant-notification-notice-wrapper:has(> .ant-notification-notice-error) {
  border: 1px solid var(--error-500);
  background-color: var(--error-50);
  height: auto !important;
}

.ant-notification-notice-wrapper:has(> .ant-notification-notice-info) {
  border: 1px solid var(--primary-600);
  background-color: var(--primary-50);
  height: auto !important;
}

.ant-notification-notice-wrapper:has(> .ant-notification-notice-warning) {
  border: 1px solid var(--warning-500);
  background-color: var(--warning-50);
  height: auto !important;
}
.ant-notification-notice-message {
  font-family: var(--font-family);
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}
.ant-notification-notice-description {
  font-family: var(--font-family);
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}
.notify-wrapper {
  padding: 13px 14px !important;
}
.ant-notification-notice {
  width: 395px !important;
}
.ant-notification-notice-close {
  inset-inline-end: 14px !important;
  top: 12px !important;
}
.ant-notification-notice-icon {
  margin-top: -2px;
}
