import { RouteObject } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import GeneralComponent from '@/components/sidebar/settings/general';
import PartnersComponent from '@/components/sidebar/settings/partners';
import TemplatesComponent from '@/components/sidebar/settings/templates';
import { ROUTE_ROLE_RESTRICTION } from '@/constant/RouteRestrictionsConstant';
import { ROUTES } from '@/constant/RoutesConstant';

export const SettingsRoutes: RouteObject[] = [
  {
    path: ROUTES.SETTINGS.SETTINGS_GENERAL,
    element: (
      <ProtectedRoute
        element={<GeneralComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.SETTINGS_GENERAL}
      />
    ),
  },
  {
    path: ROUTES.SETTINGS.SETTINGS_PARTNERS,
    element: (
      <ProtectedRoute
        element={<PartnersComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.SETTINGS_PARTNERS}
      />
    ),
  },
  {
    path: ROUTES.SETTINGS.SETTINGS_TEMPLATE,
    element: (
      <ProtectedRoute
        element={<TemplatesComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.SETTINGS_TEMPLATES}
      />
    ),
  },
];
