import { Navigate, RouteObject } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import { DashboardPage } from '@/pages/Dashboard';
import { TrackingPage } from '@/pages/TrackingPage';
import PartnerComponent from '@/components/sidebar/customer/partner';
import BillingComponent from '@/components/sidebar/customer/billing';
import { ROUTE_ROLE_RESTRICTION } from '@/constant/RouteRestrictionsConstant';
import { ROUTES } from '@/constant/RoutesConstant';
import Customer from '@pages/customer';
import CustomerTabs from '@/pages/customer/customerOperations';

export const CustomerRoutes: RouteObject[] = [
  {
    path: ROUTES.CUSTOMER.CUSTOMER_DASHBOARD,
    element: <ProtectedRoute element={<DashboardPage />} allowedRoles={['Tenant', 'Dispatcher']} />,
  },
  {
    path: ROUTES.CUSTOMER.CUSTOMER_PARTNER,
    element: (
      <ProtectedRoute
        element={<PartnerComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.CUSTOMER_PARTNER}
      />
    ),
  },
  {
    path: ROUTES.CUSTOMER.CUSTOMER_TRACKING,
    element: <ProtectedRoute element={<TrackingPage />} allowedRoles={['Tenant', 'Dispatcher']} />,
  },
  {
    path: ROUTES.COMMON.DEFAULT,
    element: <Navigate to={ROUTES.CUSTOMER.CUSTOMER_CUSTOMERS} replace />,
  },
  {
    path: ROUTES.CUSTOMER.CUSTOMER_CUSTOMERS,
    element: (
      <ProtectedRoute
        element={<Customer />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.CUSTOMER_CUSTOMERS}
      />
    ),
  },
  {
    path: ROUTES.CUSTOMER.CUSTOMER_BILLING,
    element: (
      <ProtectedRoute
        element={<BillingComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.CUSTOMER_BILLING}
      />
    ),
  },
  {
    path: ROUTES.CUSTOMER.CUSTOMER_TAB,
    element: (
      <ProtectedRoute
        element={<CustomerTabs />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.CUSTOMER_CUSTOMERS}
      />
    ),
  },
  {
    path: ROUTES.CUSTOMER.CUSTOMER_ADD,
    element: (
      <ProtectedRoute
        element={<CustomerTabs />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.CUSTOMER_CUSTOMERS}
      />
    ),
  },
];
