import { ROUTES } from '@/constant/RoutesConstant';
import { RouteObject } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import InvoiceGrid from '@/pages/billing/invoices';
import { ROUTE_ROLE_RESTRICTION } from '@/constant/RouteRestrictionsConstant';
import PaymentsGrid from '@/pages/billing/payments';
import CreateInvoiceComponent from '@/pages/billing/invoices/invoicesOperations/createInvoice';
import PaymentOperationalForm from '@/pages/billing/payments/paymentOperations';

export const BillingRoutes: RouteObject[] = [
  {
    path: ROUTES.BILLING.BILLING_INVOICES_GRID,
    element: (
      <ProtectedRoute
        element={<InvoiceGrid />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.BILLING_INVOICE}
      />
    ),
  },
  {
    path: ROUTES.BILLING.BILLING_PAYMENTS_GRID,
    element: (
      <ProtectedRoute
        element={<PaymentsGrid />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.BILLING_PAYMENT}
      />
    ),
  },
  {
    path: ROUTES.BILLING.BILLING_CREATE_INVOICE,
    element: (
      <ProtectedRoute
        element={<CreateInvoiceComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.BILLING_INVOICE}
      />
    ),
  },
  {
    path: ROUTES.BILLING.BILLING_ADD_PAYMENT,
    element: (
      <ProtectedRoute
        element={<PaymentOperationalForm />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.BILLING_PAYMENT}
      />
    ),
  },
  {
    path: ROUTES.BILLING.BILLING_EDIT_PAYMENT,
    element: (
      <ProtectedRoute
        element={<PaymentOperationalForm />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.BILLING_PAYMENT}
      />
    ),
  },
];
