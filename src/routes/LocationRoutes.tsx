import { RouteObject } from 'react-router-dom';
import RoutesComponent from '@/components/sidebar/location/routes';
import { ROUTE_ROLE_RESTRICTION } from '@/constant/RouteRestrictionsConstant';
import { ROUTES } from '@/constant/RoutesConstant';
import ProtectedRoute from './ProtectedRoute';
import AddressPage from '@/pages/location/address';
import ZonePage from '@pages/location/zone/list';
import Index from '@pages/location/zone/lookupTable';
import AddZoneLookupTablePage from '@pages/location/zone/lookupTable/addZoneLookup.tsx';

export const LocationRoutes: RouteObject[] = [
  {
    path: ROUTES.LOCATION.LOCATION_ADDRESS,
    element: (
      <ProtectedRoute
        element={<AddressPage />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOCATION_ADDRESS}
      />
    ),
  },
  {
    path: ROUTES.LOCATION.LOCATION_ZONE,
    element: (
      <ProtectedRoute element={<ZonePage />} allowedRoles={ROUTE_ROLE_RESTRICTION.LOCATION_ZONE} />
    ),
  },
  {
    path: ROUTES.LOCATION.LOCATION_ZONE_LOOKUP_TABLE,
    element: (
      <ProtectedRoute element={<Index />} allowedRoles={ROUTE_ROLE_RESTRICTION.LOCATION_ZONE} />
    ),
  },
  {
    path: ROUTES.LOCATION.LOCATION_ZONE_LOOKUP_TABLE_ADD,
    element: (
      <ProtectedRoute
        element={<AddZoneLookupTablePage />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOCATION_ZONE}
      />
    ),
  },
  {
    path: ROUTES.LOCATION.LOCATION_ZONE_LOOKUP_TABLE_EDIT,
    element: (
      <ProtectedRoute
        element={<AddZoneLookupTablePage />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOCATION_ZONE}
      />
    ),
  },
  {
    path: ROUTES.LOCATION.LOCATION_ROUTES,
    element: (
      <ProtectedRoute
        element={<RoutesComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOCATION_ROUTES}
      />
    ),
  },
];
