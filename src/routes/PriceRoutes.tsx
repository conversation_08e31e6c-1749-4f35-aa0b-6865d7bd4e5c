import { RouteObject } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import PriceSetComponent from '@/pages/prices/priceSets';
import { ROUTE_ROLE_RESTRICTION } from '@/constant/RouteRestrictionsConstant';
import { ROUTES } from '@/constant/RoutesConstant';
import Index from '@pages/priceRules/priceModifier/index';
import AddModifier from '@pages/priceRules/priceModifier/oprations/addModifier';
import PriceSetOperation from '@/pages/prices/priceSets/priceSetsOperation';
import PricesGroupModifiers from '@/pages/priceRules/priceModifier/oprations/groupModifier';

export const PriceRoutes: RouteObject[] = [
  {
    path: ROUTES.PRICES.PRICES_PRICE_SETS,
    element: (
      <ProtectedRoute
        element={<PriceSetComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.PRICES_PRICE_SETS}
      />
    ),
  },
  {
    path: ROUTES.PRICES.PRICES_PRICE_SETS_ADD,
    element: (
      <ProtectedRoute
        element={<PriceSetOperation />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.PRICES_PRICE_SETS}
      />
    ),
  },
  {
    path: ROUTES.PRICES.PRICES_PRICE_SETS_EDIT,
    element: (
      <ProtectedRoute
        element={<PriceSetOperation />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.PRICES_PRICE_SETS}
      />
    ),
  },
  {
    path: ROUTES.PRICES.PRICES_PRICE_MODIFIERS,
    element: (
      <ProtectedRoute
        element={<Index />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.PRICES_PRICE_MODIFIERS}
      />
    ),
  },
  {
    path: ROUTES.PRICES.PRICES_PRICE_MODIFIER_ADD,
    element: (
      <ProtectedRoute
        element={<AddModifier />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.PRICES_PRICE_MODIFIERS}
      />
    ),
  },
  {
    path: ROUTES.PRICES.PRICES_PRICE_MODIFIER_EDIT,
    element: (
      <ProtectedRoute
        element={<AddModifier />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.PRICES_PRICE_MODIFIERS}
      />
    ),
  },
  {
    path: ROUTES.PRICES.PRICES_PRICE_GROUP_MODIFIER_ADD,
    element: (
      <ProtectedRoute
        element={<PricesGroupModifiers />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.PRICES_PRICE_MODIFIERS}
      />
    ),
  },
  {
    path: ROUTES.PRICES.PRICES_PRICE_GROUP_MODIFIER_EDIT,
    element: (
      <ProtectedRoute
        element={<PricesGroupModifiers />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.PRICES_PRICE_MODIFIERS}
      />
    ),
  },
];
