import React from 'react';
import { RouteObject, useRoutes, Outlet } from 'react-router-dom';
import { CustomerRoutes } from './CustomerRoutes';
import { LocationRoutes } from './LocationRoutes';
import { LogisticsRoutes } from './LogisticsRoutes';
import { SettingsRoutes } from './SettingRoutes';
import { PriceRoutes } from './PriceRoutes';
import { CommonRoutes } from './CommonRoutes';
import SidebarLayout from '@/components/layout/SidebarLayout/SidebarLayout';
import ProtectedRoute from './ProtectedRoute';
import { BillingRoutes } from './BillingRoutes';

const SidebarWrapper: React.FC = () => {
  return (
    <ProtectedRoute
      allowedRoles={['Tenant', 'Dispatcher']}
      element={
        <SidebarLayout>
          <Outlet />
        </SidebarLayout>
      }
    />
  );
};

const AppRoutes: React.FC = () => {
  const routes: RouteObject[] = [
    ...CommonRoutes,
    {
      path: '/',
      element: <SidebarWrapper />,
      children: [
        ...CustomerRoutes,
        ...LocationRoutes,
        ...LogisticsRoutes,
        ...SettingsRoutes,
        ...PriceRoutes,
        ...BillingRoutes,
      ],
    },
  ];

  const Router = useRoutes(routes);

  return <>{Router}</>;
};

export default AppRoutes;
