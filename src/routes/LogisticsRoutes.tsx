import { RouteObject } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import DispatcherComponent from '@/components/sidebar/logistic/dispatcher';
import VehiclePage from '@/pages/logistics/vehicle';
import { ROUTE_ROLE_RESTRICTION } from '@/constant/RouteRestrictionsConstant';
import { ROUTES } from '@/constant/RoutesConstant';
import VehicleOperationForm from '@/pages/logistics/vehicle/vehicleOperation';
import OrdersPage from '@/pages/logistics/orders/OrdersPage';
import OrdersInfoComponent from '@/pages/logistics/orders/ordersOperation';

export const LogisticsRoutes: RouteObject[] = [
  {
    path: ROUTES.LOGISTIC.LOGISTICS_ORDERS,
    element: (
      <ProtectedRoute
        element={<OrdersPage />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOGISTICS_ORDERS}
      />
    ),
  },
  {
    path: ROUTES.LOGISTIC.LOGISTICS_DISPATCHER,
    element: (
      <ProtectedRoute
        element={<DispatcherComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOGISTICS_DISPATCHER}
      />
    ),
  },
  {
    path: ROUTES.LOGISTIC.LOGISTICS_VEHICLE,
    element: (
      <ProtectedRoute
        element={<VehiclePage />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOGISTICS_VEHICLE}
      />
    ),
  },
  {
    path: ROUTES.LOGISTIC.LOGISTICS_VEHICLE_ADD,
    element: (
      <ProtectedRoute
        element={<VehicleOperationForm />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOGISTICS_VEHICLE}
      />
    ),
  },
  {
    path: ROUTES.LOGISTIC.LOGISTICS_VEHICLE_EDIT,
    element: (
      <ProtectedRoute
        element={<VehicleOperationForm />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOGISTICS_VEHICLE}
      />
    ),
  },
  {
    path: ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION,
    element: (
      <ProtectedRoute
        element={<OrdersInfoComponent />}
        allowedRoles={ROUTE_ROLE_RESTRICTION.LOGISTICS_ORDERS}
      />
    ),
  },
];
