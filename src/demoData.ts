import { Plans } from './types/enums/Plans';
import { Roles } from './types/enums/Roles';
import {
  IAccessRights,
  IPlanBased<PERSON><PERSON>ure<PERSON><PERSON><PERSON>,
  IRoleBased<PERSON><PERSON><PERSON><PERSON><PERSON>,
  IUser,
} from './types/UserTypes';

const planBasedFeature: IPlanBasedFeatureChecker = {
  [Plans.FREE]: new Set(['basic-delivery', 'basic-tracking']),
  [Plans.BASIC]: new Set(['basic-delivery', 'basic-tracking', 'route-optimization']),
  [Plans.PREMIUM]: new Set([
    'basic-delivery',
    'basic-tracking',
    'route-optimization',
    'analytics-basic',
    'bulk-import',
    'manageColumn',
  ]),
  [Plans.ENTERPRISE]: new Set([
    'basic-delivery',
    'basic-tracking',
    'route-optimization',
    'analytics-basic',
    'analytics-advanced',
    'bulk-import',
    'api-access',
    'manageColumn',
    'search-highlight',
  ]),
};

const roleBasedActions: IRoleBasedActionsChecker = {
  [Roles.TENANT]: {
    customer: ['create', 'read', 'update', 'delete'],
    deliveries: ['create', 'read', 'update', 'delete'],
    users: ['create', 'read', 'update', 'delete'],
    reports: ['create', 'read', 'update', 'delete'],
    analytics: ['create', 'read', 'update', 'delete'],
    settings: ['create', 'read', 'update', 'delete'],
  },
  [Roles.DISPATCHER]: {
    customer: ['read', 'update', 'delete'],
    deliveries: ['create', 'read', 'update'],
    users: ['read'],
    reports: ['read', 'create'],
    analytics: ['read'],
    settings: ['read', 'update'],
  },
};

const accessRight: IAccessRights = {
  planBasedFeature,
  roleBasedActions,
};

export const users: IUser[] = [
  {
    id: '',
    companyName: 'John Doe',
    email: '<EMAIL>',
    password: 'enterprise123',
    role: Roles.TENANT,
    plan: Plans.ENTERPRISE,
    accessRight,
    accessToken: '',
  },
  {
    id: '',
    companyName: 'Sarah Smith',
    email: '<EMAIL>',
    password: 'dispatch123',
    role: Roles.DISPATCHER,
    plan: Plans.ENTERPRISE,
    accessRight,
    accessToken: '',
  },
  {
    id: '',
    companyName: 'Michael Brown',
    email: '<EMAIL>',
    password: 'premium123',
    role: Roles.TENANT,
    plan: Plans.PREMIUM,
    accessRight,
    accessToken: '',
  },
  {
    id: '',
    companyName: 'Emma Wilson',
    email: '<EMAIL>',
    password: 'dispatch456',
    role: Roles.DISPATCHER,
    plan: Plans.PREMIUM,
    accessRight,
    accessToken: '',
  },
];
