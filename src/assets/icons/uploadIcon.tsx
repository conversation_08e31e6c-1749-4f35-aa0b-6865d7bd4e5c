export const UploadIcon = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_9395_122544)">
        <path
          d="M10.8303 5.43766C10.6878 5.43766 10.5453 5.38516 10.4328 5.27266L8.91031 3.75016L7.38781 5.27266C7.17031 5.49016 6.81031 5.49016 6.59281 5.27266C6.37531 5.05516 6.37531 4.69516 6.59281 4.47766L8.51281 2.55766C8.73031 2.34016 9.09031 2.34016 9.30781 2.55766L11.2278 4.47766C11.4453 4.69516 11.4453 5.05516 11.2278 5.27266C11.1153 5.38516 10.9728 5.43766 10.8303 5.43766Z"
          fill="white"
        />
        <path
          d="M8.91016 11.1973C8.60266 11.1973 8.34766 10.9423 8.34766 10.6348V3.00732C8.34766 2.69982 8.60266 2.44482 8.91016 2.44482C9.21766 2.44482 9.47266 2.69982 9.47266 3.00732V10.6348C9.47266 10.9498 9.21766 11.1973 8.91016 11.1973Z"
          fill="white"
        />
        <path
          d="M9 15.5625C5.1375 15.5625 2.4375 12.8625 2.4375 9C2.4375 8.6925 2.6925 8.4375 3 8.4375C3.3075 8.4375 3.5625 8.6925 3.5625 9C3.5625 12.2025 5.7975 14.4375 9 14.4375C12.2025 14.4375 14.4375 12.2025 14.4375 9C14.4375 8.6925 14.6925 8.4375 15 8.4375C15.3075 8.4375 15.5625 8.6925 15.5625 9C15.5625 12.8625 12.8625 15.5625 9 15.5625Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_9395_122544">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
