export const SettingSelectedIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 13.125C8.275 13.125 6.875 11.725 6.875 10C6.875 8.275 8.275 6.875 10 6.875C11.725 6.875 13.125 8.275 13.125 10C13.125 11.725 11.725 13.125 10 13.125ZM10 8.125C8.96667 8.125 8.125 8.96667 8.125 10C8.125 11.0333 8.96667 11.875 10 11.875C11.0333 11.875 11.875 11.0333 11.875 10C11.875 8.96667 11.0333 8.125 10 8.125Z"
      fill="#0876A4"
    />
    <path
      d="M12.675 18.4917C12.5 18.4917 12.325 18.4667 12.15 18.425C11.6334 18.2833 11.2 17.9583 10.925 17.5L10.825 17.3333C10.3334 16.4833 9.65835 16.4833 9.16669 17.3333L9.07502 17.4917C8.80002 17.9583 8.36669 18.2917 7.85002 18.425C7.32502 18.5667 6.78335 18.4917 6.32502 18.2167L4.89169 17.3917C4.38335 17.1 4.01669 16.625 3.85835 16.05C3.70835 15.475 3.78335 14.8833 4.07502 14.375C4.31669 13.95 4.38335 13.5667 4.24169 13.325C4.10002 13.0833 3.74169 12.9417 3.25002 12.9417C2.03335 12.9417 1.04169 11.95 1.04169 10.7333V9.26668C1.04169 8.05001 2.03335 7.05835 3.25002 7.05835C3.74169 7.05835 4.10002 6.91668 4.24169 6.67502C4.38335 6.43335 4.32502 6.05002 4.07502 5.62502C3.78335 5.11668 3.70835 4.51668 3.85835 3.95001C4.00835 3.37501 4.37502 2.90001 4.89169 2.60835L6.33335 1.78335C7.27502 1.22501 8.51669 1.55001 9.08335 2.50835L9.18335 2.67501C9.67502 3.52501 10.35 3.52501 10.8417 2.67501L10.9334 2.51668C11.5 1.55001 12.7417 1.22501 13.6917 1.79168L15.125 2.61668C15.6334 2.90835 16 3.38335 16.1584 3.95835C16.3084 4.53335 16.2334 5.12502 15.9417 5.63335C15.7 6.05835 15.6334 6.44168 15.775 6.68335C15.9167 6.92502 16.275 7.06668 16.7667 7.06668C17.9834 7.06668 18.975 8.05835 18.975 9.27501V10.7417C18.975 11.9583 17.9834 12.95 16.7667 12.95C16.275 12.95 15.9167 13.0917 15.775 13.3333C15.6334 13.575 15.6917 13.9583 15.9417 14.3833C16.2334 14.8917 16.3167 15.4917 16.1584 16.0583C16.0084 16.6333 15.6417 17.1083 15.125 17.4L13.6834 18.225C13.3667 18.4 13.025 18.4917 12.675 18.4917ZM10 15.4083C10.7417 15.4083 11.4334 15.875 11.9084 16.7L12 16.8583C12.1 17.0333 12.2667 17.1583 12.4667 17.2083C12.6667 17.2583 12.8667 17.2333 13.0334 17.1333L14.475 16.3C14.6917 16.175 14.8584 15.9667 14.925 15.7167C14.9917 15.4667 14.9584 15.2083 14.8334 14.9917C14.3584 14.175 14.3 13.3333 14.6667 12.6917C15.0334 12.05 15.7917 11.6833 16.7417 11.6833C17.275 11.6833 17.7 11.2583 17.7 10.725V9.25835C17.7 8.73335 17.275 8.30001 16.7417 8.30001C15.7917 8.30001 15.0334 7.93335 14.6667 7.29168C14.3 6.65002 14.3584 5.80835 14.8334 4.99168C14.9584 4.77501 14.9917 4.51668 14.925 4.26668C14.8584 4.01668 14.7 3.81668 14.4834 3.68335L13.0417 2.85835C12.6834 2.64168 12.2084 2.76668 11.9917 3.13335L11.9 3.29168C11.425 4.11668 10.7334 4.58335 9.99169 4.58335C9.25002 4.58335 8.55835 4.11668 8.08335 3.29168L7.99169 3.12501C7.78335 2.77501 7.31669 2.65001 6.95835 2.85835L5.51669 3.69168C5.30002 3.81668 5.13335 4.02502 5.06669 4.27502C5.00002 4.52502 5.03335 4.78335 5.15835 5.00002C5.63335 5.81668 5.69169 6.65835 5.32502 7.30002C4.95835 7.94168 4.20002 8.30835 3.25002 8.30835C2.71669 8.30835 2.29169 8.73335 2.29169 9.26668V10.7333C2.29169 11.2583 2.71669 11.6917 3.25002 11.6917C4.20002 11.6917 4.95835 12.0583 5.32502 12.7C5.69169 13.3417 5.63335 14.1833 5.15835 15C5.03335 15.2167 5.00002 15.475 5.06669 15.725C5.13335 15.975 5.29169 16.175 5.50835 16.3083L6.95002 17.1333C7.12502 17.2417 7.33335 17.2667 7.52502 17.2167C7.72502 17.1667 7.89169 17.0333 8.00002 16.8583L8.09169 16.7C8.56669 15.8833 9.25835 15.4083 10 15.4083Z"
      fill="#0876A4"
    />
  </svg>
);
