export const PdfIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M4.66667 2.33317C4.40145 2.33317 4.1471 2.43853 3.95956 2.62606C3.77202 2.8136 3.66667 3.06795 3.66667 3.33317V12.6665C3.66667 12.9317 3.77202 13.1861 3.95956 13.3736C4.1471 13.5611 4.40145 13.6665 4.66667 13.6665H11.3333C11.5985 13.6665 11.8529 13.5611 12.0404 13.3736C12.228 13.1861 12.3333 12.9317 12.3333 12.6665V6.27591C12.3333 6.18753 12.2982 6.10272 12.2357 6.04024L8.6263 2.43087C8.56382 2.36837 8.47904 2.33321 8.39067 2.33317H4.66667ZM3.48816 2.15466C3.80072 1.8421 4.22464 1.6665 4.66667 1.6665H8.39067C8.65585 1.66656 8.91021 1.77194 9.0977 1.95947M9.0977 1.95947L12.707 5.56877C12.8945 5.75626 12.9999 6.01059 13 6.27577V12.6665C13 13.1085 12.8244 13.5325 12.5118 13.845C12.1993 14.1576 11.7754 14.3332 11.3333 14.3332H4.66667C4.22464 14.3332 3.80072 14.1576 3.48816 13.845C3.17559 13.5325 3 13.1085 3 12.6665V3.33317C3 2.89114 3.17559 2.46722 3.48816 2.15466"
      fill="#F04438"
      className="cell-icons"
    />
    <path
      d="M6.77231 5.33481C6.44727 5.32913 6.1667 5.65243 6.23546 5.97542C6.28171 6.46898 6.59995 6.86948 6.8483 7.27704C6.87621 7.42876 6.79618 7.57654 6.79083 7.72722C6.62891 8.61172 6.3819 9.49155 6.00187 10.3034C5.53163 10.5198 4.99918 10.7336 4.72413 11.2002C4.54957 11.5239 4.79536 11.9608 5.15614 11.995C5.49087 12.0408 5.7393 11.7539 5.92228 11.5136C6.11333 11.2551 6.23903 10.9473 6.39799 10.6694C7.36008 10.282 8.372 10.0112 9.40517 9.90844C9.82289 10.2074 10.2655 10.5601 10.8011 10.5814C11.1125 10.5768 11.3644 10.2746 11.3302 9.96757C11.3102 9.68808 11.0619 9.49208 10.8046 9.4396C10.3898 9.31463 9.94542 9.38307 9.52338 9.3861C8.71128 8.7372 7.96153 7.99059 7.37107 7.13625C7.4149 6.6169 7.5029 6.04242 7.23246 5.56994C7.12979 5.42093 6.95576 5.31884 6.77231 5.33481ZM6.78779 5.80647C6.87922 5.82409 6.88531 5.96029 6.91092 6.03314C6.93501 6.18159 6.93238 6.33306 6.93062 6.48297C6.82172 6.27943 6.68027 6.0668 6.70547 5.82547C6.73417 5.82435 6.75959 5.8098 6.78779 5.80647ZM7.29156 7.83176C7.77558 8.43351 8.31322 8.9877 8.89999 9.49029C8.12635 9.60827 7.35772 9.80592 6.62174 10.0725C6.90404 9.33465 7.11898 8.57239 7.26061 7.79445C7.27093 7.80689 7.28124 7.81932 7.29156 7.83176ZM10.3107 9.85494C10.4984 9.87304 10.715 9.86217 10.8651 9.99151C10.8792 10.1082 10.7364 10.1253 10.6604 10.083C10.5017 10.0301 10.3448 9.96345 10.2101 9.86198C10.2436 9.8599 10.2771 9.8556 10.3107 9.85494ZM5.62122 11.0826C5.53198 11.2486 5.40209 11.4051 5.26661 11.5226C5.22152 11.5956 5.09782 11.4607 5.15474 11.4037C5.27228 11.2197 5.47748 11.1222 5.65922 11.0122C5.64655 11.0357 5.63389 11.0592 5.62122 11.0826Z"
      fill="#F04438"
      className="cell-icons"
    />
  </svg>
);
