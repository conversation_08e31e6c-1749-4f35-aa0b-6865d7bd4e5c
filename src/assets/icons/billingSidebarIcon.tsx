export const BillingSidebarIcon = ({ bool }: { bool?: boolean }) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill={bool ? '#0876A4' : '#20363F'}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.6327 15.613H5.36602C2.04935 15.613 1.04102 14.6047 1.04102 11.288V6.00469C1.04102 2.68802 2.04935 1.67969 5.36602 1.67969H14.6243C17.941 1.67969 18.9494 2.68802 18.9494 6.00469V11.2797C18.9577 14.6047 17.9493 15.613 14.6327 15.613ZM5.36602 2.92969C2.74935 2.92969 2.29102 3.38802 2.29102 6.00469V11.2797C2.29102 13.8964 2.74935 14.3547 5.36602 14.3547H14.6243C17.241 14.3547 17.6994 13.8964 17.6994 11.2797V6.00469C17.6994 3.38802 17.241 2.92969 14.6243 2.92969H5.36602Z"
      fill={bool ? '#0876A4' : '#20363F'}
    />
    <path
      d="M10.8557 12.3047H9.32152C8.31907 12.3047 7.50611 11.3499 7.50611 10.1737C7.50611 9.89002 7.71394 9.65478 7.96455 9.65478C8.21516 9.65478 8.42298 9.89002 8.42298 10.1737C8.42298 10.7756 8.82641 11.2669 9.32152 11.2669H10.8557C11.2531 11.2669 11.5831 10.8656 11.5831 10.3813C11.5831 9.77932 11.3936 9.6617 11.0819 9.53716L8.61858 8.55469C8.14181 8.36788 7.5 7.96659 7.5 6.72812C7.5 5.66262 8.23961 4.80469 9.14425 4.80469H10.6785C11.6809 4.80469 12.4939 5.75948 12.4939 6.93568C12.4939 7.21936 12.2861 7.4546 12.0355 7.4546C11.7848 7.4546 11.577 7.21936 11.577 6.93568C11.577 6.33375 11.1736 5.84251 10.6785 5.84251H9.14425C8.74694 5.84251 8.41687 6.2438 8.41687 6.72812C8.41687 7.33006 8.60636 7.44768 8.91809 7.57222L11.3814 8.55469C11.8582 8.7415 12.5 9.14279 12.5 10.3813C12.4939 11.4398 11.7604 12.3047 10.8557 12.3047Z"
      fill={bool ? '#0876A4' : '#20363F'}
    />
    <path
      d="M10.0001 13.1374C9.72678 13.1374 9.5 12.9065 9.5 12.6281V4.47996C9.5 4.20157 9.72678 3.9707 10.0001 3.9707C10.2734 3.9707 10.5001 4.20157 10.5001 4.47996V12.6281C10.5001 12.9065 10.2734 13.1374 10.0001 13.1374Z"
      fill={bool ? '#0876A4' : '#20363F'}
    />
    <path
      d="M10 19.5956C9.65833 19.5956 9.375 19.3123 9.375 18.9706V14.9873C9.375 14.6456 9.65833 14.3623 10 14.3623C10.3417 14.3623 10.625 14.6456 10.625 14.9873V18.9706C10.625 19.3123 10.3417 19.5956 10 19.5956Z"
      fill={bool ? '#0876A4' : '#20363F'}
    />
    <path
      d="M13.75 19.5957H6.25C5.90833 19.5957 5.625 19.3124 5.625 18.9707C5.625 18.629 5.90833 18.3457 6.25 18.3457H13.75C14.0917 18.3457 14.375 18.629 14.375 18.9707C14.375 19.3124 14.0917 19.5957 13.75 19.5957Z"
      fill={bool ? '#0876A4' : '#20363F'}
    />
  </svg>
);
