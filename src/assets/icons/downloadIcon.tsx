export const DownloadIcon = ({ bool }: { bool: boolean }) => (
  <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M9.90078 12.4916C9.74245 12.4916 9.58411 12.4333 9.45911 12.3083L7.32578 10.1749C7.08411 9.93327 7.08411 9.53327 7.32578 9.2916C7.56745 9.04993 7.96745 9.04993 8.20911 9.2916L9.90078 10.9833L11.5924 9.2916C11.8341 9.04993 12.2341 9.04993 12.4758 9.2916C12.7174 9.53327 12.7174 9.93327 12.4758 10.1749L10.3424 12.3083C10.2174 12.4333 10.0591 12.4916 9.90078 12.4916Z"
      fill={bool ? '#088DC1' : '#20363F'}
      className="cell-icons"
    />
    <path
      d="M9.90039 12.4335C9.55872 12.4335 9.27539 12.1502 9.27539 11.8085V3.3335C9.27539 2.99183 9.55872 2.7085 9.90039 2.7085C10.2421 2.7085 10.5254 2.99183 10.5254 3.3335V11.8085C10.5254 12.1502 10.2421 12.4335 9.90039 12.4335Z"
      fill={bool ? '#088DC1' : '#20363F'}
      className="cell-icons"
    />
    <path
      d="M10.0007 17.4416C5.70898 17.4416 2.70898 14.4416 2.70898 10.1499C2.70898 9.80824 2.99232 9.5249 3.33398 9.5249C3.67565 9.5249 3.95898 9.80824 3.95898 10.1499C3.95898 13.7082 6.44232 16.1916 10.0007 16.1916C13.559 16.1916 16.0423 13.7082 16.0423 10.1499C16.0423 9.80824 16.3257 9.5249 16.6673 9.5249C17.009 9.5249 17.2923 9.80824 17.2923 10.1499C17.2923 14.4416 14.2923 17.4416 10.0007 17.4416Z"
      fill={bool ? '#088DC1' : '#20363F'}
      className="cell-icons"
    />
  </svg>
);
