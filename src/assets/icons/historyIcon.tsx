export const HistoryIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="cell-icons"
  >
    <path
      d="M9.99935 2.38406C7.94761 2.38553 5.96888 3.14561 4.44379 4.51809V3.07851C4.44379 2.89433 4.37063 2.7177 4.2404 2.58746C4.11016 2.45723 3.93353 2.38406 3.74935 2.38406C3.56517 2.38406 3.38854 2.45723 3.2583 2.58746C3.12807 2.7177 3.0549 2.89433 3.0549 3.07851V5.16184C3.0549 5.71438 3.2744 6.24428 3.6651 6.63498C4.0558 7.02568 4.5857 7.24517 5.13824 7.24517H7.22157C7.40575 7.24517 7.58238 7.17201 7.71262 7.04178C7.84285 6.91154 7.91601 6.73491 7.91601 6.55073C7.91601 6.36655 7.84285 6.18992 7.71262 6.05968C7.58238 5.92945 7.40575 5.85629 7.22157 5.85629H5.13824C5.11482 5.85281 5.09163 5.84794 5.06879 5.8417C6.20041 4.702 7.69249 3.99037 9.29036 3.82828C10.8882 3.66619 12.4928 4.06368 13.8303 4.95292C15.1677 5.84216 16.155 7.16802 16.6238 8.70417C17.0925 10.2403 17.0136 11.8915 16.4004 13.376C15.7873 14.8604 14.678 16.086 13.2618 16.8436C11.8457 17.6012 10.2105 17.8438 8.63533 17.53C7.06021 17.2163 5.64281 16.3655 4.62508 15.1231C3.60735 13.8806 3.05238 12.3235 3.0549 10.7174C3.0549 10.5332 2.98174 10.3566 2.85151 10.2263C2.72127 10.0961 2.54464 10.023 2.36046 10.023C2.17628 10.023 1.99965 10.0961 1.86941 10.2263C1.73918 10.3566 1.66602 10.5332 1.66602 10.7174C1.66602 12.3656 2.15476 13.9767 3.07044 15.3471C3.98611 16.7176 5.2876 17.7857 6.81032 18.4164C8.33304 19.0471 10.0086 19.2121 11.6251 18.8906C13.2416 18.5691 14.7265 17.7754 15.8919 16.61C17.0573 15.4445 17.851 13.9597 18.1726 12.3431C18.4941 10.7266 18.3291 9.05108 17.6983 7.52837C17.0676 6.00565 15.9995 4.70416 14.6291 3.78848C13.2587 2.87281 11.6475 2.38406 9.99935 2.38406Z"
      fill="#20363F"
      className="cell-icons"
    />
    <path
      d="M9.83432 6.55075C9.65708 6.55075 9.48709 6.62108 9.36176 6.74625C9.23643 6.87143 9.16602 7.04121 9.16602 7.21824V10.5557C9.16605 10.7327 9.23649 10.9024 9.36183 11.0276L11.3668 13.03C11.4928 13.1516 11.6616 13.2189 11.8368 13.2174C12.0121 13.2159 12.1797 13.1457 12.3036 13.0219C12.4275 12.8982 12.4978 12.7307 12.4993 12.5557C12.5008 12.3807 12.4335 12.2121 12.3117 12.0862L10.5026 10.2793V7.21824C10.5026 7.04121 10.4322 6.87143 10.3069 6.74625C10.1816 6.62108 10.0116 6.55075 9.83432 6.55075Z"
      fill="#20363F"
      className="cell-icons"
    />
  </svg>
);
