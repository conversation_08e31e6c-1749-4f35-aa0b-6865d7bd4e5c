export const AssignToIcon = ({ bool }: { bool: boolean }) => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M11.0625 8.3125H6.9375C6.92444 8.3125 6.90878 8.3073 6.89449 8.29301C6.8802 8.27872 6.875 8.26306 6.875 8.25C6.875 8.23694 6.8802 8.22128 6.89449 8.20699C6.90878 8.1927 6.92444 8.1875 6.9375 8.1875H11.0625C11.0756 8.1875 11.0912 8.1927 11.1055 8.20699C11.1198 8.22128 11.125 8.23694 11.125 8.25C11.125 8.26306 11.1198 8.27872 11.1055 8.29301C11.0912 8.3073 11.0756 8.3125 11.0625 8.3125Z"
      stroke={bool ? '#088DC1' : '#20363F'}
    />
    <path
      d="M9 10.375C8.98694 10.375 8.97128 10.3698 8.95699 10.3555C8.9427 10.3412 8.9375 10.3256 8.9375 10.3125V6.1875C8.9375 6.17444 8.9427 6.15878 8.95699 6.14449C8.97128 6.1302 8.98694 6.125 9 6.125C9.01306 6.125 9.02872 6.1302 9.04301 6.14449C9.0573 6.15878 9.0625 6.17444 9.0625 6.1875V10.3125C9.0625 10.3256 9.0573 10.3412 9.04301 10.3555C9.02872 10.3698 9.01306 10.375 9 10.375Z"
      fill="#20363F"
      stroke={bool ? '#088DC1' : '#20363F'}
    />
    <path
      d="M2.65425 6.35874L2.65437 6.35818C3.42292 2.97243 6.37494 1.4375 8.99928 1.4375H8.99928H8.99929H8.99929H8.99929H8.9993H8.9993H8.99931H8.99931H8.99931H8.99932H8.99932H8.99933H8.99933H8.99934H8.99934H8.99934H8.99935H8.99935H8.99936H8.99936H8.99937H8.99937H8.99937H8.99938H8.99938H8.99939H8.9994H8.9994H8.99941H8.99941H8.99942H8.99942H8.99943H8.99944H8.99944H8.99945H8.99946H8.99946H8.99947H8.99947H8.99948H8.99948H8.99949H8.99949H8.99949H8.9995H8.9995H8.99951H8.99951H8.99952H8.99952H8.99952H8.99953H8.99953H8.99954H8.99954H8.99955H8.99955H8.99956H8.99956H8.99957H8.99957H8.99958H8.99958H8.99959H8.99959H8.9996H8.9996H8.99961H8.99961H8.99962H8.99963H8.99963H8.99964H8.99964H8.99965H8.99966H8.99966H8.99967H8.99967H8.99968H8.99969H8.99969H8.9997H8.99971H8.99971H8.99972H8.99972H8.99973H8.99974H8.99975H8.99975H8.99976H8.99977H8.99977H8.99978H8.99979H8.9998H8.9998H8.99981H8.99982H8.99983H8.99983H8.99984H8.99985H8.99986H8.99987H8.99987H8.99988H8.99989H8.9999H8.99991H8.99991H8.99992H8.99993H8.99994H8.99995H8.99996H8.99997H8.99998H8.99998H8.99999H9H9.00001H9.00002H9.00003H9.00004H9.00005H9.00006H9.00007H9.00008H9.00009H9.0001H9.00011H9.00012H9.00013H9.00014H9.00015H9.00016H9.00017H9.00018H9.00019H9.0002H9.00022H9.00023H9.00024H9.00025H9.00026H9.00027H9.00028H9.00029H9.00031H9.00032H9.00033H9.00034H9.00035H9.00037H9.00038H9.00039H9.0004H9.00041H9.00043H9.00044H9.00045H9.00047H9.00048H9.00049H9.0005H9.00052H9.00053H9.00054H9.00056H9.00057H9.00059H9.0006H9.00061H9.00063H9.00064H9.00066H9.00067H9.00068H9.0007H9.00071H9.00073H9.00074H9.00076H9.00077H9.00079H9.0008H9.00082H9.00083H9.00085H9.00086H9.00088H9.0009H9.00091H9.00093H9.00094H9.00096H9.00098H9.00099H9.00101H9.00102H9.00104H9.00106H9.00107H9.00109H9.00111H9.00113H9.00114H9.00116H9.00118H9.0012H9.00121H9.00123H9.00125H9.00127H9.00129H9.0013H9.00132H9.00134H9.00136H9.00138H9.0014H9.00142H9.00144H9.00145H9.00147H9.00149H9.00151H9.00153H9.00155H9.00157H9.00159H9.00161H9.00163H9.00165H9.00167H9.00169H9.00172H9.00174H9.00176H9.00178H9.0018H9.00182H9.00184H9.00186H9.00188H9.00191H9.00193H9.00195H9.00197H9.002H9.00202H9.00204H9.00206H9.00209H9.00211H9.00213H9.00215H9.00218H9.0022H9.00222H9.00225H9.00227H9.0023H9.00232H9.00234H9.00237H9.00239H9.00242H9.00244H9.00247H9.00249H9.00252H9.00254H9.00257H9.00259H9.00262H9.00264H9.00267H9.0027H9.00272H9.00275H9.00277H9.0028H9.00283H9.00285H9.00288H9.00291H9.00294H9.00296H9.00299H9.00302H9.00305H9.00307H9.0031H9.00313H9.00316H9.00319H9.00321H9.00324H9.00327H9.0033H9.00333H9.00336H9.00339H9.00342H9.00345H9.00348H9.00351H9.00354H9.00357H9.0036H9.00363H9.00366H9.00369H9.00372H9.00375H9.00378H9.00381H9.00385H9.00388H9.00391H9.00394H9.00397H9.00401H9.00404H9.00407H9.0041H9.00414H9.00417H9.0042H9.00424H9.00427H9.0043H9.00434H9.00437H9.0044H9.00444H9.00447H9.00451H9.00454H9.00458H9.00461H9.00465H9.00468H9.00472H9.00475H9.00479H9.00482H9.00486H9.0049H9.00493H9.00497H9.00501H9.00504H9.00508H9.00512H9.00515H9.00519H9.00523H9.00527H9.0053H9.00534H9.00538H9.00542H9.00546H9.0055H9.00554H9.00557H9.00561H9.00565H9.00569H9.00573H9.00577H9.00581H9.00585H9.00589H9.00593H9.00597H9.00601H9.00606H9.0061H9.00614H9.00618H9.00622H9.00626H9.0063H9.00635H9.00639H9.00643H9.00647H9.00652H9.00656H9.0066H9.00665H9.00669H9.00673H9.00678C11.6308 1.4375 14.5829 2.97209 15.3516 6.36538C16.2055 10.1377 13.9334 13.3552 11.7504 15.4569C10.9756 16.198 9.98304 16.57 8.99928 16.57C8.01551 16.57 7.02295 16.198 6.24814 15.4569C4.06542 13.3554 1.79346 10.1308 2.65425 6.35874ZM15.2321 6.38522L15.2316 6.38322C14.4128 2.81295 11.3305 1.5625 8.99928 1.5625C6.66772 1.5625 3.59279 2.81336 2.78181 6.38377C1.90903 10.1913 4.30918 13.4102 6.34267 15.3604C7.83376 16.7991 10.1725 16.799 11.6635 15.3602C13.6894 13.4102 16.0888 10.192 15.2321 6.38522Z"
      fill="#20363F"
      stroke={bool ? '#088DC1' : '#20363F'}
    />
  </svg>
);
