export const ColumnMergeIcon = ({ bool }: { bool: boolean }) => (
  <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M7.58148 20.8542H3.87815C2.03565 20.8542 1.14648 20.0017 1.14648 18.2417V3.75834C1.14648 1.99834 2.04482 1.14584 3.87815 1.14584H7.58148C9.42398 1.14584 10.3132 1.99834 10.3132 3.75834V18.2417C10.3132 20.0017 9.41482 20.8542 7.58148 20.8542ZM3.87815 2.52084C2.71398 2.52084 2.52148 2.83251 2.52148 3.75834V18.2417C2.52148 19.1675 2.71398 19.4792 3.87815 19.4792H7.58148C8.74565 19.4792 8.93815 19.1675 8.93815 18.2417V3.75834C8.93815 2.83251 8.74565 2.52084 7.58148 2.52084H3.87815Z"
      fill={bool ? '#088DC1' : '#20363F'}
    />
    <path
      d="M18.1225 12.6042H14.4192C12.5767 12.6042 11.6875 11.7517 11.6875 9.99168V3.75834C11.6875 1.99834 12.5858 1.14584 14.4192 1.14584H18.1225C19.965 1.14584 20.8542 1.99834 20.8542 3.75834V9.99168C20.8542 11.7517 19.9558 12.6042 18.1225 12.6042ZM14.4192 2.52084C13.255 2.52084 13.0625 2.83251 13.0625 3.75834V9.99168C13.0625 10.9175 13.255 11.2292 14.4192 11.2292H18.1225C19.2867 11.2292 19.4792 10.9175 19.4792 9.99168V3.75834C19.4792 2.83251 19.2867 2.52084 18.1225 2.52084H14.4192Z"
      fill={bool ? '#088DC1' : '#20363F'}
    />
    <path
      d="M18.1225 20.8542H14.4192C12.5767 20.8542 11.6875 20.0017 11.6875 18.2417V16.5917C11.6875 14.8317 12.5858 13.9792 14.4192 13.9792H18.1225C19.965 13.9792 20.8542 14.8317 20.8542 16.5917V18.2417C20.8542 20.0017 19.9558 20.8542 18.1225 20.8542ZM14.4192 15.3542C13.255 15.3542 13.0625 15.6658 13.0625 16.5917V18.2417C13.0625 19.1675 13.255 19.4792 14.4192 19.4792H18.1225C19.2867 19.4792 19.4792 19.1675 19.4792 18.2417V16.5917C19.4792 15.6658 19.2867 15.3542 18.1225 15.3542H14.4192Z"
      fill={bool ? '#088DC1' : '#20363F'}
    />
  </svg>
);
