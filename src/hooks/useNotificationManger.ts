import React from 'react';
import { useConfig } from '@/contexts/ConfigContext';
import NotificationManager from '@/lib/NotificationManager';
import ConfigManager from '@/lib/ConfigManager';

/**
 * Hook to create and use a NotificationManager instance
 * @returns {NotificationManager} An instance of NotificationManager
 */

export const useNotificationManager = (): NotificationManager => {
  const { config } = useConfig();

  return React.useMemo(
    () =>
      new NotificationManager({
        duration: config.notification?.duration || 5,
        placement: config.notification?.placement || 'topRight',
        pauseOnHover: config.notification?.pauseOnHover || false,
        className: config.notification?.className || 'notify-wrapper',
      }),
    [
      config.notification?.duration,
      config.notification?.placement,
      config.notification?.pauseOnHover,
      config.notification?.className,
    ]
  );
};

export const getNotificationManager = () => {
  const config = ConfigManager.getAll();

  return new NotificationManager({
    duration: config.notification?.duration || 5,
    placement: config.notification?.placement || 'topRight',
    pauseOnHover: config.notification?.pauseOnHover || false,
    className: config.notification?.className || 'notify-wrapper',
  });
};

const notificationManagerInstance = getNotificationManager();

export default notificationManagerInstance;
