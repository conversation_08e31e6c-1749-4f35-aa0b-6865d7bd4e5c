import { useCallback, useRef } from 'react';

const useThrottle = <T extends (...args: any[]) => void>(callback: T, delay: number) => {
  const lastCall = useRef<number>(0);

  return useCallback(
    (...args: Parameters<T>): any => {
      const now = Date.now();
      if (now - lastCall.current >= delay) {
        lastCall.current = now;
        callback(...args);
      }
    },
    [callback, delay]
  );
};

export default useThrottle;
