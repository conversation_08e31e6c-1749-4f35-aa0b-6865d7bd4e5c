import { useEffect } from 'react';

export const useGooglePlaceDropdownFix = (isOpen: boolean, autoCompleteId: string) => {
  useEffect(() => {
    const modalBody = document.querySelector('.custom-modal-body') as HTMLDivElement;

    const onScrollHandler = () => {
      const autoComplete = document.getElementById(autoCompleteId);
      const pacContainer = document.querySelector('.pac-container') as HTMLElement;

      if (pacContainer && autoComplete) {
        const autoComRect = autoComplete.getBoundingClientRect();
        pacContainer.style.top = `${autoComRect.y + autoComRect.height}px`;
      }
    };

    if (isOpen) {
      modalBody?.addEventListener('scroll', onScrollHandler);
    }

    return () => {
      if (isOpen) {
        modalBody?.removeEventListener('scroll', onScrollHandler);
        const pacContainer = document.querySelector('.pac-container') as HTMLElement;
        pacContainer?.remove();
      }
    };
  }, [isOpen, autoCompleteId]);
};
