import { modalSuccess, modalError, modalWarningIcon } from '@/assets';
import { ModalConfigsType } from '@/components/common/customAlert/CustomAlert';

export const alertPopupIcons: ModalConfigsType = {
  success: {
    icon: <img src={modalSuccess} alt="Success" />,
    title: 'Success',
    message: 'Operation succeed.',
    firstButtonTitle: 'Cancel',
    secondButtonTitle: 'Ok',
    firstButtonFunction: () => {},
    secondButtonFunction: () => {},
    firstButtonClass: 'primary-button-class',
    secondButtonClass: 'second-button-class',
  },
  error: {
    icon: <img src={modalError} alt="Error" />,
    title: 'Error',
    message: 'Error occurred.',
    firstButtonTitle: 'Cancel',
    secondButtonTitle: 'Ok',
    firstButtonFunction: () => {},
    secondButtonFunction: () => {},
    firstButtonClass: 'primary-button-error',
    secondButtonClass: 'second-button-error',
  },
  warning: {
    icon: <img src={modalWarningIcon} alt="Warning" />,
    title: 'Warning',
    message: 'This action cannot be undone.',
    firstButtonTitle: 'Cancel',
    secondButtonTitle: 'Do it anyway',
    firstButtonFunction: () => {},
    secondButtonFunction: () => {},
    firstButtonClass: 'primary-button-class',
    secondButtonClass: 'second-button-class',
  },
};
