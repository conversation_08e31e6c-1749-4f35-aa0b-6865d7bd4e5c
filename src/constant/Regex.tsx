export const formErrorRegex = {
  NoMultipleWhiteSpaces: /^(?!.*\s{2,}).*$/,
  ValidEmailOrNot: /^([a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})$/,
  CheckIfValidURL: /^([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/,
  NoSpecialCharacters: /^[a-zA-Z0-9. ]*$/,
  PostalCode: /^[A-Za-z0-9\- ]{2,7}$/,
  onlyDigits: /^\d$/,
  DIGITS_WITH_DECIMAL: /^\d{1,10}(\.\d{1,2})?$/,
  FSA_REGEX: /^[A-Z0-9]{3}$/,
  NAME_PATTERN: /^[A-Za-z0-9 ]+$/,
  NO_SPACES: /^\S*$/,
  ALLOW_NUMBER_AND_SYMBOL: /[0-9!@#$%^&*(),.?":{}|<>]/,
  ALLOW_UPPERCASE_ALPHABETS: /[A-Z]/,
  ALLOW_LOWERCASE_ALPHABETS: /[a-z]/,
};
