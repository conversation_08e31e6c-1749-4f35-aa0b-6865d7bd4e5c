import { translator } from '@/i18n/languageLoader';

export const optionsForPrefix = [
  {
    value: 'USA',
    label: translator('common.usa'),
    mask: '(000)000-0000',
    length: 10,
    geoCountryCode: 'Usa',
  },
  {
    value: 'IND',
    label: translator('common.ind'),
    mask: '00000-00000',
    length: 10,
    geoCountryCode: 'India',
  },
  {
    value: 'CAN',
    label: translator('common.can'),
    mask: '(000)000-0000',
    length: 10,
    geoCountryCode: 'Canada',
  },
];
