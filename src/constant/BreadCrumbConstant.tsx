import { ROUTES } from './RoutesConstant';

export const breadCrumbsPathForPages = {
  dashBoard: [{ name: 'Dashboard', path: '/dashboard' }],
  customers: [
    { name: 'Customers', path: ROUTES.CUSTOMER.CUSTOMER_ADD },
    { name: 'General', path: ROUTES.CUSTOMER.CUSTOMER_TAB },
    { name: 'Contacts', path: ROUTES.CUSTOMER.CUSTOMER_TAB.replace(':tab', 'contacts') },
    { name: 'Address', path: ROUTES.CUSTOMER.CUSTOMER_TAB.replace(':tab', 'address') },
    { name: 'Services', path: ROUTES.CUSTOMER.CUSTOMER_TAB.replace(':tab', 'services') },
    { name: 'Settings', path: ROUTES.CUSTOMER.CUSTOMER_TAB.replace(':tab', 'settings') },
  ],
  vehicle: [
    { name: 'Vehicle', path: ROUTES.LOGISTIC.LOGISTICS_VEHICLE_ADD },
    { name: 'General', path: ROUTES.CUSTOMER.CUSTOMER_TAB },
    {
      name: 'Time Clock Session',
      path: ROUTES.LOGISTIC.LOGISTICS_VEHICLE_EDIT.replace(':tab', 'timeClockSession'),
    },
  ],
  PriceSet: [
    { name: 'Price set', path: ROUTES.PRICES.PRICES_PRICE_SETS_ADD },
    { name: 'General', path: ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':tab', 'general') },
    { name: 'Schedule', path: ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':tab', 'schedule') },
    {
      name: 'Base price by zone',
      path: ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':tab', 'basePriceByZone'),
    },
    {
      name: 'Price modifiers',
      path: ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':tab', 'priceModifiers'),
    },
    { name: 'Customers', path: ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':tab', 'customers') },
    { name: 'History', path: ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':tab', 'customers') },
  ],
  PriceModifier: [
    { name: 'Prices Modifiers', path: ROUTES.PRICES.PRICES_PRICE_MODIFIER_ADD },
    { name: 'General', path: ROUTES.PRICES.PRICES_PRICE_MODIFIER_EDIT.replace(':tab', 'general') },
  ],
  Orders: [
    { name: 'Orders', path: ROUTES.LOGISTIC.LOGISTICS_ORDERS },
    { name: 'General', path: ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION },
    {
      name: 'Prices Breakdown',
      path: ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':tab', 'pricesBreakdown'),
    },
    {
      name: 'Packages',
      path: ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':tab', 'packages'),
    },
    {
      name: 'Attachments',
      path: ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':tab', 'attachments'),
    },
    {
      name: 'History',
      path: ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':tab', 'history'),
    },
  ],
};
