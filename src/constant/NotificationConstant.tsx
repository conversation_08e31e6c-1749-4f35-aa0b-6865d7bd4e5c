import {
  errorNotifyIconSvg,
  infoNotifyIconSvg,
  successNotifyIconSvg,
  warnNotifyIconSvg,
} from '../assets';
import { TNotificationType } from '../types/NotificationTypes';

// Notification icons for all variants

export const notificationIcons: Record<TNotificationType, React.ReactNode> = {
  success: <img src={successNotifyIconSvg} alt="Success" />,
  error: <img src={errorNotifyIconSvg} alt="Error" />,
  info: <img src={infoNotifyIconSvg} alt="Info" />,
  warning: <img src={warnNotifyIconSvg} alt="Warning" />,
};
