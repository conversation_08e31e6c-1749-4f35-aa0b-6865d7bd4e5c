import React, { createContext, useCallback, useEffect, useState } from 'react';
import {
  LanguageStrings,
  SupportedLanguage,
  LanguagePath,
  TranslationVariables,
  getCurrentLanguage,
  loadLanguage,
  setCurrentLanguage,
  createTranslator,
} from '@/i18n/languageLoader';

/**
 * Represents the shape of the language context provided by the LanguageProvider.
 *
 * @interface LanguageContextType
 * @property {LanguageStrings} strings - The currently loaded language strings.
 * @property {(lang: SupportedLanguage) => void} setLanguage - Function to change the current language.
 * @property {(path: LanguagePath, vars?: TranslationVariables) => string} t - Translation function.
 * @property {SupportedLanguage} currentLanguage - The current language code.
 *
 * @example
 * // Using the context in a component
 * const { t, setLanguage, currentLanguage } = useContext(LanguageContext);
 *
 * // Translate a string
 * const welcomeMessage = t('common.welcome', { name: '<PERSON>' });
 *
 * // Change the language
 * setLanguage('FR');
 *
 * // Check the current language
 * console.log(currentLanguage); // 'EN' or 'FR'
 */
export interface LanguageContextType {
  strings: LanguageStrings;
  setLanguage: (lang: SupportedLanguage) => void;
  t: (path: LanguagePath, vars?: TranslationVariables) => string;
  currentLanguage: SupportedLanguage;
}

/**
 * React Context for the language provider.
 * Use this with the useContext hook to access language-related functionality.
 *
 * @example
 * import { useContext } from 'react';
 * import { LanguageContext } from './LanguageProvider';
 *
 * function MyComponent() {
 *   const { t } = useContext(LanguageContext);
 *   return <h1>{t('myComponent.title')}</h1>;
 * }
 */
export const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

/**
 * LanguageProvider component that manages language state and provides translation functionality.
 *
 * This component should be placed near the root of your React component tree to provide
 * language support throughout your application.
 *
 * @component
 * @example
 * // In your main App component or a high-level component
 * import { LanguageProvider } from './LanguageProvider';
 *
 * function App() {
 *   return (
 *     <LanguageProvider>
 *       <YourAppComponents />
 *     </LanguageProvider>
 *   );
 * }
 */
export const LanguageProvider: React.FC<React.PropsWithChildren<object>> = ({ children }) => {
  // State to hold the current language
  const [currentLanguage, setCurrentLang] = useState<SupportedLanguage>(getCurrentLanguage());

  // State to hold the current language strings
  const [strings, setStrings] = useState<LanguageStrings>(() => loadLanguage(currentLanguage));

  /**
   * Function to change the current language.
   * This updates the language strings, sets the new language in local storage,
   * and updates the currentLanguage state.
   *
   * @param {SupportedLanguage} lang - The language code to set
   *
   * @example
   * const { setLanguage } = useContext(LanguageContext);
   *
   * // Change language to French
   * setLanguage('FR');
   */
  const setLanguage = useCallback((lang: SupportedLanguage) => {
    setStrings(loadLanguage(lang));
    setCurrentLanguage(lang);
    setCurrentLang(lang);
  }, []);

  /**
   * Translation function that retrieves a translated string and interpolates variables if provided.
   *
   * @param {LanguagePath} path - The dot-notated path to the desired string in the language object
   * @param {TranslationVariables} [vars] - Optional variables to interpolate into the string
   * @returns {string} The translated and interpolated string
   *
   * @example
   * const { t } = useContext(LanguageContext);
   *
   * // Simple translation
   * const simpleMessage = t('common.hello');
   *
   * // Translation with variables
   * const welcomeMessage = t('common.welcome', { name: 'Alice', count: 3 });
   *
   * // Nested path
   * const deeplyNestedMessage = t('pages.home.header.title');
   */
  const t = useCallback(
    (path: LanguagePath, vars?: TranslationVariables) => createTranslator(strings)(path, vars),
    [strings]
  );

  // Effect to load the saved language on component mount
  useEffect(() => {
    const savedLanguage = getCurrentLanguage();
    setLanguage(savedLanguage);
  }, [setLanguage]);

  // Provide the language context to child components
  return (
    <LanguageContext.Provider value={{ strings, setLanguage, t, currentLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
};
