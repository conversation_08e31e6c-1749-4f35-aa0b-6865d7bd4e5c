import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { useLanguage } from '@/hooks/useLanguage';
import { NavigationContextType } from '@/types/PreventExitTypes';
import React, { createContext, useState, ReactNode, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

export const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

/**
 * NavigationProvider component that manages navigation with exit blocking functionality.
 * It provides a context to control and handle navigation blocking with custom alerts.
 *
 * Context Value:
 *   - `isBlocked` (boolean): Indicates whether navigation is currently blocked.
 *   - `setIsBlocked` (function): Function to toggle the navigation blocking state.
 *   - `navigate` (function): Custom navigation handler.
 */

export const NavigationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isBlocked, setIsBlocked] = useState(false);
  const isBlockedRef = useRef(isBlocked);

  useEffect(() => {
    isBlockedRef.current = isBlocked;
  }, [isBlocked]);

  const navigate = useNavigate();
  const { t } = useLanguage();

  const navigateHandler = (path: string) => {
    if (isBlockedRef.current) {
      customAlert.warning({
        title: t('common.alert.areYouSure'),
        message: t('common.alert.preventExist'),
        firstButtonTitle: t('common.leave'),
        secondButtonTitle: t('common.stay'),
        firstButtonFunction: () => {
          navigate(path);
          customAlert.destroy();
          setIsBlocked(false);
        },
        secondButtonFunction: () => customAlert.destroy(),
      });
      return;
    } else {
      navigate(path);
    }
  };

  return (
    <NavigationContext.Provider value={{ isBlocked, setIsBlocked, navigate: navigateHandler }}>
      {children}
    </NavigationContext.Provider>
  );
};
