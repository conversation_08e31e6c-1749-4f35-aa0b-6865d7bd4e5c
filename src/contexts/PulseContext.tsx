import { Events, Pulse } from '@devforgets/pulse';
import { AppEvents } from '@customTypes/AppEvents.ts';

type EventName = keyof AppEvents;

export function emit<E extends EventName>(eventName: E, data: AppEvents[E]) {
  Events.emit<AppEvents, E>(eventName, data);
}

export function on<E extends EventName>(eventName: E, callback: (data: AppEvents[E]) => void) {
  Events.on<AppEvents, E>(eventName, callback);
}

export const { PulseProvider, useEvent, emitEvent } = Pulse.createContext<AppEvents>();
