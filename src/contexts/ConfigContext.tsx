import React, { createContext, useState, useContext, useEffect } from 'react';
import ConfigManager from '@/lib/ConfigManager';
import { ConfigSchema } from '@customTypes/ConfigSchema';

interface ConfigContextType {
  config: Partial<ConfigSchema>;
  updateConfig: (newConfig: Partial<ConfigSchema>) => void;
}

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

export const ConfigProvider: React.FC<React.PropsWithChildren<object>> = ({ children }) => {
  const [config, setConfig] = useState(ConfigManager.getAll());

  useEffect(() => {
    const loadConfig = async () => {
      await ConfigManager.initialize();
      setConfig(ConfigManager.getAll());
    };
    loadConfig();
  }, []);

  const updateConfig = (newConfig: Partial<ConfigSchema>) => {
    ConfigManager.updateConfig(newConfig);
    setConfig(ConfigManager.getAll());
  };

  return (
    <ConfigContext.Provider value={{ config, updateConfig }}>{children}</ConfigContext.Provider>
  );
};

export const useConfig = (): ConfigContextType => {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
};
