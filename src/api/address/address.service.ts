import { AxiosInstance } from 'axios';
import { BaseService } from '@api/core/base-service.ts';
import { GetAddressDto, CreateAddressDto } from './address.types';

/**
 * Service class for managing Address
 */
export class AddressService extends BaseService<GetAddressDto, CreateAddressDto, CreateAddressDto> {
  /**
   * Creates a new instance of Address
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/address');
  }
}
