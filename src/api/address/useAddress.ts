import { createEntityHooks } from '@api/core/react-query-hooks.ts';
import { AddressService } from './address.service';
import { CreateAddressDto, GetAddressDto } from './address.types';
import { apiClient } from '..';

export const addressService = new AddressService(apiClient.getAxiosInstance());

export const addressServiceHook = createEntityHooks<
  GetAddressDto,
  CreateAddressDto,
  CreateAddressDto
>('Address', addressService);
