import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { apiClient } from '..';
import { IDrivers } from './driver.types';
import { AxiosError } from 'axios';

const api = apiClient.getAxiosInstance();

export const getDrivers = async (): Promise<IDrivers[] | null> => {
  const response = await api.get('api/v1/drivers/all/minimal');

  if (response.status === 200 && response.data) {
    return response.data.data as IDrivers[];
  }
  return null;
};

export const useGetDrivers = (
  options?: Omit<UseQueryOptions<any, AxiosError>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    ...options,
    queryKey: ['drivers'],
    queryFn: getDrivers,
    staleTime: 20000,
  });
};
