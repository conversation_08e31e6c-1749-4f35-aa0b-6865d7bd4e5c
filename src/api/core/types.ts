/**
 * Base interface for API response
 * @template T - The type of data in the response
 */
export interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

/**
 * Base interface for pagination parameters
 */
export interface PaginationParams {
  pageNumber: number;
  pageSize: number;
}

/**
 * Base interface for paginated response
 * @template T - The type of items in the paginated response
 */
export interface PaginatedResponse<T> {
  // flatMap(arg0: (zone: any) => any): import('react').SetStateAction<string[]>;
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  totalDistance?: number;
}

/**
 * Base interface for filter parameters
 */
export interface FilterParams {
  [key: string]: string | number | boolean | undefined;
  sortDirection?: 'ASC' | 'DESC';
  searchTerm?: string;
  filter?: any;
}
