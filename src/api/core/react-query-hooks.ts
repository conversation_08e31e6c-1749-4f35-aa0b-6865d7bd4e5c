import {
  useQuery,
  useMutation,
  UseQueryOptions,
  UseMutationOptions,
  QueryClient,
} from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { PaginationParams, PaginatedResponse, FilterParams } from './types';
import { BaseService } from '@api/core/base-service.ts';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

export const createEntityHooks = <T, CreateDTO = Partial<T>, UpdateDTO = Partial<T>>(
  queryKey: string,
  service: BaseService<T, CreateDTO, UpdateDTO>
) => {
  const useList = (
    params?: PaginationParams & FilterParams,
    options?: Omit<UseQueryOptions<PaginatedResponse<T>, AxiosError>, 'queryKey' | 'queryFn'>
  ) => {
    return useQuery({
      queryKey: [queryKey, 'list', params] as const,
      queryFn: () => service.getList(params),
      refetchOnWindowFocus: false,
      ...options,
    });
  };
  const useGetByEntity = (
    entity: string,
    params?: PaginationParams,
    options?: Omit<UseQueryOptions<PaginatedResponse<T>, AxiosError>, 'queryKey' | 'queryFn'>
  ) => {
    return useQuery({
      queryKey: [queryKey, 'list', entity] as const,
      queryFn: () => service.getByEntity(entity, params),
      refetchOnWindowFocus: false,
      ...options,
    });
  };

  const useEntities = <D = T>(
    id: string,
    params?: PaginationParams & FilterParams,
    options?: Omit<UseQueryOptions<PaginatedResponse<D>, AxiosError>, 'queryKey' | 'queryFn'>
  ) => {
    return useQuery({
      queryKey: [queryKey, 'list', params] as const,
      queryFn: () => service.getListById<D>(id, params),
      refetchOnWindowFocus: false,
      ...options,
    });
  };

  const useEntity = <D = T>(
    id: string | number,
    options?: Omit<UseQueryOptions<D, AxiosError>, 'queryKey' | 'queryFn'>
  ) => {
    return useQuery({
      queryKey: [queryKey, 'detail', id] as const,
      queryFn: () => service.getById<D>(id),
      enabled: Boolean(id),
      refetchOnWindowFocus: false,
      ...options,
    });
  };

  const useCreate = (
    options?: Omit<UseMutationOptions<T, AxiosError, CreateDTO>, 'mutationFn'>
  ) => {
    return useMutation({
      mutationFn: (data: CreateDTO) => service.create(data),
      onSuccess: () => {
        // Invalidate the list query when a new entity is created
        queryClient.invalidateQueries({ queryKey: [queryKey, 'list'] });
      },
      ...options,
    });
  };

  const useCreateById = (
    entity: string,
    id: string,
    options?: Omit<UseMutationOptions<T, AxiosError, CreateDTO>, 'mutationFn'>
  ) => {
    return useMutation({
      mutationFn: (data: CreateDTO) => service.createById(data, id, entity),
      onSuccess: () => {
        // Invalidate the list query when a new entity is created
        queryClient.invalidateQueries({ queryKey: [queryKey, 'list'] });
      },
      ...options,
    });
  };

  const useUpdate = <D = UpdateDTO>(
    options?: Omit<
      UseMutationOptions<T, AxiosError, { id: string | number; data: D }>,
      'mutationFn'
    >
  ) => {
    return useMutation({
      mutationFn: ({ id, data }: { id: string | number; data: D }) => service.update(id, data),
      onSuccess: (_, variables) => {
        // Invalidate both list and detail queries
        queryClient.invalidateQueries({ queryKey: [queryKey, 'list'] });
        queryClient.invalidateQueries({
          queryKey: [queryKey, 'detail', variables.id],
        });
      },
      ...options,
    });
  };

  const useDelete = (
    options?: Omit<UseMutationOptions<void, AxiosError, string | number>, 'mutationFn'>
  ) => {
    return useMutation({
      mutationFn: (id: string | number) => service.delete(id),
      onSuccess: () => {
        // Invalidate the list query when an entity is deleted
        queryClient.invalidateQueries({ queryKey: [queryKey, 'list'] });
      },
      ...options,
    });
  };

  const useDuplicate = (
    options?: Omit<UseMutationOptions<void, AxiosError, string | number>, 'mutationFn'>
  ) => {
    return useMutation({
      mutationFn: (id: string) => service.duplicate(id),
      onSuccess: () => {
        // Invalidate the list query when an entity is duplicated
        queryClient.invalidateQueries({ queryKey: [queryKey, 'list'] });
      },
      ...options,
    });
  };

  return {
    useList,
    useEntities,
    useEntity,
    useCreate,
    useUpdate,
    useDelete,
    useDuplicate,
    useCreateById,
    useGetByEntity,
  };
};
