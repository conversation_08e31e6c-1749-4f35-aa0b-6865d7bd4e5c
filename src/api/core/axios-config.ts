import { handleSystemErrorNotification } from '@/lib/errorHandling/ErrorCodes';
import { sessionExpiredHandler } from '@/lib/helper/userHelper';
import { TrackedError } from '@/types/AxiosTypes';
import axios, { AxiosInstance, AxiosError } from 'axios';

/**
 * Configuration for the API client
 */
export interface ApiConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

/**
 * Creates and configures an Axios instance
 */
export class ApiClient {
  private instance: AxiosInstance;

  constructor(config: ApiConfig) {
    this.instance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 10000,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    });

    this.setupInterceptors();
  }

  private isError = false;

  private setupInterceptors(): void {
    this.instance.interceptors.request.use(
      async (config) => {
        return config;
      },
      (error) => Promise.reject(error)
    );

    this.instance.interceptors.response.use(
      async (response) => response,
      async (error: AxiosError) => {
        if (!this.isError) {
          this.isError = true;
          const errorStack = error?.response?.data as TrackedError;

          if (errorStack.code !== '200102' && errorStack.statusCode === 401) {
            sessionExpiredHandler();
            handleSystemErrorNotification(errorStack?.statusCode, errorStack);
            return;
          }

          handleSystemErrorNotification(errorStack?.code, errorStack);
          this.isError = false;
        }
        await Promise.reject(error);
      }
    );
  }

  public getAxiosInstance(): AxiosInstance {
    return this.instance;
  }
}
