import { ITierForConfigure } from '@/pages/priceRules/priceModifier/priceModifiers.types';
import { IPriceModifierGroup } from '../groupModifiers/groupModifier.types';

export enum CalculationType {
  FixedAmount = 'FixedAmount',
  FixedPercentage = 'FixedPercentage',
  FixedOverageAmount = 'FixedOverageAmount',
  FixedOveragePercentage = 'FixedOveragePercentage',
  TieredFixedOverageAmount = 'TieredFixedOverageAmount',
  TieredFixedOveragePercentage = 'TieredFixedOveragePercentage',
  IncrementalOverageAmount = 'IncrementalOverageAmount',
  IncrementalOveragePercentage = 'IncrementalOveragePercentage',
  TieredIncrementalOverageAmount = 'TieredIncrementalOverageAmount',
  TieredIncrementalOveragePercentage = 'TieredIncrementalOveragePercentage',
}

export enum CalculationField {
  BasePrice = 'BasePrice',
  DeclaredValue = 'DeclaredPrice',
  CubicDimensions = 'CubicDimensions',
  Distance = 'Distance',
  Height = 'Height',
  Width = 'Width',
  Length = 'Length',
  Quantity = 'Quantity',
  CollectionWaitTime = 'CollectionWaitTime',
  DeliveryWaitTime = 'DeliveryWaitTime',
  Weight = 'Weight',
  CustomAmount = 'CustomAmount',
}

export enum RangeFromOperator {
  GreaterThan = 'GreaterThan',
  GreaterThanOrEqual = 'GreaterThanOrEqual',
}

export enum RangeToOperator {
  LessThan = 'LessThan',
  LessThanOrEqual = 'LessThanOrEqual',
}

export enum ModifierGroupBehavior {
  UseSum = 'UseSum',
  UseHighest = 'UseHighest',
  UseLowest = 'UseLowest',
}
export interface IMetadata {
  summaryText: string;
}
export interface CreatePriceModifierDto {
  name: string;
  calculationType: CalculationType;
  fieldName: CalculationField;
  applicableRangeMin: number;
  applicableRangeMax: number;
  calculationBase: number;
  increment: number;
  amount: number;
  tieredRanges: ITierForConfigure[];
  metaData?: IMetadata;
  defaultValue?: number;
  isGroup: boolean;
  transferId?: string;
}

export interface IPriceModifier extends CreatePriceModifierDto {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  transferId?: string;
}

export type IPriceModifiersListing = IPriceModifier | IPriceModifierGroup;

export interface PriceModifierPaginatedResponse {
  data: IPriceModifier[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
export interface IPriceModifierTablePros {
  searchText?: string;
  onEdit?: (priceModifier: IPriceModifier) => void;
  onDelete?: (priceModifier: IPriceModifier) => void;
  isColumnSortable: (field: string) => boolean;
}
