import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { PriceModifierService } from './priceModifier.service';
import { CreatePriceModifierDto, IPriceModifiersListing } from './priceModifier.types';

export const priceModifierService = new PriceModifierService(apiClient.getAxiosInstance());

export const priceModifierHook = createEntityHooks<
  IPriceModifiersListing,
  CreatePriceModifierDto,
  CreatePriceModifierDto
>(QueryHookKey.Price_Modifier, priceModifierService);
