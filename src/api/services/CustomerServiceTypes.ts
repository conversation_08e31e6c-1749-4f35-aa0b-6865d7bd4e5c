import notificationManagerInstance from '@/hooks/useNotificationManger';
import { IndexedDBStore } from '@/lib/indexedDB';
import { StoreName } from '@/lib/indexedDB/databaseConfig';
import { ICustomerService } from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { useQuery } from '@tanstack/react-query';

const customerServicesStore = new IndexedDBStore<ICustomerService>(StoreName.customerService);

const getCustomerServices = async () => {
  try {
    const response = await customerServicesStore.getAll();
    const sortedItems = response.sort((a, b) => {
      if (a.isSelected === b.isSelected) return 0;
      return a.isSelected ? -1 : 1;
    });
    return sortedItems;
  } catch (error) {
    // will moved this notification string in language file after API integration
    notificationManagerInstance.error({ message: 'error', description: 'something went wrong' });
  }
};

export const useGetCustomersServices = () => {
  return useQuery({
    queryKey: ['customerServices'],
    queryFn: getCustomerServices,
  });
};
