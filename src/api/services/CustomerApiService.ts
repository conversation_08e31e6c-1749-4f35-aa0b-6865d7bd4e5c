import notificationManagerInstance from '@/hooks/useNotificationManger';
import { IndexedDBStore } from '@/lib/indexedDB';
import { StoreName } from '@/lib/indexedDB/databaseConfig';
import { ICustomerService } from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { useQuery } from '@tanstack/react-query';

const customerStore = new IndexedDBStore<ICustomerService>(StoreName.Customers);

const getCustomers = async () => {
  try {
    const response = await customerStore.getAll();
    return response;
  } catch (error) {
    // will moved this notification string in language file after API integration
    notificationManagerInstance.error({ message: 'error', description: 'something went wrong' });
    return [];
  }
};

export const useGetCustomers = () => {
  return useQuery({
    queryKey: ['customers'],
    queryFn: getCustomers,
  });
};
