import notificationManagerInstance from '@/hooks/useNotificationManger';
import { IndexedDBStore } from '@/lib/indexedDB';
import { StoreName } from '@/lib/indexedDB/databaseConfig';
import { ICustomerAddress } from '@/pages/customer/customerOperations/customerAddress/customerAddress.types';
import { useQuery } from '@tanstack/react-query';

const customerAddressStore = new IndexedDBStore<ICustomerAddress>(StoreName.customerAddress);

const getCustomerAddress = async () => {
  try {
    const response = await customerAddressStore.getAll();
    return response || [];
  } catch (error) {
    // will moved this notification string in language file after API integration
    notificationManagerInstance.error({ message: 'error', description: 'something went wrong' });
    return [];
  }
};

export const useGetCustomersAddress = () => {
  return useQuery({
    queryKey: ['customerAddress'],
    queryFn: getCustomerAddress,
  });
};
