import notificationManagerInstance from '@/hooks/useNotificationManger';
import { IndexedDBStore } from '@/lib/indexedDB';
import { StoreName } from '@/lib/indexedDB/databaseConfig';
import { IZone, IZoneLookupTable } from '@/pages/location/zone/list/zone.type';
import { useQuery } from '@tanstack/react-query';

const lookupTableStore = new IndexedDBStore<IZoneLookupTable>(StoreName.zoneLookup);
const zoneStore = new IndexedDBStore<IZone>(StoreName.Zone);

const loadZonesTable = async () => {
  try {
    const data = await lookupTableStore.getAll();
    return data;
  } catch (error) {
    notificationManagerInstance.error({
      message: 'Error',
      description: 'Failed to load Zones Tables',
    });
    return [];
  }
};

const loadZones = async () => {
  try {
    const data = await zoneStore.getAll();
    return data;
  } catch (error) {
    notificationManagerInstance.error({ message: 'Error', description: 'Failed to load Zones' });
    return [];
  }
};

export const useGetZoneLookup = () => {
  return useQuery({
    queryKey: ['zoneLookup'],
    queryFn: loadZonesTable,
  });
};

export const useGetZones = () => {
  return useQuery({
    queryKey: ['zones'],
    queryFn: loadZones,
  });
};
