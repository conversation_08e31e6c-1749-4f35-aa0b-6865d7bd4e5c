import { QueryHook<PERSON>ey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { apiClient } from '..';
import { GroupModifierService } from './groupModifier.service';
import { CreatePriceModifierGroupDto, IPriceModifierGroup } from './groupModifier.types';

const groupModifierService = new GroupModifierService(apiClient.getAxiosInstance());

export const groupModifierHook = createEntityHooks<
  IPriceModifierGroup,
  CreatePriceModifierGroupDto,
  CreatePriceModifierGroupDto
>(QueryHookKey.Group_Modifier, groupModifierService);
