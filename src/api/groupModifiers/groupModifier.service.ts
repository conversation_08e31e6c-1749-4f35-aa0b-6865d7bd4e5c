import { AxiosInstance } from 'axios';
import { BaseService } from '../core/base-service';
import { CreatePriceModifierGroupDto, IPriceModifierGroup } from './groupModifier.types';

export class GroupModifierService extends BaseService<
  IPriceModifierGroup,
  CreatePriceModifierGroupDto,
  CreatePriceModifierGroupDto
> {
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/price-modifiers/groups');
  }
}
