import { IUser } from '@/types/UserTypes';
import { removeStorageItem, setStorageItem, setStringifyStorageItem } from '@/lib/Storage';
import { StorageKeys } from '@/types/enums/StorageEnums';
import { apiClient } from '..';
import { AUTH_ENDPOINTS } from '../endpoints/AuthEndpoints';
import { ROUTES } from '@/constant/RoutesConstant';
import { ILogin } from './auth.types';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { AxiosError } from 'axios';

const api = apiClient.getAxiosInstance();

export const login = async (payload: ILogin): Promise<{ user: IUser } | void> => {
  const response = await api.post(AUTH_ENDPOINTS.LOGIN, payload);

  if (response.status === 200 && response.data) {
    setStringifyStorageItem(StorageKeys.USER_INFO, { ...response.data.user, role: 'Tenant' }); //TODO: remove static role after role based access implementation.
    setStorageItem(StorageKeys.IS_AUTHENTICATED, 'true');
    return response.data as { user: IUser };
  }
};

export const logout = async (): Promise<void> => {
  const response = await api.post(AUTH_ENDPOINTS.LOGOUT);

  if (response.status === 200) {
    removeStorageItem(StorageKeys.USER_INFO);
    removeStorageItem(StorageKeys.IS_AUTHENTICATED);
    window.location.replace(ROUTES.COMMON.LOGIN);
  }
};

export const getUserInfo = async (): Promise<IUser | null> => {
  const response = await api.get(AUTH_ENDPOINTS.ME);

  if (response.status === 200 && response.data) {
    setStringifyStorageItem(StorageKeys.USER_INFO, { ...response.data.user, role: 'Tenant' }); //TODO: remove static role after role based access implementation.
    setStorageItem(StorageKeys.IS_AUTHENTICATED, 'true');
    return response.data.user as IUser;
  }
  return null;
};

export const useGetCurrentUser = (
  options?: Omit<UseQueryOptions<any, AxiosError>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    ...options,
    queryKey: ['userDetails'],
    queryFn: getUserInfo,
    staleTime: 120000,
  });
};
