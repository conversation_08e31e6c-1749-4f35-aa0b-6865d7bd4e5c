import axios, { AxiosInstance, AxiosResponse, AxiosError, HttpStatusCode } from 'axios';
import ConfigManager from '@/lib/ConfigManager';
import { ApiError } from '@/lib/errorHandling/CustomError';
import { ExtendedAxiosConfig } from '@customTypes/AxiosTypes';
import { ConfigSchema } from '@customTypes/ConfigSchema';

/**
 * AxiosClient class provides an enhanced Axios instance with advanced error handling,
 * request retrying, refresh token logic, and TypeScript support.
 *
 * @example
 * ```typescript
 * const client = new AxiosClient({
 *   baseURL: 'https://api.example.com',
 *   timeout: 10000,
 *   retries: 5,
 *   retryDelay: 2000,
 * });
 *
 * try {
 *   const userData = await client.get<UserData>('/users/1');
 *   console.log('User data:', userData);
 * } catch (error) {
 *   if (error instanceof ApiError) {
 *     console.error(`API Error ${error.statusCode}:`, error.responseData);
 *   } else {
 *     console.error('Unexpected error:', error);
 *   }
 * }
 * ```
 */
class AxiosClient {
  private axiosInstance: AxiosInstance;
  private globalConfig: ExtendedAxiosConfig;
  private refreshAttempts: number = 0;
  private defaultConfig: Readonly<ConfigSchema['AxiosDefaultConfig']>;

  /**
   * Constructs an AxiosClient instance.
   * @param config - Configuration options for the Axios instance
   */
  constructor(config: ExtendedAxiosConfig = {}) {
    this.defaultConfig = ConfigManager.get('AxiosDefaultConfig')!;
    this.globalConfig = {
      timeout: this.defaultConfig.timeout,
      retries: this.defaultConfig.retries,
      retryDelay: this.defaultConfig.retryDelay,
      ...config,
    };

    this.axiosInstance = axios.create(this.globalConfig);
    this.initInterceptors();
  }

  /**
   * Creates a resolved promise with the given value.
   * @param value - The value to resolve the promise with
   */
  private resolvePromise<T>(value: T): Promise<T> {
    return Promise.resolve(value);
  }

  /**
   * Creates a rejected promise with the given reason.
   * @param reason - The reason for rejecting the promise
   */
  private rejectPromise<T = never>(reason: any): Promise<T> {
    return Promise.reject(reason);
  }

  /**
   * Sets up request and response interceptors for handling authentication, retries, and errors.
   */
  private initInterceptors(): void {
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        if (this.globalConfig.getAccessToken) {
          const token = this.globalConfig.getAccessToken();
          config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
      },
      (error) => this.rejectPromise(error)
    );

    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse<unknown>) => this.resolvePromise(response),
      async (error: AxiosError) => {
        const originalRequest = error.config as ExtendedAxiosConfig;

        if (
          error.response?.status === HttpStatusCode.Unauthorized &&
          this.refreshAttempts < this.defaultConfig.maxRefreshAttempt
        ) {
          this.refreshAttempts++;
          if (this.globalConfig.refreshToken) {
            try {
              await this.globalConfig.refreshToken();
              this.refreshAttempts = 0;
              return this.axiosInstance(originalRequest);
            } catch (refreshError) {
              // If refresh token fails, proceed with normal error handling
              return this.rejectPromise(refreshError);
            }
          }
        }

        if (this.shouldRetry(error)) {
          return this.retryRequest(error);
        }

        return this.rejectPromise(
          new ApiError(
            error.response?.status || HttpStatusCode.InternalServerError,
            error.response?.data,
            originalRequest
          )
        );
      }
    );
  }

  /**
   * Determines if a request should be retried based on the error and configuration.
   * @param error - The error that occurred during the request
   * @returns A boolean indicating whether the request should be retried
   */
  private shouldRetry(error: AxiosError): boolean {
    const config = error.config as ExtendedAxiosConfig;
    const retries = config.retries ?? this.globalConfig.retries;
    return !!retries && retries > 0 && error.response?.status !== HttpStatusCode.Unauthorized;
  }

  /**
   * Retries a failed request after a delay.
   * @param error - The error that occurred during the request
   * @returns A promise that resolves with the retried request
   */
  private async retryRequest(error: AxiosError): Promise<AxiosResponse> {
    const config = error.config as ExtendedAxiosConfig;
    config.retries = (config.retries || this.globalConfig.retries || 1) - 1;
    const retryDelay = config.retryDelay ?? this.globalConfig.retryDelay;

    await new Promise((resolve) => setTimeout(resolve, retryDelay));
    return this.axiosInstance(config);
  }

  /**
   * Sends a request using the Axios instance.
   * @param config - Request configuration
   * @returns A promise that resolves with the response data
   * @throws {ApiError} When the request fails
   */
  public async request<T = any>(config: ExtendedAxiosConfig): Promise<T> {
    try {
      const response = await this.axiosInstance.request<T>({
        ...this.globalConfig,
        ...config,
      });
      return response.data;
    } catch (error) {
      if (error instanceof ApiError) {
        return this.rejectPromise(error);
      }
      return this.rejectPromise(new Error('An unexpected error occurred'));
    }
  }

  public getInstance(): AxiosInstance {
    return this.axiosInstance;
  }

  /**
   * Sends a GET request.
   * @param url - The URL to send the request to
   * @param config - Additional configuration for the request
   * @returns A promise that resolves with the response data
   * @throws {ApiError} When the request fails
   */
  public async get<T = any>(url: string, config?: ExtendedAxiosConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  /**
   * Sends a POST request.
   * @param url - The URL to send the request to
   * @param data - The data to send in the request body
   * @param config - Additional configuration for the request
   * @returns A promise that resolves with the response data
   * @throws {ApiError} When the request fails
   */
  public async post<T = any>(url: string, data?: any, config?: ExtendedAxiosConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  /**
   * Sends a PUT request.
   * @param url - The URL to send the request to
   * @param data - The data to send in the request body
   * @param config - Additional configuration for the request
   * @returns A promise that resolves with the response data
   * @throws {ApiError} When the request fails
   */
  public async put<T = any>(url: string, data?: any, config?: ExtendedAxiosConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  /**
   * Sends a PATCH request.
   * @param url - The URL to send the request to
   * @param data - The data to send in the request body
   * @param config - Additional configuration for the request
   * @returns A promise that resolves with the response data
   * @throws {ApiError} When the request fails
   */
  public async patch<T = any>(url: string, data?: any, config?: ExtendedAxiosConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PATCH', url, data });
  }

  /**
   * Sends a DELETE request.
   * @param url - The URL to send the request to
   * @param config - Additional configuration for the request
   * @returns A promise that resolves with the response data
   * @throws {ApiError} When the request fails
   */
  public async delete<T = any>(url: string, config?: ExtendedAxiosConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }
}

export default AxiosClient;
