import { AxiosError, AxiosResponse } from 'axios';
import { CreateSettingsDto } from './settings.types';
import { apiClient } from '..';
import { UseQueryOptions, useQuery, UseQueryResult } from '@tanstack/react-query';
import { GridStorageKey } from '@/types/AgGridTypes';

const API = apiClient.getAxiosInstance();

export const getSetting = async (
  key: GridStorageKey,
  userId?: string
): Promise<CreateSettingsDto | null> => {
  const endpoint = userId ? `user/${userId}/${key}` : `${key}`;
  if (!key) return null;
  const response: AxiosResponse<CreateSettingsDto> = await API.get(`api/v1/settings/${endpoint}`);
  return response.data;
};

export const addSetting = async (payload: CreateSettingsDto): Promise<CreateSettingsDto> => {
  const response: AxiosResponse<CreateSettingsDto> = await API.post(`api/v1/settings`, payload);
  return response.data;
};
export const updateSetting = async (id: string, payload: any): Promise<CreateSettingsDto> => {
  const response: AxiosResponse<CreateSettingsDto> = await API.put(
    `api/v1/settings/${id}`,
    payload
  );
  return response.data;
};

export const useGetSettings = (
  key: GridStorageKey,
  userId?: string,
  options?: Omit<UseQueryOptions<any, AxiosError>, 'queryKey' | 'queryFn'>
): UseQueryResult<any, Error> => {
  return useQuery({
    ...options,
    retry: false,
    queryKey: [`settings`, key, userId],
    queryFn: () => getSetting(key, userId),
    staleTime: 30000,
  });
};
