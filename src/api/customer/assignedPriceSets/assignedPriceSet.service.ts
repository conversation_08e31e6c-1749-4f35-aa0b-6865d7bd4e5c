import { AxiosInstance } from 'axios';
import { IPostAssignedPriceSets, IGetAssignedPriceSets } from './assignedPriceSet.types';
import { BaseService } from '@/api/core/base-service';

export class AssignedPriceSetService extends BaseService<
  IGetAssignedPriceSets,
  IPostAssignedPriceSets,
  IPostAssignedPriceSets
> {
  constructor(axios: AxiosInstance) {
    super(axios, `/api/v1/customers`);
  }
}
