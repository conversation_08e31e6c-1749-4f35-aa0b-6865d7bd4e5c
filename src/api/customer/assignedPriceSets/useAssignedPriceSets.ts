import { apiClient } from '@/api';
import { createEntityHooks } from '@/api/core/react-query-hooks';
import { IPostAssignedPriceSets, IGetAssignedPriceSets } from './assignedPriceSet.types';
import { AssignedPriceSetService } from './assignedPriceSet.service';

export const assignedPriceSetsService = new AssignedPriceSetService(apiClient.getAxiosInstance());

export const assignedPriceSetsHook = createEntityHooks<
  IGetAssignedPriceSets,
  IPostAssignedPriceSets,
  IPostAssignedPriceSets
>('assignedPriceSets', assignedPriceSetsService);
