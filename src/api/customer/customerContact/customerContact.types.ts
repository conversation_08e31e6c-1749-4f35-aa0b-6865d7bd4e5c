export interface ICategories {
  id?: string;
  name: string;
}

export interface IPermissionsForContacts {
  prices: boolean;
  address: boolean;
  invoices: boolean;
}

export interface CreateCustomerContactDto {
  name: string;
  email: string;
  phoneCountryCode: string;
  phoneNumber: string;
  phoneExtension?: string;
  categories: ICategories[];
  permissions: IPermissionsForContacts;
  userId: string;
  isActive: boolean;
  isPrimary?: boolean;
}

export interface ICustomerContact extends CreateCustomerContactDto {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}
export interface CustomerContactPaginatedResponse {
  data: ICustomerContact[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}
