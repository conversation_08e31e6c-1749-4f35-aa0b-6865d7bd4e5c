import { apiClient } from '@/api';
import { createEntityHooks } from '@/api/core/react-query-hooks';
import { CreateCustomerContactDto, ICustomerContact } from './customerContact.types';
import { CustomerContactsService } from './customerContact.service';

export const customersContactService = new CustomerContactsService(apiClient.getAxiosInstance());

export const customerContactsHook = createEntityHooks<
  ICustomerContact,
  CreateCustomerContactDto,
  CreateCustomerContactDto
>('customerContacts', customersContactService);
