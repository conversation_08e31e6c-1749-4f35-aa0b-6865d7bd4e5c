import { AxiosInstance } from 'axios';
import { BaseService } from '../core/base-service';
import { CreateCustomerDto, ICustomer } from './customer.types';

export class CustomerService extends BaseService<ICustomer, CreateCustomerDto, CreateCustomerDto> {
  /**
   * Creates a new instance of ZoneService
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/customers');
  }
  async generateAccountNumber(): Promise<{ accountNumber: string }> {
    const response = await this.axios.get<{ accountNumber: string }>(
      '/api/v1/customers/account-number/generate'
    );
    return response.data;
  }
}
