import { apiClient } from '..';
import { createEntityHooks } from '../core/react-query-hooks';
import { CustomerService } from './customer.service';
import { CreateCustomerDto, ICustomer } from './customer.types';

export const customerService = new CustomerService(apiClient.getAxiosInstance());

export const customerHook = createEntityHooks<ICustomer, CreateCustomerDto, CreateCustomerDto>(
  'customer',
  customerService
);

export const customerServiceHook = createEntityHooks<
  ICustomer,
  CreateCustomerDto,
  CreateCustomerDto
>('customer', customerService);
