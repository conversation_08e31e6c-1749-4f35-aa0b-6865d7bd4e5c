import { AxiosInstance } from 'axios';
import { BaseService } from '../core/base-service';
import { CreatePriceSetDto, IPriceSet } from './priceSet.types';

export class PriceSetService extends BaseService<IPriceSet, CreatePriceSetDto, CreatePriceSetDto> {
  /**
   * Creates a new instance of ZoneService
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/priceSets');
  }
}
