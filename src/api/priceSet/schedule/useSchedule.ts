import { apiClient } from '@/api';
import { createEntityHooks } from '@/api/core/react-query-hooks';
import { QueryHookKey } from '@/constant/QueryHookConstant';
import { ScheduleService } from './schedule.service';
import { CreateScheduleDto, ISchedule } from './schedule.types';

const scheduleService = new ScheduleService(apiClient.getAxiosInstance());

export const scheduleServiceHook = createEntityHooks<
  ISchedule,
  CreateScheduleDto,
  CreateScheduleDto
>(QueryHookKey.Schedule, scheduleService);
