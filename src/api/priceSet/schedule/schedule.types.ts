import { Dayjs } from 'dayjs';

export type IPaymentOptions = 'none' | 'partial' | 'full';

export interface IScheduleForm {
  availabilityType: PriceSetAvailability;
  offsetType: OffsetType;
  time: Dayjs;
  hours: string;
  minutes: string;
  daysOut: string;
  schedule: IFormSchedules[];
  includeWeekends: boolean;
}

export enum PriceSetAvailability {
  NEVER = 'never',
  WEEKLY = 'weekly',
  ALWAYS = 'always',
}

export enum OffsetType {
  TO = 'to',
  BY = 'by',
}

export interface IScheduleArray {
  days: string;
  startTime: string;
  endTime: string;
}
export interface IOffsetData {
  hours: number | null;
  minutes: number | null;
  time: string | null;
  daysOut: number | null;
  includeWeekends: boolean | null;
}

export interface CreateScheduleDto {
  availabilityType: PriceSetAvailability;
  offsetType: OffsetType;
  offsetData: IOffsetData;
  schedule: IScheduleArray[];
}
export interface IFormSchedules {
  days: string[];
  time: [Dayjs, Dayjs];
}

export interface ISchedule extends CreateScheduleDto {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface SchedulePaginatedResponse {
  total: number;
  pageNumber: number;
  pageSize: number;
  data: ISchedule[];
}
