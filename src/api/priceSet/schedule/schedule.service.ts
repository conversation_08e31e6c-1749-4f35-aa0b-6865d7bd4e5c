import { AxiosInstance } from 'axios';
import { CreateScheduleDto, ISchedule } from './schedule.types';
import { BaseService } from '@/api/core/base-service';

export class ScheduleService extends BaseService<ISchedule, CreateScheduleDto, CreateScheduleDto> {
  /**
   * Creates a new instance of ScheduleService
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/priceSets');
  }
}
