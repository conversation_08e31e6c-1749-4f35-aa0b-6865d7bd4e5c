export interface IGetAssignedModifiers {
  id?: string;
  name: string;
  memberId: string;
  isGroup: boolean;
  configuration: ModifiersConfig;
}

export interface IChangeConfigDTO {
  configuration: ModifiersConfig;
}

export enum ModifiersConfig {
  'none',
  'selected',
  'notSelected',
}

export interface IPostAssignedModifiers {
  members: {
    id: string;
    isGroup: boolean;
    configuration?: string;
  }[];
}
