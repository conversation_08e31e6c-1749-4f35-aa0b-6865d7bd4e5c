import { apiClient } from '@/api';
import { createEntityHooks } from '@/api/core/react-query-hooks';
import { IGetAssignedModifiers, IPostAssignedModifiers } from './assignedModifiers.types';
import { AssignedModifierService } from './assignedModifier.service';

export const assignedModifierService = new AssignedModifierService(apiClient.getAxiosInstance());

export const assignedModifierHook = createEntityHooks<
  IGetAssignedModifiers,
  IPostAssignedModifiers,
  IPostAssignedModifiers
>('assignedModifier', assignedModifierService);
