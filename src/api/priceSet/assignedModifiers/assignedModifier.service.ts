import { AxiosInstance } from 'axios';
import { BaseService } from '@/api/core/base-service';
import { IGetAssignedModifiers, IPostAssignedModifiers } from './assignedModifiers.types';

export class AssignedModifierService extends BaseService<
  IGetAssignedModifiers,
  IPostAssignedModifiers,
  IPostAssignedModifiers
> {
  constructor(axios: AxiosInstance) {
    super(axios, `/api/v1/priceSets`);
  }
}
