import { apiClient } from '@/api';
import { createEntityHooks } from '@/api/core/react-query-hooks';
import { IGetAssignedCustomers, IPostAssignedCustomers } from './assignedCustomers.types';
import { AssignedCustomerService } from './assignedCustomers.service';

export const assignedCustomersService = new AssignedCustomerService(apiClient.getAxiosInstance());

export const assignedCustomersHook = createEntityHooks<
  IGetAssignedCustomers,
  IPostAssignedCustomers,
  IPostAssignedCustomers
>('assignedCustomers', assignedCustomersService);
