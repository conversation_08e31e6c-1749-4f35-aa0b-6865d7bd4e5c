import { AxiosInstance } from 'axios';
import { BaseService } from '@/api/core/base-service';
import { IGetAssignedCustomers, IPostAssignedCustomers } from './assignedCustomers.types';

export class AssignedCustomerService extends BaseService<
  IGetAssignedCustomers,
  IPostAssignedCustomers,
  IPostAssignedCustomers
> {
  constructor(axios: AxiosInstance) {
    super(axios, `/api/v1/priceSets`);
  }
}
