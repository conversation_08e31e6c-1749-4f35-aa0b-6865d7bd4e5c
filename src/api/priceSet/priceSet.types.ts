export type IPaymentOptions = 'none' | 'partial' | 'full';

export interface CreatePriceSetDto {
  name: string;
  internalName: string;
  paymentOption: IPaymentOptions;
  description: string;
  notes: string;
}
export interface IPriceSet extends CreatePriceSetDto {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}
export interface PriceSetPaginatedResponse {
  total: number;
  pageNumber: number;
  pageSize: number;
  data: IPriceSet[];
}

export interface ZoneTableValues {
  originZoneId: string;
  destinationZoneId: string;
  value: number;
}

export interface BasePriceByZoneDTO {
  name: string;
  zoneTableValues: ZoneTableValues[];
}
