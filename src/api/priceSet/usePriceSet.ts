import { QueryHookKey } from '@/constant/QueryHookConstant';
import { createEntityHooks } from '../core/react-query-hooks';
import { PriceSetService } from './priceSet.service';
import { IPriceSet, CreatePriceSetDto } from './priceSet.types';
import { apiClient } from '..';

export const priceSetService = new PriceSetService(apiClient.getAxiosInstance());

export const priceSetHook = createEntityHooks<IPriceSet, CreatePriceSetDto, CreatePriceSetDto>(
  QueryHookKey.Price_Set,
  priceSetService
);
