import { AxiosInstance } from 'axios';
import { BaseService } from '@api/core/base-service.ts';
import { CreateZoneTableDto, ZoneTable } from './zoneTable.types';

/**
 * Service class for managing zones
 */
export class ZoneTableService extends BaseService<
  ZoneTable,
  CreateZoneTableDto,
  CreateZoneTableDto
> {
  /**
   * Creates a new instance of ZoneService
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/zoneTable');
  }
}
