import { createEntityHooks } from '@api/core/react-query-hooks.ts';
import { ZoneTableService } from './zoneTable.service';
import { CreateZoneTableDto, ZoneTable } from './zoneTable.types';
import { apiClient } from '..';
import { QueryHookKey } from '@/constant/QueryHookConstant';

// Create the zone service instance
export const zoneTableService = new ZoneTableService(apiClient.getAxiosInstance());

// Create the hooks using our generic hook factory
export const zoneTableHook = createEntityHooks<ZoneTable, CreateZoneTableDto, CreateZoneTableDto>(
  QueryHookKey.Zone_Tables,
  zoneTableService
);
