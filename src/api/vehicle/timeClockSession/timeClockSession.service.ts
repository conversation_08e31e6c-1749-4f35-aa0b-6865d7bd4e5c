import { AxiosInstance } from 'axios';
import { BaseService } from '@api/core/base-service.ts';
import { CreateTimeClockSessionDto, GetTimeClockSessionDto } from './timeClockSession.types';

/**
 * Service class for managing Time Clock Session
 */
export class TimeClockSessionService extends BaseService<
  GetTimeClockSessionDto,
  CreateTimeClockSessionDto,
  CreateTimeClockSessionDto
> {
  /**
   * Creates a new instance of TimeClockSession
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/timeClockSession');
  }

  /**
   * Override the getList method to match the API's pagination format
   */
}
