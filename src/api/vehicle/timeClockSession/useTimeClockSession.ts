import { createEntityHooks } from '@api/core/react-query-hooks.ts';
import { TimeClockSessionService } from './timeClockSession.service';
import { CreateTimeClockSessionDto, GetTimeClockSessionDto } from './timeClockSession.types';
import { apiClient } from '@/api';

const timeClockSessionService = new TimeClockSessionService(apiClient.getAxiosInstance());

// Create the hooks using our generic hook factory
export const timeClockSessionServiceHook = createEntityHooks<
  GetTimeClockSessionDto,
  CreateTimeClockSessionDto,
  CreateTimeClockSessionDto
>('timeClockSession', timeClockSessionService);
