export interface CreateTimeClockSessionDto {
  driverId: string;
  vehicleId: string;
  distanceTraveled: number;
  startTime: string;
  endTime: string;
  source: SessionSource;
}

export enum SessionSource {
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
}

export enum SessionStatus {
  IN_COMPLETE = 0,
  ACTIVE = 1,
}

export interface GetTimeClockSessionDto extends CreateTimeClockSessionDto {
  totalHours: string;
  totalDistance: number;
  status: SessionStatus;
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export interface GetAllTimeClockSessionDto {
  total: number;
  pageNumber: number;
  pageSize: number;
  data: GetTimeClockSessionDto[];
  totalDistance: string;
}
