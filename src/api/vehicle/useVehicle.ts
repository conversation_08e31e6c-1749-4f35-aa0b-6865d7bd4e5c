import { createEntityHooks } from '@api/core/react-query-hooks.ts';
import { VehicleService } from './vehicle.service';
import { GetVehicleDto, CreateVehicleDto } from './vehicle.types';
import { apiClient } from '..';

// Create the vehicle service instance
export const vehicleService = new VehicleService(apiClient.getAxiosInstance());

// Create the hooks using our generic hook factory
export const vehicleServiceHooks = createEntityHooks<
  GetVehicleDto,
  CreateVehicleDto,
  CreateVehicleDto
>('vehicles', vehicleService);
