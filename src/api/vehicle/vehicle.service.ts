import { AxiosInstance } from 'axios';
import { BaseService } from '@api/core/base-service.ts';
import {} from './vehicleTypes/vehicleType.types';
import { GetVehicleDto, CreateVehicleDto } from './vehicle.types';

/**
 * Service class for managing vehicles
 */
export class VehicleService extends BaseService<GetVehicleDto, CreateVehicleDto, CreateVehicleDto> {
  /**
   * Creates a new instance of VehicleType
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/vehicles');
  }

  async generateFleetId(): Promise<{ fleetId: string }> {
    const response = await this.axios.get<{ fleetId: string }>('/api/v1/vehicles/generateFleetId');
    return response.data;
  }
}
