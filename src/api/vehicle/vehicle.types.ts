import { ITimeClockSessions } from '@/pages/logistics/vehicle/vehicleTypes';

export interface CreateVehicleDto {
  vehicleTypeId: string;
  fleetId: string;
  make: string;
  model: string;
  year: string;
  licensePlate: string;
  vin: string;
  packageType: string;
  maxWeight: string;
  branch: string;
  currentOdometer: string;
  ownedBy: string;
  notes: string;
}

export interface GetVehicleDto extends CreateVehicleDto {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  timeClockSessions?: ITimeClockSessions[];
}

export interface GetAllVehicleDto {
  total: number;
  pageNumber: number;
  pageSize: number;
  data: GetVehicleDto[];
}
