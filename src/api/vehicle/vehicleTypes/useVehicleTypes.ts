import { createEntityHooks } from '@api/core/react-query-hooks.ts';
import { VehicleTypes } from './vehicleType.service';
import { CreateVehicleTypeDto, GetVehicleTypeDto } from './vehicleType.types';
import { apiClient } from '@/api';

// Create the vehicle service instance
const vehicleTypeService = new VehicleTypes(apiClient.getAxiosInstance());

// Create the hooks using our generic hook factory
export const vehicleTypeServiceHook = createEntityHooks<
  GetVehicleTypeDto,
  CreateVehicleTypeDto,
  CreateVehicleTypeDto
>('vehicleTypes', vehicleTypeService);
