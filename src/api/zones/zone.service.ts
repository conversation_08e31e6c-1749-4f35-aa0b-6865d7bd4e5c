import { AxiosInstance } from 'axios';
import { CreateZoneDto, Zone } from '@api/zones/zone.types.ts';
import { BaseService } from '@api/core/base-service.ts';

/**
 * Service class for managing zones
 */
export class ZoneService extends BaseService<Zone, CreateZoneDto, CreateZoneDto> {
  /**
   * Creates a new instance of ZoneService
   * @param axios - Configured Axios instance
   */
  constructor(axios: AxiosInstance) {
    super(axios, '/api/v1/zones');
  }
}
