/**
 * Zone creation DTO interface based on API spec
 */
export interface CreateZoneDto {
  /** Name of the zone */
  name: string;
  /** Array of postal codes included in the zone */
  postalCodes: string[];
  /** Additional notes about the zone */
  notes: string;
}

/**
 * Zone response interface based on API spec
 */
export interface Zone extends CreateZoneDto {
  /** Unique identifier for the zone */
  id: string;
  /** Timestamp when the zone was created */
  createdAt: string;
  /** Timestamp when the zone was last updated */
  updatedAt: string;
  /** User who created the zone */
  createdBy: string;
  /** User who last updated the zone */
  updatedBy: string;
}

/**
 * Paginated response interface for zones
 */
export interface ZonePaginatedResponse {
  /** Total number of zones */
  total: number;
  /** Current page number */
  pageNumber: number;
  /** Number of items per page */
  pageSize: number;
  /** Array of zone data */
  data: Zone[];
}
