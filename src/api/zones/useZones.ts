import { ZoneService } from '@api/zones/zone.service.ts';
import { createEntityHooks } from '@api/core/react-query-hooks.ts';
import { CreateZoneDto, Zone } from '@api/zones/zone.types.ts';
import { apiClient } from '..';
import { QueryHookKey } from '@/constant/QueryHookConstant';

// Create the zone service instance
export const zoneService = new ZoneService(apiClient.getAxiosInstance());

// Create the hooks using our generic hook factory
export const zoneHooks = createEntityHooks<Zone, CreateZoneDto, CreateZoneDto>(
  QueryHookKey.Zones,
  zoneService
);
