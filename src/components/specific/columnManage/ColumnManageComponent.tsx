import { useEffect, useRef, useState } from 'react';
import { AnimatePresence, Reorder } from 'framer-motion';
import { Button, Checkbox, Input, Popover } from 'antd';
import './ColumnManage.css';
import { ColDef } from 'ag-grid-community';
import { useLanguage } from '@/hooks/useLanguage';
import PermissionChecker from '../permissionChecker/PermissionChecker';
import {
  ColumnManageMenuIcon,
  ColumnMergeIcon,
  infoCircleOutlined,
  SearchInputIcon,
} from '@/assets';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';

interface IColumnManageProps {
  colDefs: ColDef[];
}
interface IColumns {
  id: string;
  label: string;
}
const ColumnManage = ({ colDefs }: IColumnManageProps) => {
  const [columns, setColumns] = useState<IColumns[]>([]);
  const [defaultColumns, setDefaultColumns] = useState<IColumns[]>([]);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [isColumnManageOpen, setIsColumnManageOpen] = useState<boolean>(false);
  const [searchedColumns, setSearchedColumns] = useState<string>('');
  const [isDragging, setIsDragging] = useState(false);
  const [draggingColumnId, setDraggingColumnId] = useState<string>('');
  const { t } = useLanguage();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const mappedValues = colDefs?.map((col) => {
      if (!col.field || !col.headerName) return { id: '', label: '' };
      return { id: col?.field, label: col?.headerName };
    });
    setColumns(mappedValues);
    setDefaultColumns(mappedValues);
    setSelectedColumns(mappedValues?.map((col) => col.id));
  }, [colDefs]);

  /**
   * Checks if the user is hovering near the top or bottom of the column list
   * and scrolls the list automatically if so.
   *
   * @param {number} cursorY - The y-coordinate of the user's cursor
   */

  const checkAutoScroll = (cursorY: number) => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const { top, bottom } = container.getBoundingClientRect();
    const scrollSpeed = 7;

    if (cursorY - top < 40) {
      container.scrollTop -= scrollSpeed;
    } else if (bottom - cursorY < 40) {
      container.scrollTop += scrollSpeed;
    }
  };

  const handleSelectAll = (e: { target: { checked: boolean } }) => {
    if (e.target.checked) {
      setSelectedColumns(columns.map((col) => col.id));
    } else {
      setSelectedColumns([]);
    }
  };

  const handleColumnSelect = (columnId: string) => {
    setSelectedColumns((prev) =>
      prev.includes(columnId) ? prev.filter((id) => id !== columnId) : [...prev, columnId]
    );
  };

  const filteredColumns = columns?.filter((column) =>
    column.label.toLowerCase().includes(searchedColumns.toLowerCase())
  );

  /**
   * Handles mouse move events during a drag operation by calling
   * `checkAutoScroll` to scroll the column list automatically if the user
   * is hovering near the top or bottom of the list.
   *
   * @param {{ clientY: number }} e - The mouse event
   */
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) checkAutoScroll(e.clientY);
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, [isDragging]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef?.current.contains(event.target as Node)) {
        setIsColumnManageOpen(false);
        setSearchedColumns('');
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  const handleDraggingItem = (e: string) => {
    setIsDragging(true);
    setDraggingColumnId(e);
  };
  const handleDragStop = () => {
    setIsDragging(false);
    setDraggingColumnId('');
  };
  return (
    <div className="main-container">
      {' '}
      <PermissionChecker feature="manageColumn" type="popover">
        <Popover
          trigger="click"
          placement="bottomRight"
          content={
            <div ref={containerRef} className="manage-columns-container">
              {' '}
              <div className="manage-columns-header">
                <h2 className="column-manage-title">Column manage</h2>
                <CustomTooltip title={t('columnMange.columnManagerTooltip')}>
                  <img src={infoCircleOutlined} alt="info" />
                </CustomTooltip>
              </div>
              <div>
                <div className="inside-content-container">
                  <div className="input-wrapper">
                    <Input
                      className="search-input"
                      value={searchedColumns}
                      type="text"
                      placeholder="Search"
                      onChange={(e) => {
                        setSearchedColumns(e.target.value);
                      }}
                      prefix={<SearchInputIcon />}
                    />
                  </div>
                  <p className="uncheck-info-text">{`${t('columnMange.uncheckColumn')}`}</p>
                  <div className="scroll-container" ref={scrollContainerRef}>
                    {searchedColumns === '' && (
                      <div className="select-all-container">
                        <Checkbox
                          id="select-all-tailwind"
                          checked={selectedColumns?.length === columns?.length}
                          onChange={handleSelectAll}
                          className="select-all-checkbox "
                        />
                        <label htmlFor="select-all-tailwind" className="select-all-label">
                          {`${t('columnMange.selectAll')}`}
                        </label>
                      </div>
                    )}
                    <Reorder.Group axis="y" values={columns} onReorder={setColumns}>
                      <AnimatePresence initial={false}>
                        {filteredColumns && filteredColumns.length > 0 ? (
                          filteredColumns.map((column) => (
                            <Reorder.Item
                              key={column.id}
                              value={column}
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              exit={{ opacity: 0 }}
                              layout
                              transition={{
                                opacity: { duration: 0.5, ease: 'easeIn' },
                                layout: { duration: 0.8, type: 'spring' },
                              }}
                              className={`reorder-item ${draggingColumnId === column.id ? 'dragging-item' : ''}`}
                              onDragStart={() => handleDraggingItem(column.id)}
                              onDragEnd={() => handleDragStop()} // Stop dragging
                            >
                              <div className="checkbox-label-container">
                                <Checkbox
                                  id={`${column.id}-tailwind`}
                                  checked={selectedColumns.includes(column.id)}
                                  onChange={() => handleColumnSelect(column.id)}
                                  className="all-checkboxes"
                                />
                                <label htmlFor={`${column.id}-tailwind`} className="column-label">
                                  {column.label}
                                </label>
                              </div>
                              <Button
                                className="column-menu-button"
                                icon={<ColumnManageMenuIcon />}
                              />
                            </Reorder.Item>
                          ))
                        ) : (
                          <div className="no-columns-message">No such column exists</div>
                        )}
                      </AnimatePresence>
                    </Reorder.Group>
                  </div>
                </div>
              </div>
              <div className="button-container">
                <Button onClick={() => setColumns(defaultColumns)} className="reset-button">
                  {`${t('columnMange.resetToDefault')}`}
                </Button>
                <Button className="apply-button">{`${t('columnMange.apply')}`}</Button>
              </div>
            </div>
          }
        >
          <Button
            className="column-manager-button"
            icon={<ColumnMergeIcon bool={isColumnManageOpen} />}
            onClick={() => setIsColumnManageOpen(true)}
          />
        </Popover>
      </PermissionChecker>
    </div>
  );
};

const ColumnManageComponent = ({ colDefs }: IColumnManageProps) => {
  return (
    <div>
      <ColumnManage colDefs={colDefs} />
    </div>
  );
};

export default ColumnManageComponent;
