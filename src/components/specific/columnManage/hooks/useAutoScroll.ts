import { RefObject, useEffect } from 'react';

export const useAutoScroll = (
  scrollContainerRef: RefObject<HTMLDivElement>,
  isDragging: boolean
) => {
  const checkAutoScroll = (cursorY: number) => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const { top, bottom } = container.getBoundingClientRect();
    const scrollSpeed = 7;

    if (cursorY - top < 40) {
      container.scrollTop -= scrollSpeed;
    } else if (bottom - cursorY < 40) {
      container.scrollTop += scrollSpeed;
    }
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) checkAutoScroll(e.clientY);
    };

    document.addEventListener('mousemove', handleMouseMove);
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, [isDragging]);

  return checkAutoScroll;
};
