import { useCallback, useEffect, useState } from 'react';
import { IColumns } from '../types';
import { emit } from '@contexts/PulseContext.tsx';
import { IColDef } from '@/types/AgGridTypes.ts';
import { GridNames } from '@/types/AppEvents.ts';
import { updateSetting, useGetSettings } from '@/api/settings/settings.service';
import { CreateSettingsDto } from '@/api/settings/settings.types';
import { useGetCurrentUser } from '@/api/auth/auth.service';
import notificationManagerInstance from '@/hooks/useNotificationManger';
import { useLanguage } from '@/hooks/useLanguage';

export const createColumnState = (colDefs: IColDef[]) => {
  return colDefs?.map((col) => {
    if (!col.field || !col.headerName) return { id: '', label: '' };
    return { id: col.field, label: col.headerName, visible: col.visible };
  });
};

export const useColumnManagement = (colDefs: IColDef[], gridName: GridNames) => {
  const [columns, setColumns] = useState<IColumns[]>([]);
  const [defaultColumns, setDefaultColumns] = useState<IColumns[]>([]);
  const [searchedColumns, setSearchedColumns] = useState<string>('');

  const { data: userInfo } = useGetCurrentUser();
  const { t } = useLanguage();

  const { data: gridColumnState, refetch: refetchSettings } = useGetSettings(
    gridName,
    userInfo?.id,
    {
      enabled: Boolean(userInfo?.id),
    }
  );

  const getColState = useCallback(async () => {
    if (!colDefs.length || !gridName) return;

    const mappedValues = createColumnState(colDefs);
    setDefaultColumns(mappedValues);

    if (!gridColumnState?.value) {
      setColumns(mappedValues);
    } else {
      setColumns(gridColumnState?.value);
    }
  }, [colDefs, gridColumnState, gridName]);

  useEffect(() => {
    getColState();
  }, [getColState]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setColumns(
        columns.map((col) => {
          return { ...col, visible: true };
        })
      );
    } else {
      setColumns(
        columns.map((col) => {
          if (col.id === 'action') {
            return { ...col, visible: true };
          }
          return { ...col, visible: false };
        })
      );
    }
  };

  const handleColumnSelect = (currentColumn: IColumns) => {
    setColumns((prev) => {
      const isColumnExist = prev.map((col) => {
        if (col.id === currentColumn.id) {
          return { ...col, visible: !col.visible };
        }
        return col;
      });
      return isColumnExist;
    });
  };

  const filteredColumns = columns?.filter((column) =>
    column.label.toLowerCase().includes(searchedColumns.toLowerCase())
  );

  const applyColumns = async () => {
    const isAnyNonActionColumnVisible = columns.some(
      (col) => col.id !== 'action' && col.visible === true
    );

    if (!isAnyNonActionColumnVisible) {
      notificationManagerInstance.warning({
        message: t('common.warn'),
        description: t('columnMange.keepVisibleWarn'),
      });
      return false;
    }

    emit('columnManager:changed', {
      gridName: gridName,
      gridState: columns,
    });

    const addGridStatePayload: Partial<CreateSettingsDto> = {
      value: columns,
    };
    try {
      await updateSetting(gridColumnState.id, addGridStatePayload);
      await refetchSettings();
      return true;
    } catch (error) {
      notificationManagerInstance.error({
        message: t('common.error'),
        description: t('columnMange.errorWhileUpdating'),
      });
      return false;
    }
  };

  const resetColumns = () => setColumns(defaultColumns);

  return {
    columns,
    setColumns,
    searchedColumns,
    setSearchedColumns,
    filteredColumns,
    handleSelectAll,
    handleColumnSelect,
    resetColumns,
    applyColumns,
  };
};
