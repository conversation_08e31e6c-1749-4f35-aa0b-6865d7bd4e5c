import { Input } from 'antd';
import { SearchInputIcon } from '@assets/icons/searchInputIcon.tsx';

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
}

export const SearchInput = ({ value, onChange }: SearchInputProps) => (
  <div className="input-wrapper">
    <Input
      className="search-input"
      value={value}
      type="text"
      placeholder="Search"
      onChange={(e) => onChange(e.target.value)}
      prefix={<SearchInputIcon />}
    />
  </div>
);
