import { AnimatePresence, Reorder } from 'framer-motion';
import { Button, Checkbox } from 'antd';
import { IColumns } from '../types';
import { useLanguage } from '@hooks/useLanguage.ts';
import { ColumnManageMenuIcon } from '@assets/icons/columnManageMenuIcon.tsx';

interface ColumnListProps {
  filteredColumns: IColumns[];
  onColumnSelect: (currentColumn: IColumns) => void;
  onReorder: (newOrder: IColumns[]) => void;
  draggingColumnId: string;
  onDragStart: (id: string) => void;
  onDragEnd: () => void;
  isDraggable: boolean;
}

export const ColumnList = ({
  filteredColumns,
  isDraggable,
  onColumnSelect,
  onReorder,
  draggingColumnId,
  onDragStart,
  onDragEnd,
}: ColumnListProps) => {
  const { t } = useLanguage();

  return (
    <Reorder.Group
      axis="y"
      values={filteredColumns}
      onReorder={isDraggable ? onReorder : () => {}}
      className={!isDraggable ? 'non-draggable-group' : ''}
    >
      <AnimatePresence initial={false}>
        {filteredColumns && filteredColumns.length > 0 ? (
          filteredColumns.map((column) => (
            <Reorder.Item
              key={column.id}
              value={column}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              layout
              transition={{
                opacity: { duration: 0.5, ease: 'easeIn' },
                layout: { duration: 0.0, type: 'spring' },
              }}
              className={`reorder-item ${draggingColumnId === column.id ? 'dragging-item' : ''} ${column.id === 'action' && 'hidden'}`}
              onDragStart={isDraggable ? () => onDragStart(column.id) : undefined}
              onDragEnd={isDraggable ? onDragEnd : undefined}
              drag={isDraggable ? true : false}
            >
              <div className="checkbox-label-container">
                <Checkbox
                  id={`${column.id}-tailwind`}
                  checked={column?.visible}
                  onChange={() => onColumnSelect(column)}
                  className="all-checkboxes"
                />
                <label
                  htmlFor={`${column.id}-tailwind`}
                  className="column-label cursor-pointer ml-0 pl-2"
                >
                  {column.label}
                </label>
              </div>
              <Button
                className={`column-menu-button ${isDraggable ? 'cursor-move' : 'cursor-not-allowed'}`}
                icon={<ColumnManageMenuIcon />}
                disabled={!isDraggable}
              />
            </Reorder.Item>
          ))
        ) : (
          <div className="no-columns-message">{t('columnMange.noColumns')}</div>
        )}
      </AnimatePresence>
    </Reorder.Group>
  );
};
