import { useNavigationContext } from '@/hooks/useNavigationContext';
import { Breadcrumb } from 'antd';
import { PageBreadCrumbsComponentProps } from './pageBreadCrumbTypes';

const PageBreadCrumbsComponent: React.FC<PageBreadCrumbsComponentProps> = ({ path }) => {
  const { navigate } = useNavigationContext();
  return (
    <div>
      <Breadcrumb separator=">" className=" pt-1 align-middle flex ps-[50px] text-[15px]">
        {path?.map((breadcrumb) => {
          return (
            <Breadcrumb.Item
              key={breadcrumb.name}
              onClick={() => navigate(breadcrumb.path)}
              className="cursor-pointer"
            >
              <div className="fs-name font-medium">{breadcrumb.name}</div>
            </Breadcrumb.Item>
          );
        })}
      </Breadcrumb>
    </div>
  );
};

export default PageBreadCrumbsComponent;
