import { backArrowIconsvg } from '@/assets';
import { customAlert } from '../../common/customAlert/CustomAlert';
import { useLanguage } from '@/hooks/useLanguage';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { PageHeadingComponentProps } from './pageHeadingTypes';

const PageHeadingComponent: React.FC<PageHeadingComponentProps> = (props) => {
  const {
    title,
    isChildComponent,
    classNameTitle,
    classNameChildren,
    redirectUrl,
    children,
    parentClassName,
    onBackClick,
  } = props;
  const { isBlocked, setIsBlocked } = useNavigationContext();
  const { t } = useLanguage();
  const backButtonHandler = () => {
    if (isBlocked) {
      customAlert.warning({
        title: t('common.alert.areYouSure'),
        message: t('common.alert.preventExist'),
        firstButtonTitle: t('common.leave'),
        secondButtonTitle: t('common.stay'),
        firstButtonFunction: () => {
          customAlert.destroy();
          setIsBlocked(false);
          redirectUrl ? redirectUrl : window.history.back();
        },
        secondButtonFunction: () => customAlert.destroy(),
      });
      return;
    }

    return onBackClick ? onBackClick() : redirectUrl ? redirectUrl : window.history.back();
  };
  return (
    <div className={`flex flex-col sm:flex-row justify-between ${children ? 'w-[98%]' : 'w-full'}`}>
      <div className={parentClassName ? parentClassName : `flex gap-3 pt-5 pl-0`}>
        {' '}
        {isChildComponent && (
          <img
            src={backArrowIconsvg}
            alt="Back"
            className={classNameChildren ? classNameChildren : 'w-[34px] h-[40px] cursor-pointer'}
            onClick={backButtonHandler}
          />
        )}
        <div
          className={
            classNameTitle ? classNameTitle : 'md:text-3xl font-semibold font-primary 3xsm:text-lg'
          }
        >
          {title}
        </div>
      </div>
      {children}
    </div>
  );
};

export default PageHeadingComponent;
