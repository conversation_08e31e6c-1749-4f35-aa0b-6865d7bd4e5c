import React, { memo, useMemo } from 'react';
import { WarningOutlined } from '@ant-design/icons';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { IPermissionCheckerProps } from '@customTypes/PermissionCheckerType';

const DISABLED_STYLE = {
  opacity: 0.5,
  cursor: 'not-allowed',
};

/**
 * A permission wrapper component to control access based on user roles or subscription plans.
 *
 * @component
 * @param {IPermissionCheckerProps} props - The properties for permission checking.
 * @param {string} [props.feature] - The feature to validate for plan-based access.
 * @param {string} [props.resource] - The resource to validate for role-based access.
 * @param {Action} [props.action] - The action to check against the resource.
 * @param {React.ReactNode} props.children - The content to render if permission is granted.
 * @param {'display' | 'popover'} [props.type='display'] - The type of permission handling (display content or show tooltip).
 * @returns {React.ReactNode | null} - The rendered children if permitted, or null if access is denied.
 */

const PermissionChecker: React.FC<IPermissionCheckerProps> = ({
  feature,
  resource,
  action,
  children,
  type = 'display',
}) => {
  // TODO: uncomment this code after integrating plan and role-based access control

  // const currentUser = JSON.parse(localStorage.getItem('user'));

  // const accessController = useMemo(
  //   () =>
  //     new AccessController(
  //       currentUser?.accessRight?.roleBasedActions,
  //       currentUser?.accessRight?.planBasedFeature
  //     ),
  //   [currentUser]
  // );

  const { isPermitted, message } = useMemo(() => {
    if (resource && action) {
      return {
        isPermitted: true,
        // isPermitted: accessController.roleBasedPermissionChecker(currentUser, resource, action),
        message: 'You do not have permission',
      };
    }
    if (feature) {
      return {
        isPermitted: true,
        // isPermitted: accessController.planBasedFeatureChecker(currentUser, feature),
        message: 'Please upgrade your plan',
      };
    }
    return { isPermitted: false, message: '' };
  }, [feature, resource, action]);

  const tooltipContent = useMemo(
    () => (
      <div className="flex items-center gap-2">
        <WarningOutlined /> {message}
      </div>
    ),
    [message]
  );

  if (type === 'popover' && !isPermitted) {
    return (
      <CustomTooltip content={tooltipContent} id="permission-popover" trigger="hover">
        <div style={DISABLED_STYLE}>
          {React.isValidElement(children) &&
            React.cloneElement(children as React.ReactElement<{ disabled?: boolean }>, {
              disabled: true,
            })}
        </div>
      </CustomTooltip>
    );
  }

  return isPermitted ? children : null;
};

export default memo(PermissionChecker);
