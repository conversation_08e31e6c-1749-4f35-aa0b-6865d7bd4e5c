import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';

export const LanguageToggle: React.FC = () => {
  const { currentLanguage, setLanguage } = useLanguage();

  return (
    <div className="flex space-x-2">
      <button
        className={`px-2 py-1 rounded ${currentLanguage === 'EN' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        onClick={() => setLanguage('EN')}
      >
        EN
      </button>
      <button
        className={`px-2 py-1 rounded ${currentLanguage === 'FR' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        onClick={() => setLanguage('FR')}
      >
        FR
      </button>
    </div>
  );
};
