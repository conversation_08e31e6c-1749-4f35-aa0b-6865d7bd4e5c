import { ICellRendererParams } from 'ag-grid-community';
import { useMemo } from 'react';
import CustomAgGrid from '../../../common/agGrid/AgGrid';
import {
  AssignToOutlined,
  DeleteIcon,
  deleteSvg,
  DuplicateCustomerIcon,
  EyeIcon,
  HistoryIcon,
  PrinterIcon,
} from '@/assets';
import RoutesHeader from './routesHeader';
import { GridNames } from '@/types/AppEvents';
import { useLanguage } from '@/hooks/useLanguage';
import { IColDef } from '@/types/AgGridTypes';
import Icon from '@ant-design/icons';
interface ICustomer {
  companyName: string;
  accountNumber: string;
  contactName: string;
  addressLine1: string;
  city: string;
  phone: string;
  email: string;
  fax: string;
  status: boolean;
  category: string;
  dateUpdated: string;
  lastUpdateBy: string;
}

const RoutesComponent = () => {
  const { t } = useLanguage();
  const customerColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'companyName',
        headerName: `${t('dashboard.customer.columns.companyName')}`,
        unSortIcon: true,
        width: 158,
        visible: true,
      },
      {
        field: 'accountNumber',
        headerName: `${t('dashboard.customer.columns.accountNumber')}`,
        unSortIcon: true,
        width: 165,
        visible: true,
      },
      {
        field: 'contactName',
        headerName: `${t('dashboard.customer.columns.contactName')}`,
        unSortIcon: true,
        width: 149,
        visible: true,
      },
      {
        field: 'addressLine1',
        headerName: `${t('dashboard.customer.columns.addressLine1')}`,
        unSortIcon: true,
        width: 136,
        visible: true,
      },
      {
        field: 'city',
        headerName: `${t('dashboard.customer.columns.city')}`,
        unSortIcon: true,
        width: 109,
        visible: true,
      },
      {
        field: 'phone',
        headerName: `${t('dashboard.customer.columns.phone')}`,
        unSortIcon: true,
        width: 132,
        visible: true,
      },
      {
        field: 'email',
        headerName: `${t('dashboard.customer.columns.email')}`,
        unSortIcon: true,
        width: 132,
        visible: true,
      },
      {
        field: 'fax',
        headerName: `${t('dashboard.customer.columns.fax')}`,
        unSortIcon: true,
        width: 132,
        visible: true,
      },
      {
        field: 'status',
        headerName: `${t('dashboard.customer.columns.status')}`,
        unSortIcon: true,
        width: 132,
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.status ? (
            <>
              <span className="h-[10px] w-[10px] rounded-full bg-[seagreen] inline-block mr-1" />{' '}
              Active
            </>
          ) : (
            <>
              <span className="h-[10px] w-[10px] rounded-full bg-red-600 inline-block mr-1" />{' '}
              Inactive
            </>
          );
        },
      },
      {
        field: 'category',
        headerName: `${t('dashboard.customer.columns.category')}`,
        unSortIcon: true,
        width: 132,
        visible: true,
      },
      {
        field: 'dateAdded',
        headerName: `${t('dashboard.customer.columns.dateAdded')}`,
        unSortIcon: true,
        width: 132,
        visible: true,
      },
      {
        field: 'dateUpdated',
        headerName: `${t('dashboard.customer.columns.dateUpdated')}`,
        unSortIcon: true,
        width: 132,
        visible: true,
      },
      {
        field: 'lastUpdateBy',
        headerName: `${t('dashboard.customer.columns.lastUpdateBy')}`,
        unSortIcon: true,
        width: 132,
        visible: true,
      },
      {
        field: 'action',
        headerName: `${t('dashboard.customer.columns.action')}`,
        pinned: 'right',
        width: 110,
        visible: true,
        sortable: false,
        resizable: false,
        cellRenderer: () => {
          return (
            <div className="flex gap-2 h-full items-center">
              <Icon component={HistoryIcon} className="cursor-pointer" />
              <Icon component={EyeIcon} className="cursor-pointer" />
              <Icon component={DeleteIcon} className="cursor-pointer" />
            </div>
          );
        },
      },
    ];
  }, [t]);
  function generateRandomCustomerData(dataArray: ICustomer[], count: number) {
    const randomData = [];

    for (let i = 0; i < count; i++) {
      const randomIndex = Math.floor(Math.random() * dataArray.length);
      const selectedEntry = { ...dataArray[randomIndex] };

      selectedEntry.status = Math.random() < 0.5 ? true : false;

      randomData.push(selectedEntry);
    }

    return randomData;
  }
  const customerRowData = [
    {
      companyName: 'Life - Life Corporation',
      accountNumber: '********',
      contactName: 'Dianne Russell',
      addressLine1: '567 cedar street Mumbai',
      city: 'Austin',
      phone: '+ 49 **********',
      email: '<EMAIL>',
      fax: '+ 49 **********',
      status: true,
      category: 'Home & Office',
      dateAdded: '12/06/2020',
      dateUpdated: '12/06/2020',
      lastUpdateBy: 'Annette Black',
    },
    {
      companyName: 'Yates - Yates Entertainment',
      accountNumber: '********',
      contactName: 'Cody Fisher',
      addressLine1: '890 Birch Cordiform',
      city: 'Naperville',
      phone: '+ 49 **********',
      email: '<EMAIL>',
      fax: '+ 49 **********',
      status: true,
      category: 'Equipment Restaurants',
      dateAdded: '15/08/2017',
      dateUpdated: '12/06/2020',
      lastUpdateBy: 'Wade Warren',
    },
  ];
  const randomCustomer = generateRandomCustomerData(customerRowData, 50);

  const customerContextMenuItems: any = useMemo(() => {
    // wrap the context menu labels in translator function for multi-language support
    return [
      {
        label: 'New customer',
        key: 'Open',
        icon: AssignToOutlined,
        onClick: () => {
          alert('from click');
        },
      },
      {
        label: 'Duplicate customer',
        icon: DuplicateCustomerIcon,
        key: 'assignTo',
      },
      {
        label: 'Print',
        icon: PrinterIcon,
        key: 'unAssign',
      },
      {
        label: <span className="text-red-600">Delete</span>,
        icon: <img src={deleteSvg} />,
        key: 'delete',
      },
    ];
  }, []);

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        <RoutesHeader />
        <main className=" h-screen overflow-x-hidden overflow-y-auto bg-white">
          <div className=" mx-auto pr-6 py-5 h-full flex justify-center items-center ">
            <CustomAgGrid
              rowData={[...customerRowData, ...randomCustomer]}
              columnDefs={customerColDefs}
              isContextMenu
              contextMenuItem={customerContextMenuItems}
              onContextMenu={() => {}}
              gridName={GridNames.routesGrid}
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default RoutesComponent;
