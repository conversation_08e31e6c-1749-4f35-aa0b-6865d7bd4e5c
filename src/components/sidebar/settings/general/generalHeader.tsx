import { Button } from 'antd';

import { useNavigate } from 'react-router-dom';
import { PlusButtonIcon } from '../../../../assets/icons/plusButtonIcon';
import PageHeadingComponent from '../../../specific/pageHeading/PageHeadingComponent';
import { ROUTES } from '@/constant/RoutesConstant';
import ColumnManage from '@/components/specific/columnManage';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { GridNames } from '@/types/AppEvents';

const GeneralHeader = () => {
  const navigate = useNavigate();

  return (
    <>
      <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
        <div className="md:w-1/3 flex flex-col 3xsm:w-full">
          <PageHeadingComponent title={'General'} />
        </div>
        <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
          <SearchFilterComponent searchedValues={() => {}} colDefs={[]} onFilterApply={() => {}} />

          <div className="pt-5">
            <Button
              onClick={() => {
                navigate(ROUTES.CUSTOMER.CUSTOMER_ADD);
              }}
              className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-[#0876A4] text-white font-[500] hover:!bg-[#0876A4] hover:!text-white"
              icon={<PlusButtonIcon />}
            >
              Add customer
            </Button>
          </div>

          <ColumnManage colDefs={[]} gridName={GridNames.generalGrid} />
        </div>
      </div>
    </>
  );
};
export default GeneralHeader;
