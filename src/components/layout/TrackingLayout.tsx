import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { LanguageToggle } from '../specific/LanguageToggle';

export const TrackingLayout: React.FC<React.PropsWithChildren<object>> = ({ children }) => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-gray-900">DeliverEase</h1>
          <nav className="flex items-center space-x-4">
            <Link to="/dashboard" className="text-gray-500 hover:text-gray-700">
              {t('common.dashboard')}
            </Link>
            <Link to="/tracking" className="text-gray-500 hover:text-gray-700">
              {t('common.tracking')}
            </Link>
            <LanguageToggle />
          </nav>
        </div>
      </header>
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">{children}</main>
    </div>
  );
};
