import React from 'react';
import PageHeadingComponent from '../specific/pageHeading/PageHeadingComponent';
import PageBreadCrumbsComponent from '../specific/pageBreadCrumb/PageBreadCrumbComponent';
import { breadCrumbsPathForPages } from '@/constant/BreadCrumbConstant';

export const DashboardLayout: React.FC<React.PropsWithChildren<object>> = () => {
  return (
    <div className="flex h-screen bg-gray-100">
      <div className="container flex-1 flex flex-col overflow-hidden bg-white">
        <div className="flex w-full">
          <div className="md:w-1/3 flex flex-col 3xsm:w-full">
            <PageHeadingComponent title={'Dashboard'} isChildComponent={true} />
            <PageBreadCrumbsComponent path={breadCrumbsPathForPages.customers} />
          </div>
        </div>
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-white">
          <div className="mx-auto px-6 py-5">{/* <AgGrid /> */}</div>
        </main>
      </div>
    </div>
  );
};
