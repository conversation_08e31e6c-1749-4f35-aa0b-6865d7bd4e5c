.sidebar-container-menu-sub {
  background-color: var(--white) !important;
  gap: 20px;
}

.sidebar-container-menu-sub > .sidebar-container-menu-item-selected {
  border-right: 3px solid var(--primary-600);
  border-radius: 0;
}

.sidebar-container-menu {
  border-inline-end: 1px solid transparent !important;
}

.sidebar-container-menu > .sidebar-container-menu-submenu > div {
  gap: 5px;
}
.sidebar-container-menu-collapsed
  > .sidebar-container-menu-collapsed-submenu
  > .sidebar-container-menu-collapsed-submenu-title
  > .sidebar-container-menu-collapsed-title-content {
  @apply hidden;
}
.sidebar-container-menu
  > .sidebar-container-menu-submenu-selected
  > .sidebar-container-menu-submenu-title
  > .sidebar-container-menu-title-content {
  @apply !text-[#0876A4];
}
.sidebar-container-menu
  > .sidebar-container-menu-submenu-selected
  > ul
  > .sidebar-container-menu-item-selected
  > .sidebar-container-menu-title-content {
  @apply !text-[#0876A4];
}
