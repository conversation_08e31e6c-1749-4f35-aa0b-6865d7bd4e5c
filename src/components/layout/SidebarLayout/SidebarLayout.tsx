import React, { ReactNode, useEffect, useMemo, useState } from 'react';
import Icon from '@ant-design/icons';
import { Avatar, Menu } from 'antd';
import './SidebarLayout.css';
import { URoles } from '@/types/enums/Roles';
import { useLanguage } from '@/hooks/useLanguage';
import { RouteGuard } from '@/lib/routeGuard/RouteGuard';
import { ROUTE_ROLE_RESTRICTION } from '@/constant/RouteRestrictionsConstant';
import {
  BillingSidebarIcon,
  CustomerIcon,
  CustomerSelectedOutlinedIcon,
  LeftOutlinedIcon,
  LocationOutlinedIcon,
  LocationSelectedOutlinedIcon,
  Logo,
  LogoIcon,
  LogoutIcon,
  PricesOutlinedIcon,
  PricesSelectedOutlinedIcon,
  RightOutlined,
  SettingIcon,
  SettingSelectedIcon,
  TruckOutlinedIcon,
  TruckSelectedOutlinedIcon,
} from '@/assets';
import { ROUTES } from '@/constant/RoutesConstant';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { getUserInfoFromStorage } from '@/lib/helper/userHelper';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { logout, useGetCurrentUser } from '@/api/auth/auth.service';

export interface SidebarLayoutProps {
  key: string;
  label: React.ReactNode;
  url?: string;
  children?: SidebarLayoutProps[];
  icon?: React.ReactNode;
  allowedroles?: URoles[];
}
interface SidebarLayoutChild {
  children?: ReactNode;
}

const SidebarLayout: React.FC<SidebarLayoutChild> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);

  const { navigate } = useNavigationContext();

  const currentPath = window.location.pathname;
  const parentKey = useMemo(() => currentPath.split('/')[1], [currentPath]);
  const { t } = useLanguage();
  const LeftOutlinedSVG = () => <Icon component={LeftOutlinedIcon} />;
  const RightOutlinedSVG = () => <Icon component={RightOutlined} />;
  const { data: userInfo } = useGetCurrentUser();

  const items: SidebarLayoutProps[] = [
    {
      key: 'customer',
      icon: parentKey === 'customer' ? <CustomerSelectedOutlinedIcon /> : <CustomerIcon />,
      label: `${t('sidebar.customer')}`,
      children: [
        {
          key: ROUTES.CUSTOMER.CUSTOMER_CUSTOMERS,
          label: (
            <a
              href={ROUTES.CUSTOMER.CUSTOMER_CUSTOMERS}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.CUSTOMER.CUSTOMER_CUSTOMERS);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.customers')}
            </a>
          ),
          url: ROUTES.CUSTOMER.CUSTOMER_CUSTOMERS,
          allowedroles: ROUTE_ROLE_RESTRICTION.CUSTOMER_CUSTOMERS,
        },
        {
          key: ROUTES.CUSTOMER.CUSTOMER_PARTNER,
          label: (
            <a
              href={ROUTES.CUSTOMER.CUSTOMER_PARTNER}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.CUSTOMER.CUSTOMER_PARTNER);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.partner')}
            </a>
          ),
          url: ROUTES.CUSTOMER.CUSTOMER_PARTNER,
          allowedroles: ROUTE_ROLE_RESTRICTION.CUSTOMER_PARTNER,
        },
        {
          key: ROUTES.CUSTOMER.CUSTOMER_BILLING,
          label: (
            <a
              href={ROUTES.CUSTOMER.CUSTOMER_BILLING}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.CUSTOMER.CUSTOMER_BILLING);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.billing')}
            </a>
          ),
          url: ROUTES.CUSTOMER.CUSTOMER_BILLING,
          allowedroles: ROUTE_ROLE_RESTRICTION.CUSTOMER_BILLING,
        },
      ],
    },
    {
      key: 'logistic',
      icon: parentKey === 'logistic' ? <TruckSelectedOutlinedIcon /> : <TruckOutlinedIcon />,
      label: `${t('sidebar.logistic')}`,
      children: [
        {
          key: ROUTES.LOGISTIC.LOGISTICS_ORDERS,
          label: (
            <a
              href={ROUTES.LOGISTIC.LOGISTICS_ORDERS}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.LOGISTIC.LOGISTICS_ORDERS);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.orders')}
            </a>
          ),
          url: ROUTES.LOGISTIC.LOGISTICS_ORDERS,
          allowedroles: ROUTE_ROLE_RESTRICTION.LOGISTICS_ORDERS,
        },

        {
          key: ROUTES.LOGISTIC.LOGISTICS_DISPATCHER,
          label: (
            <a
              href={ROUTES.LOGISTIC.LOGISTICS_DISPATCHER}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.LOGISTIC.LOGISTICS_DISPATCHER);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.dispatcher')}
            </a>
          ),
          url: ROUTES.LOGISTIC.LOGISTICS_DISPATCHER,
          allowedroles: ROUTE_ROLE_RESTRICTION.LOGISTICS_DISPATCHER,
        },

        {
          key: ROUTES.LOGISTIC.LOGISTICS_VEHICLE,
          label: (
            <a
              href={ROUTES.LOGISTIC.LOGISTICS_VEHICLE}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.LOGISTIC.LOGISTICS_VEHICLE);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.vehicle')}
            </a>
          ),
          url: ROUTES.LOGISTIC.LOGISTICS_VEHICLE,
          allowedroles: ROUTE_ROLE_RESTRICTION.LOGISTICS_VEHICLE,
        },
      ],
    },
    {
      key: 'location',
      label: `${t('sidebar.location')}`,
      icon: parentKey === 'location' ? <LocationSelectedOutlinedIcon /> : <LocationOutlinedIcon />,
      children: [
        {
          key: ROUTES.LOCATION.LOCATION_ADDRESS,
          label: (
            <a
              href={ROUTES.LOCATION.LOCATION_ADDRESS}
              rel="noopener noreferrer"
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.LOCATION.LOCATION_ADDRESS);
              }}
            >
              {t('sidebar.address')}
            </a>
          ),
          url: ROUTES.LOCATION.LOCATION_ADDRESS,
          allowedroles: ROUTE_ROLE_RESTRICTION.LOCATION_ADDRESS,
        },
        {
          key: ROUTES.LOCATION.LOCATION_ZONE,
          label: (
            <a
              href={ROUTES.LOCATION.LOCATION_ZONE}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.LOCATION.LOCATION_ZONE);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.zone')}
            </a>
          ),
          url: ROUTES.LOCATION.LOCATION_ZONE,
          allowedroles: ROUTE_ROLE_RESTRICTION.LOCATION_ZONE,
        },
        {
          key: '/location/lookup-table',
          label: (
            <a
              href="/location/lookup-table"
              onClick={(e) => {
                e.preventDefault();
                navigate('/location/lookup-table');
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.zoneLookupTable')}
            </a>
          ),
          url: '/location/lookup-table',
          allowedroles: ROUTE_ROLE_RESTRICTION.LOCATION_ZONE,
        },
        {
          key: ROUTES.LOCATION.LOCATION_ROUTES,
          label: (
            <a
              href={ROUTES.LOCATION.LOCATION_ROUTES}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.LOCATION.LOCATION_ROUTES);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.routes')}
            </a>
          ),
          url: ROUTES.LOCATION.LOCATION_ROUTES,
          allowedroles: ROUTE_ROLE_RESTRICTION.LOCATION_ROUTES,
        },
      ],
    },
    {
      key: 'billing',
      icon: <BillingSidebarIcon bool={parentKey === 'billing'} />,
      label: `${t('sidebar.billing')}`,
      children: [
        {
          key: ROUTES.BILLING.BILLING_INVOICES_GRID,
          label: (
            <a
              href={ROUTES.BILLING.BILLING_INVOICES_GRID}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.BILLING.BILLING_INVOICES_GRID);
              }}
              rel="noopener noreferrer"
            >
              {/* {t('sidebar.billing')} */}
              Inovices
            </a>
          ),
          url: ROUTES.BILLING.BILLING_INVOICES_GRID,
          allowedroles: ROUTE_ROLE_RESTRICTION.BILLING_INVOICE,
        },
        {
          key: ROUTES.BILLING.BILLING_PAYMENTS_GRID,
          label: (
            <a
              href={ROUTES.BILLING.BILLING_PAYMENTS_GRID}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.BILLING.BILLING_PAYMENTS_GRID);
              }}
              rel="noopener noreferrer"
            >
              {/* {t('sidebar.billing')} */}
              Payments
            </a>
          ),
          url: ROUTES.BILLING.BILLING_PAYMENTS_GRID,
          allowedroles: ROUTE_ROLE_RESTRICTION.BILLING_INVOICE,
        },
      ],
    },
    {
      key: 'prices',
      label: `${t('sidebar.prices')}`,
      icon: parentKey === 'prices' ? <PricesSelectedOutlinedIcon /> : <PricesOutlinedIcon />,
      children: [
        {
          key: ROUTES.PRICES.PRICES_PRICE_SETS,
          label: (
            <a
              href={ROUTES.PRICES.PRICES_PRICE_SETS}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.PRICES.PRICES_PRICE_SETS);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.pricesSets')}
            </a>
          ),
          url: ROUTES.PRICES.PRICES_PRICE_SETS,
          allowedroles: ROUTE_ROLE_RESTRICTION.PRICES_PRICE_SETS,
        },
        {
          key: ROUTES.PRICES.PRICES_PRICE_MODIFIERS,
          label: (
            <a
              href={ROUTES.PRICES.PRICES_PRICE_MODIFIERS}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.PRICES.PRICES_PRICE_MODIFIERS);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.pricesModifiers')}
            </a>
          ),
          url: ROUTES.PRICES.PRICES_PRICE_MODIFIERS,
          allowedroles: ROUTE_ROLE_RESTRICTION.PRICES_PRICE_MODIFIERS,
        },
      ],
    },
    {
      key: 'settings',
      label: `${t('sidebar.settings')}`,
      icon: parentKey === 'settings' ? <SettingSelectedIcon /> : <SettingIcon />,
      children: [
        {
          key: ROUTES.SETTINGS.SETTINGS_GENERAL,
          label: (
            <a
              href={ROUTES.SETTINGS.SETTINGS_GENERAL}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.SETTINGS.SETTINGS_GENERAL);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.general')}
            </a>
          ),
          url: ROUTES.SETTINGS.SETTINGS_GENERAL,
          allowedroles: ROUTE_ROLE_RESTRICTION.SETTINGS_GENERAL,
        },
        {
          key: ROUTES.SETTINGS.SETTINGS_PARTNERS,
          label: (
            <a
              href={ROUTES.SETTINGS.SETTINGS_PARTNERS}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.SETTINGS.SETTINGS_PARTNERS);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.partners')}
            </a>
          ),
          url: ROUTES.SETTINGS.SETTINGS_PARTNERS,
          allowedroles: ROUTE_ROLE_RESTRICTION.SETTINGS_PARTNERS,
        },
        {
          key: ROUTES.SETTINGS.SETTINGS_TEMPLATE,
          label: (
            <a
              href={ROUTES.SETTINGS.SETTINGS_TEMPLATE}
              onClick={(e) => {
                e.preventDefault();
                navigate(ROUTES.SETTINGS.SETTINGS_TEMPLATE);
              }}
              rel="noopener noreferrer"
            >
              {t('sidebar.templates')}
            </a>
          ),
          url: ROUTES.SETTINGS.SETTINGS_TEMPLATE,
          allowedroles: ROUTE_ROLE_RESTRICTION.SETTINGS_TEMPLATES,
        },
      ],
    },
  ];

  const currentUser = getUserInfoFromStorage();

  const filteredSidebarItems = useMemo(
    () => new RouteGuard().filterSidebarItems(items, (currentUser?.role as URoles) || 'Tenant'),
    [parentKey]
  );

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };
  const defaultActiveKey = () => {
    const fallbackKeyMap: Record<string, string[]> = {
      [ROUTES.CUSTOMER.CUSTOMER_CUSTOMERS]: [ROUTES.CUSTOMER.CUSTOMER_ADD, '/customer/edit/'],
      [ROUTES.CUSTOMER.CUSTOMER_PARTNER]: [],
      [ROUTES.CUSTOMER.CUSTOMER_BILLING]: [],
      [ROUTES.LOGISTIC.LOGISTICS_ORDERS]: [],
      [ROUTES.LOGISTIC.LOGISTICS_DISPATCHER]: [],
      [ROUTES.LOGISTIC.LOGISTICS_VEHICLE]: [],
      [ROUTES.LOCATION.LOCATION_ZONE_LOOKUP_TABLE]: [
        ROUTES.LOCATION.LOCATION_ZONE_LOOKUP_TABLE_ADD,

        ROUTES.LOCATION.LOCATION_ZONE_LOOKUP_TABLE_EDIT,
      ],
      [ROUTES.PRICES.PRICES_PRICE_MODIFIERS]: [],
      [ROUTES.BILLING.BILLING_INVOICES_GRID]: [ROUTES.BILLING.BILLING_CREATE_INVOICE],
    };

    let activeChildKey: string | undefined;
    items.forEach((item) => {
      if (currentPath.startsWith(`/${item.key}`)) {
        const childMatch = item.children?.find((child) => currentPath.startsWith(child.key));

        if (childMatch) {
          activeChildKey = childMatch.key;
        } else {
          Object.keys(fallbackKeyMap).forEach((fallbackKey) => {
            if (fallbackKeyMap[fallbackKey].some((path) => currentPath.startsWith(path))) {
              activeChildKey = fallbackKey;
            }
          });
        }
      }
    });

    return activeChildKey ? [activeChildKey] : [];
  };
  const transportOrders = useMemo(() => {
    return [
      {
        dateSubmitted: '2024-03-10',
        trackingNumber: 'TRK100001',
        customer: 'Alice Johnson',
        collectionCompanyName: 'Swift Logistics',
        deliveryCompanyName: 'Rapid Freight',
        serviceLevel: 'Express',
        assignee: 'Driver X',
        status: true,
        collectionTime: '2024-03-10 08:00 AM',
        deliveryTime: '2024-03-11 12:00 PM',
      },
      {
        dateSubmitted: '2024-03-11',
        trackingNumber: 'TRK100002',
        customer: 'Bob Smith',
        collectionCompanyName: 'Metro Haulage',
        deliveryCompanyName: 'FastWay Carriers',
        serviceLevel: 'Standard',
        assignee: 'Driver Y',
        status: true,
        collectionTime: '2024-03-11 09:30 AM',
        deliveryTime: '2024-03-12 01:30 PM',
      },
      {
        dateSubmitted: '2024-03-12',
        trackingNumber: 'TRK100003',
        customer: 'Charlie Brown',
        collectionCompanyName: 'Express Movers',
        deliveryCompanyName: 'GreenLine Transport',
        serviceLevel: 'Premium',
        assignee: 'Driver Z',
        status: true,
        collectionTime: '2024-03-12 10:00 AM',
        deliveryTime: '2024-03-13 03:00 PM',
      },
      {
        dateSubmitted: '2024-03-13',
        trackingNumber: 'TRK100004',
        customer: 'David Williams',
        collectionCompanyName: 'Cargo Fleet',
        deliveryCompanyName: 'Skyline Express',
        serviceLevel: 'Economy',
        assignee: 'Driver A',
        status: true,
        collectionTime: '2024-03-13 07:45 AM',
        deliveryTime: '2024-03-14 05:00 PM',
      },
      {
        dateSubmitted: '2024-03-14',
        trackingNumber: 'TRK100005',
        customer: 'Eve Adams',
        collectionCompanyName: 'National Freight',
        deliveryCompanyName: 'BlueLine Logistics',
        serviceLevel: 'Express',
        assignee: 'Driver B',
        status: true,
        collectionTime: '2024-03-14 08:15 AM',
        deliveryTime: '2024-03-15 11:45 AM',
      },
      {
        dateSubmitted: '2024-03-15',
        trackingNumber: 'TRK100006',
        customer: 'Franklin Scott',
        collectionCompanyName: 'Urban Cargo',
        deliveryCompanyName: 'Red Star Shipping',
        serviceLevel: 'Standard',
        assignee: 'Driver C',
        status: true,
        collectionTime: '2024-03-15 09:50 AM',
        deliveryTime: '2024-03-16 02:30 PM',
      },
      {
        dateSubmitted: '2024-03-16',
        trackingNumber: 'TRK100007',
        customer: 'Grace Lee',
        collectionCompanyName: 'Horizon Freight',
        deliveryCompanyName: 'SwiftHaul Logistics',
        serviceLevel: 'Premium',
        assignee: 'Driver D',
        status: true,
        collectionTime: '2024-03-16 11:00 AM',
        deliveryTime: '2024-03-17 04:15 PM',
      },
      {
        dateSubmitted: '2024-03-17',
        trackingNumber: 'TRK100008',
        customer: 'Henry Martin',
        collectionCompanyName: 'Peak Transport',
        deliveryCompanyName: 'Global Freight',
        serviceLevel: 'Economy',
        assignee: 'Driver E',
        status: true,
        collectionTime: '2024-03-17 07:00 AM',
        deliveryTime: '2024-03-18 06:00 PM',
      },
      {
        dateSubmitted: '2024-03-18',
        trackingNumber: 'TRK100009',
        customer: 'Isabel Carter',
        collectionCompanyName: 'Velocity Haulage',
        deliveryCompanyName: 'HyperShip Couriers',
        serviceLevel: 'Express',
        assignee: 'Driver F',
        status: true,
        collectionTime: '2024-03-18 08:45 AM',
        deliveryTime: '2024-03-19 12:30 PM',
      },
      {
        dateSubmitted: '2024-03-19',
        trackingNumber: 'TRK100010',
        customer: 'Jack Reynolds',
        collectionCompanyName: 'Titan Logistics',
        deliveryCompanyName: 'NextGen Freight',
        serviceLevel: 'Standard',
        assignee: 'Driver G',
        status: true,
        collectionTime: '2024-03-19 10:30 AM',
        deliveryTime: '2024-03-20 03:45 PM',
      },
    ];
  }, []);

  useEffect(() => {
    localStorage.setItem('ordersGrid', JSON.stringify(transportOrders));
  }, [transportOrders]);

  const getAvatarText = (companyName?: string): string => {
    if (!companyName) return 'NA';

    const words = companyName.split(' ').filter(Boolean);

    if (words.length === 1) return words[0].charAt(0).toUpperCase();

    return `${words[0].charAt(0).toUpperCase()}${words[1].charAt(0).toUpperCase()}`;
  };

  const logoutHandler = () => {
    customAlert.warning({
      title: t('auth.logoutConfirmation'),
      message: t('auth.redirectTxt'),
      firstButtonFunction: async () => {
        await logout();
        customAlert.destroy();
        window.location.replace(ROUTES.COMMON.LOGIN);
      },
      secondButtonFunction: () => customAlert.destroy(),
      firstButtonTitle: t('common.logout'),
      secondButtonTitle: t('common.cancel'),
    });
  };

  return (
    <div className="flex relative gap-7 z-20">
      <div
        className={`bg-white flex shadow-lg shadow-[#00000026]  h-screen transition-all duration-300 ease  ${collapsed ? 'w-[80px]' : 'w-[14.6rem]'}`}
      >
        <div className="flex flex-col">
          <div className="bg-primary-25  flex justify-center">
            <img src={collapsed ? LogoIcon : Logo} alt="logo" className="w-3/4 h-20" />
          </div>
          <div className={collapsed ? `bg-white` : `bg-white pe-1`}>
            <div className="sidebar-container">
              <Menu
                prefixCls={
                  collapsed ? 'sidebar-container-menu-collapsed' : 'sidebar-container-menu'
                }
                mode="inline"
                inlineCollapsed={collapsed}
                items={filteredSidebarItems}
                className="border-none border-color-white 3xl:h-screen-85 h-[80vh] overflow-auto flex flex-col "
                selectedKeys={defaultActiveKey()}
                defaultOpenKeys={[parentKey]}
              />
            </div>
          </div>
          <div className="bg-primary-25 border-t-2 border-grey-300 align-middle flex justify-center items-center flex-grow">
            {collapsed ? (
              <div className="w-full flex justify-center p-2">
                <Avatar className="text-[#0876A4] bg-primary-50">
                  <div title={userInfo?.companyName}>{getAvatarText(userInfo?.companyName)}</div>
                </Avatar>
              </div>
            ) : (
              <div className="flex w-full ">
                <div className="p-2  w-1/5">
                  <Avatar className="text-[#0876A4] bg-primary-50">
                    <div title={userInfo?.companyName}>{getAvatarText(userInfo?.companyName)}</div>
                  </Avatar>
                </div>
                <div className="flex flex-col w-3/5">
                  <div className="text-md font-semibold text-ellipsis">{userInfo?.companyName}</div>

                  <div
                    className="text-sm w-full max-w-32 text-ellipsis overflow-hidden whitespace-nowrap"
                    title={userInfo?.email}
                  >
                    {userInfo?.email}
                  </div>
                </div>
                <div className="w-1/5 pt-3 cursor-pointer" onClick={logoutHandler}>
                  <img src={LogoutIcon} title={t('common.logout')} />
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="drop-shadow-collapseButton">
          <button
            type="button"
            onClick={toggleCollapsed}
            className={`sm:flex md:flex justify-center items-center absolute text-black bg-white top-[45%] w-[16px] h-[85px]  transition-all duration-300 ease hover:!bg-white hover:!text-black p-0 ${collapsed ? 'left-[95%]' : 'left-[99%] top-[45%]'} clip-path-collapse-rectangle`}
          >
            {collapsed ? <RightOutlinedSVG /> : <LeftOutlinedSVG />}
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-auto ">{children}</div>
    </div>
  );
};

export default SidebarLayout;
