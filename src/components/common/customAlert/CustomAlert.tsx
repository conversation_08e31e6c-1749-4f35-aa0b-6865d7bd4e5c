import { ReactNode } from 'react';
import { <PERSON>ton, ButtonProps, Divider, Modal, ModalProps } from 'antd';
import './CustomAlert.css';
import { IAlertPopupConfig, IAlertType } from '@customTypes/AlertPopupTypes';
import { alertPopupIcons } from '@/constant/AlertPopupConstant';

export type ModalType = 'success' | 'error' | 'warning' | 'info';
const modalStyle = {
  mask: {
    backdropFilter: 'blur(2px)',
  },
};

export type ModalConfigsType = Record<IAlertType, IAlertPopupConfig>;
export interface CustomModalProps extends Omit<ModalProps, 'onOk' | 'onCancel'> {
  type?: ModalType;
  content?: ReactNode;
  footer?: ReactNode | null;
  onOk?: () => void;
  onCancel?: () => void;
  okText?: string;
  cancelText?: string;
  okButtonProps?: ButtonProps;
  cancelButtonProps?: ButtonProps;
}
class CustomAlertPopup {
  private readonly config: ModalConfigsType;

  constructor() {
    this.config = alertPopupIcons;
  }

  destroy() {
    Modal.destroyAll();
  }

  /**
   * Opens a modal dialog with customizable properties based on the alert type.
   *
   * @param {IAlertType} type - The type of alert to be displayed (e.g. 'success', 'error', 'warning').
   * @returns {(config: IAlertPopupConfig): void} A function that takes an IAlertPopupConfig object
   * and displays a modal with specified configuration.
   *
   * The returned function accepts configuration options for the modal content such as icon, title,
   * message, button titles, button functions, and button classes. These options are used to customize
   * the appearance and behavior of the modal dialog.
   * If the returned function is called with no arguments, it will open a modal with default configuration.
   */
  private openModal(type: IAlertType) {
    return ({
      icon = this.config[type].icon,
      title = this.config[type].title,
      message = this.config[type].message,
      firstButtonTitle = this.config[type].firstButtonTitle,
      secondButtonTitle = this.config[type].secondButtonTitle,
      firstButtonLoading = this.config[type].firstButtonLoading,
      secondButtonLoading = this.config[type].secondButtonLoading,
      firstButtonFunction = this.config[type].firstButtonFunction,
      secondButtonFunction = this.config[type].secondButtonFunction,
      firstButtonClass = this.config[type].firstButtonClass,
      secondButtonClass = this.config[type].secondButtonClass,
    }: IAlertPopupConfig) => {
      Modal[type]({
        icon: null,
        centered: true,
        styles: modalStyle,
        footer: false,
        open: false,
        onClose: this.destroy,
        className: 'custom-alert-modal',
        content: (
          <div className="modal-container">
            <div className="modal-content">
              <div className="modal-main-content">
                {icon}
                <h3 className="modal-title">{title}</h3>
                <p className="modal-message">{message}</p>
              </div>
              <Divider className="modal-divider" />
              <div className="modal-footer-content">
                <Button
                  className={firstButtonClass}
                  onClick={firstButtonFunction}
                  loading={firstButtonLoading}
                >
                  {firstButtonTitle}
                </Button>
                <Button
                  className={secondButtonClass}
                  onClick={secondButtonFunction}
                  loading={secondButtonLoading}
                >
                  {secondButtonTitle}
                </Button>
              </div>
            </div>
          </div>
        ),
      });
    };
  }
  public customModal = ({
    type = 'info',
    content,
    footer = null,
    width = 520,
    centered = true,
    closable = true,
    maskClosable = true,
    keyboard = true,
    onOk,
    onCancel = this.destroy,
    okText,
    cancelText,
    okButtonProps,
    cancelButtonProps,

    ...otherProps
  }: CustomModalProps): void => {
    Modal[type]({
      content,
      footer,
      width,
      centered,
      closable,
      maskClosable,
      keyboard,
      onOk,
      onCancel,
      okText,
      cancelText,
      okButtonProps,
      cancelButtonProps,
      ...otherProps,
    });
  };
  public success = this.openModal('success');
  public error = this.openModal('error');
  public warning = this.openModal('warning');
}

export const customAlert = new CustomAlertPopup();
