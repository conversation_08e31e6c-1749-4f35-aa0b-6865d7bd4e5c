.custom-alert-modal > div > .ant-modal-content {
  @apply !p-0;
}
.modal-container {
  @apply w-full;
}
.modal-content {
  @apply flex flex-col;
}
.modal-main-content {
  @apply flex flex-col items-center w-full p-3 gap-2;
}
.modal-icon {
  @apply w-[56px] h-[56px];
}
.modal-footer-content {
  @apply flex w-full justify-end bg-[#FCFCFD];
  @apply gap-3 p-3;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}
.modal-divider {
  @apply !m-0;
}
.modal-title {
  @apply text-[16px] font-[600] text-center max-w-[350px];
}
.primary-button-class {
  @apply bg-white text-black hover:!bg-white hover:!text-black  hover:!border-grey-300;
}
.second-button-class {
  @apply bg-[#0876A4] text-white hover:!bg-[#0876A4] hover:!text-white hover:!border-none border-none;
}
.primary-button-error {
  @apply bg-white text-[#F04438] hover:!bg-white hover:!text-[#F04438] border-[#F04438] hover:!border-[#F04438];
}
.second-button-error {
  @apply bg-[#F04438] text-white hover:!bg-[#F04438] hover:!text-white hover:!border-none border-none;
}
.modal-message {
  @apply text-[14px] font-[400] text-center text-[#465C65];
}
.ant-modal-mask {
  background-color: #0876a40a !important;
}
