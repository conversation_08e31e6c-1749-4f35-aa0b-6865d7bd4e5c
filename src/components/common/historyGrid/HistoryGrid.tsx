import React, { useMemo } from 'react';
import { IColDef } from '@/types/AgGridTypes';
import CustomAgGrid from '../agGrid/AgGrid';
import { IHistoryGridProps } from './historyGridTypes';
import { generateMockHistoryData } from './mockHistoryData';
import { useLanguage } from '@/hooks/useLanguage';
import { GridIdConstant } from '@/constant/GridIdConstant';

/**
 * A reusable history grid component that displays change history for a given entity
 *
 * @param props - The component props
 * @returns A React component
 */
const HistoryGrid: React.FC<IHistoryGridProps> = (props) => {
  const { entityKey, customColumnDefs, className, gridId, loading } = props;

  const { t } = useLanguage();

  // Generate mock history data based on the entity key
  const historyData = useMemo(() => generateMockHistoryData(entityKey), [entityKey]);

  // Default column definitions for the history grid
  const defaultColumnDefs = useMemo<IColDef[]>(
    () => [
      {
        field: 'property',
        headerName: t('historyGrid.property'),
        sortable: true,
        unSortIcon: true,
        minWidth: 150,
        flex: 1,
        visible: true,
      },
      {
        field: 'oldValue',
        headerName: t('historyGrid.oldValue'),
        sortable: true,
        unSortIcon: true,
        minWidth: 150,
        flex: 1,
        visible: true,
      },
      {
        field: 'newValue',
        headerName: t('historyGrid.newValue'),
        sortable: true,
        unSortIcon: true,
        minWidth: 150,
        flex: 1,
        visible: true,
      },
      {
        field: 'dateTime',
        headerName: t('historyGrid.dateTime'),
        sortable: true,
        unSortIcon: true,
        minWidth: 180,
        flex: 1,
        visible: true,
      },
      {
        field: 'modifiedBy',
        headerName: t('historyGrid.modifiedBy'),
        sortable: true,
        unSortIcon: true,
        minWidth: 150,
        flex: 1,
        visible: true,
      },
    ],
    [t]
  );

  // Use custom column definitions if provided, otherwise use default
  const columnDefs = customColumnDefs || defaultColumnDefs;

  // Default column properties
  const defaultColDef = useMemo(
    () => ({
      resizable: true,
      sortable: true,
    }),
    []
  );

  return (
    <div className={`${className || ''}`}>
      <CustomAgGrid
        rowData={historyData}
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        gridId={gridId || GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
        loading={loading}
        className="!h-[82vh]"
      />
    </div>
  );
};

export default HistoryGrid;
