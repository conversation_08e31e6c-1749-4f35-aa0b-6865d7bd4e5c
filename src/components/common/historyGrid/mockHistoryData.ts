import { IHistoryRecord } from './historyGridTypes';

/**
 * Generates mock history data for a given entity key
 * @param entityKey - The key of the entity to generate history for
 * @returns An array of history records
 */
export const generateMockHistoryData = (entityKey: string): IHistoryRecord[] => {
  // Common history records that could apply to any entity
  const commonRecords: IHistoryRecord[] = [
    {
      id: '1',
      property: 'Status',
      oldValue: 'Draft',
      newValue: 'Active',
      dateTime: '2023-05-03 08:14:01',
      modifiedBy: 'User 1',
    },
    {
      id: '2',
      property: 'Notes',
      oldValue: 'Initial notes',
      newValue: 'Updated notes with additional information',
      dateTime: '2023-05-02 07:10:15',
      modifiedBy: 'User 1',
    },
    {
      id: '3',
      property: 'Name',
      oldValue: 'Original Name',
      newValue: 'Updated Name',
      dateTime: '2023-04-28 14:22:33',
      modifiedBy: 'User 2',
    },
  ];

  // Entity-specific records based on the entityKey
  const entitySpecificRecords: Record<string, IHistoryRecord[]> = {
    order: [
      {
        id: '4',
        property: 'COD amount',
        oldValue: '$50.00',
        newValue: '$100.00',
        dateTime: '2020-05-03 08:14:01',
        modifiedBy: 'User 1',
      },
      {
        id: '5',
        property: 'Signature',
        oldValue: 'NA',
        newValue: 'NA',
        dateTime: '2020-05-02 07:10:15',
        modifiedBy: 'User 1',
      },
      {
        id: '6',
        property: 'Delivery Address',
        oldValue: '123 Main St',
        newValue: '456 Oak Ave',
        dateTime: '2020-04-30 15:45:22',
        modifiedBy: 'User 3',
      },
    ],
    customer: [
      {
        id: '4',
        property: 'Email',
        oldValue: '<EMAIL>',
        newValue: '<EMAIL>',
        dateTime: '2023-05-01 09:30:45',
        modifiedBy: 'User 2',
      },
      {
        id: '5',
        property: 'Phone',
        oldValue: '+****************',
        newValue: '+****************',
        dateTime: '2023-04-29 11:20:18',
        modifiedBy: 'User 1',
      },
    ],
    vehicle: [
      {
        id: '4',
        property: 'License Plate',
        oldValue: 'ABC123',
        newValue: 'XYZ789',
        dateTime: '2023-05-02 16:40:12',
        modifiedBy: 'User 3',
      },
      {
        id: '5',
        property: 'Maintenance Status',
        oldValue: 'Due',
        newValue: 'Completed',
        dateTime: '2023-05-01 10:15:30',
        modifiedBy: 'User 2',
      },
    ],
    zone: [
      {
        id: '4',
        property: 'Postal Codes',
        oldValue: '10001, 10002',
        newValue: '10001, 10002, 10003',
        dateTime: '2023-04-30 13:25:55',
        modifiedBy: 'User 1',
      },
      {
        id: '5',
        property: 'Zone Type',
        oldValue: 'Urban',
        newValue: 'Suburban',
        dateTime: '2023-04-28 09:50:40',
        modifiedBy: 'User 3',
      },
    ],
  };

  // Combine common records with entity-specific records if they exist
  const specificRecords = entitySpecificRecords[entityKey] || [];
  return [...specificRecords, ...commonRecords];
};
