import { memo } from 'react';
import { Popover, PopoverProps } from 'antd';
import './customTooltip.css';

/**
 * A custom tooltip component that wraps Ant Design's `Popover` component
 * with additional styling and configuration options.
 *
 * @param {PopoverProps} props - Properties passed to the Popover, including content and configuration.
 */

const CustomTooltip: React.FC<PopoverProps> = (props) => {
  const { children } = props;

  return (
    <Popover {...props} prefixCls="custom-popover">
      {children}
    </Popover>
  );
};

export default memo(CustomTooltip);
