import { CustomTooltipProps } from 'ag-grid-react';

/**
 * A custom tooltip component for with custom styling.
 *
 * @param {CustomTooltipProps} props - Properties for the tooltip, including the `value` to display.
 */

const GridTooltip: React.FC<CustomTooltipProps> = (props) => {
  const { value } = props;

  return <div className="p-2 rounded-md bg-primary-500 text-white shadow-tooltip">{value}</div>;
};

export default GridTooltip;
