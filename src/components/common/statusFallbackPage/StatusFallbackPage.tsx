import { Button, Result } from 'antd';
import { ResultProps, ResultStatusType } from 'antd/es/result';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { ROUTES } from '@/constant/RoutesConstant';

interface IStatusFallbackPageProps {
  status: ResultStatusType;
}

const StatusFallbackPage: React.FC<IStatusFallbackPageProps> = (props) => {
  const { status } = props;
  const { t } = useLanguage();
  const navigate = useNavigate();

  const BackHomeButton = useMemo(
    () => (
      <Button
        type="primary"
        onClick={() => navigate(ROUTES.COMMON.DEFAULT)}
        className="bg-primary-600"
      >
        {t('statusFallbackPage.backHome')}
      </Button>
    ),
    [navigate, t]
  );

  const content: Record<string, ResultProps> = useMemo(() => {
    return {
      403: {
        title: '403',
        subTitle: t('fallBackPages.unauthorized'),
        extra: BackHomeButton,
      },
      404: {
        title: '404',
        subTitle: t('fallBackPages.pageNotExist'),
        extra: BackHomeButton,
      },
      500: {
        title: '500',
        subTitle: t('fallBackPages.unknown'),
        extra: BackHomeButton,
      },
    };
  }, [BackHomeButton, t]);

  return (
    <div className="h-screen flex justify-center items-center">
      <Result status={status} {...content[status]} />
    </div>
  );
};

export default StatusFallbackPage;
