import { useLanguage } from '@/hooks/useLanguage';
import { LoadingOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import React from 'react';

const Spinner: React.FC = () => {
  const { t } = useLanguage();
  return (
    <div className="h-full !flex items-center justify-center">
      <Spin
        indicator={
          <div className="text-primary-600 !h-fit !w-fit">
            <LoadingOutlined spin className="text-primary-600 block" />
            <h5 className="text-sm font-medium w-fit ml-2 mt-2">{t('spinner.loading')}</h5>
          </div>
        }
        size="large"
      />
    </div>
  );
};

export default Spinner;
