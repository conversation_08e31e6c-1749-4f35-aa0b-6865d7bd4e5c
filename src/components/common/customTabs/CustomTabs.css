.custom-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list {
  gap: 20px;
}
.custom-tabs > .ant-tabs-nav {
  @apply !mb-[0];
}
.custom-tabs > .ant-tabs-nav::before {
  border-bottom: 0px !important;
}
.custom-tabs
  > .ant-tabs-nav
  > .ant-tabs-nav-wrap
  > .ant-tabs-nav-list
  > .ant-tabs-tab
  > .ant-tabs-tab-btn:hover {
  color: var(--primary-600) !important;
}
.custom-tabs
  > .ant-tabs-nav
  > .ant-tabs-nav-wrap
  > .ant-tabs-nav-list
  > .ant-tabs-tab-active
  > .ant-tabs-tab-btn {
  color: black !important;
  font-weight: 400;
}
.custom-tabs > .ant-tabs-nav .ant-tabs-ink-bar {
  background-color: var(--primary-600) !important; /* Example: Red color */
}
.custom-tabs > .ant-tabs-content-holder > .ant-tabs-content {
  @apply max-h-[81vh];
}
