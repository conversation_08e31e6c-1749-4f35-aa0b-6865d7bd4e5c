import { Tabs, TabsProps } from 'antd';
import './CustomTabs.css';
import { emit } from '@/contexts/PulseContext';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { useCallback, useEffect } from 'react';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { customAlert } from '../customAlert/CustomAlert';
import { useLanguage } from '@/hooks/useLanguage';

interface ITabsComponent extends TabsProps {
  tabs: TabsProps['items'];
  editableRoute?: string;
}

export const TabsComponent: React.FC<ITabsComponent> = (props) => {
  const { tabs, editableRoute } = props;
  const { id, tab } = useParams<{ add: string; id: string; tab: string }>();
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { isBlocked, setIsBlocked } = useNavigationContext();

  const navigateToTab = useCallback(
    (key: string) => {
      const currentTab = tabs?.find((tab) => tab.key === key);
      if (currentTab && typeof currentTab.tabKey === 'string') {
        emit('breadCrumb:changed', { breadCrumbs: currentTab.tabKey });
      }
      if (id) {
        const route = editableRoute || ROUTES.CUSTOMER.CUSTOMER_TAB;
        navigate(route.replace(':id', id).replace(':tab', key), {
          replace: true,
        });
      }
    },
    [editableRoute, id, navigate, tabs]
  );

  const handleTabChange = (key: string) => {
    if (isBlocked) {
      customAlert.warning({
        title: t('common.alert.areYouSure'),
        message: t('common.alert.preventExist'),
        firstButtonTitle: t('common.leave'),
        secondButtonTitle: t('common.stay'),
        firstButtonFunction: () => {
          customAlert.destroy();
          setIsBlocked(false);
          navigateToTab(key);
        },
        secondButtonFunction: () => customAlert.destroy(),
      });
    } else {
      navigateToTab(key);
    }
  };

  useEffect(() => {
    if (tab) {
      handleTabChange(tab);
    }
  }, [tab]);

  return (
    <Tabs
      onChange={handleTabChange}
      items={tabs}
      className="custom-tabs avoid-tab-position"
      activeKey={tab}
      {...props}
    />
  );
};
