import { LeftToRightOutlinedIcon, RightToLeftOutlined } from '@/assets';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Button } from 'antd';
import CustomAgGrid from '../agGrid/AgGrid';
import { useLanguage } from '@/hooks/useLanguage';
import { searchData } from '@/lib/helper';
import { useState, useCallback, useRef, useEffect } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { ISelectedKeys } from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { logger } from '@/lib/logger/logger';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { IRowNode, RowDataUpdatedEvent, SelectionChangedEvent } from 'ag-grid-community';
import { ExtendedRowSelectedEvent, ITransferGridProps } from './transferGrid.types';

const TransferGrid = <T extends { id: string }>(props: ITransferGridProps<T>) => {
  const {
    initialRowData,
    colDefs,
    setIsEdit,
    setSearchText,
    searchText,
    gridProps,
    isSave = true,
    onSave,
    availableGridHeader,
    assignedServices,
    selectedGridHeader,
    availableGridSearchPlaceholder,
    selectedGridSearchPlaceholder,
    availableGridEmptyStateTitle,
    availableGridEmptyStateDescription,
    selectedGridEmptyState,
    mainHeaderTitle,
    saveButtonText,
    preventEditModeOnSave = false,
    hideBackNavigation = false,
  } = props;
  const [availableData, setAvailableData] = useState<T[]>([]);
  const [selectedData, setSelectedData] = useState<T[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<ISelectedKeys>({
    available: [],
    selected: [],
  });
  const [serviceFilterForAvailable, setServiceFilterForAvailable] = useState<T[]>([]);
  const [serviceFilterForSelected, setServiceFilterForSelected] = useState<T[]>([]);
  const [availableCopy, setAvailableCopy] = useState<T[]>([]);
  const [selectedCopy, setSelectedCopy] = useState<T[]>([]);

  const [availableSearchTextValue, setAvailableSearchTextInput] = useState('');
  const [selectedSearchTextValue, setSelectedSearchTextInput] = useState('');

  const availableGridRef = useRef<AgGridReact>(null);
  const selectedGridRef = useRef<AgGridReact>(null);
  const currentNodeRef = useRef<string[]>([]);
  const selectedButNotSavedRef = useRef<{ available: any[]; selected: any[] }>({
    available: [],
    selected: [],
  });

  const { t } = useLanguage();

  useEffect(() => {
    if (initialRowData) {
      const availableData =
        selectedButNotSavedRef.current.selected.length > 0
          ? initialRowData.filter(
            (data) => !selectedButNotSavedRef.current?.selected?.some((d) => d.id === data.id)
          )
          : initialRowData;

      if (!searchText.searchTextForAvailable) {
        setAvailableCopy(availableData);
      }

      const selectedData =
        selectedButNotSavedRef.current.available.length > 0
          ? assignedServices.filter(
            (data) => !selectedButNotSavedRef.current?.available.some((d) => d.id === data.id)
          )
          : assignedServices;

      if (!searchText.searchTextForSelected) {
        setSelectedCopy(selectedData);
      }

      // TODO:refactor this component  by removing this refs - remove it if no need
      setAvailableData([...availableData, ...selectedButNotSavedRef.current.available]);
      setServiceFilterForAvailable([...availableData, ...selectedButNotSavedRef.current.available]);
      setSelectedData(
        Array.from(new Set([...selectedData, ...selectedButNotSavedRef.current.selected]))
      );
      setServiceFilterForSelected(
        Array.from(new Set([...selectedData, ...selectedButNotSavedRef.current.selected]))
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [assignedServices, initialRowData]);

  const moveToSelected = () => {
    if (!selectedKeys?.available.length) return;

    const availableForFilter = searchText.searchTextForAvailable
      ? availableCopy.filter((data) => !selectedKeys.available.includes(data.id))
      : availableData;

    const remainingItems = (
      searchText.searchTextForAvailable ? availableCopy : availableForFilter
    ).filter((item) => !selectedKeys.available.includes(item.id));

    const selectedItems = availableCopy.filter((item) => selectedKeys.available.includes(item.id));

    selectedButNotSavedRef.current = { ...selectedButNotSavedRef.current, selected: selectedItems };

    setAvailableCopy(remainingItems);
    setAvailableData(remainingItems);
    setSelectedData((prev) => [...prev, ...selectedItems]);
    setSelectedCopy((prev) => [...prev, ...selectedItems]);
    setServiceFilterForSelected((prev) => [...prev, ...selectedItems]);
    setAvailableSearchTextInput('');
    setSelectedSearchTextInput('');
    setSearchText({
      searchTextForAssigned: '',
      searchTextForAvailable: '',
      searchTextForSelected: '',
    });

    setServiceFilterForAvailable((prev) =>
      prev.filter((item) => !selectedKeys.available.includes(item.id))
    );

    setSelectedKeys({ available: [], selected: [] });
    currentNodeRef.current = [];
  };

  const moveToAvailable = () => {
    if (!selectedKeys?.selected.length) return;

    const availableForFilter = searchText.searchTextForSelected
      ? selectedCopy.filter((data) => !selectedKeys.selected.includes(data.id))
      : selectedData;

    const remainingItems = (
      searchText.searchTextForSelected ? selectedCopy : availableForFilter
    ).filter((item) => !selectedKeys.selected.includes(item.id as string));

    const selectedItems = selectedCopy.filter((item) => selectedKeys.selected.includes(item.id));

    selectedButNotSavedRef.current = {
      ...selectedButNotSavedRef.current,
      available: selectedItems,
    };

    setSelectedCopy(remainingItems);
    setSelectedData(remainingItems);
    setAvailableData((prev) => [...prev, ...selectedItems]);
    setAvailableCopy((prev) => [...prev, ...selectedItems]);
    setServiceFilterForAvailable((prev) => [...prev, ...selectedItems]);
    setAvailableSearchTextInput('');
    setSelectedSearchTextInput('');
    setSearchText({
      searchTextForAssigned: '',
      searchTextForAvailable: '',
      searchTextForSelected: '',
    });

    setServiceFilterForSelected((prev) =>
      prev.filter((item) => !selectedKeys.selected.includes(item.id))
    );

    setSelectedKeys({ available: [], selected: [] });
    currentNodeRef.current = [];
  };

  const onSelectionChangeHandler = (params: SelectionChangedEvent<T>): void => {
    selectedGridRef.current?.api.refreshHeader();
    availableGridRef.current?.api.refreshHeader();

    const key: keyof typeof selectedKeys = params.context.name;
    // added this condition avoid unnecessary api cal
    if (
      params.source !== 'checkboxSelected' &&
      params.source !== 'uiSelectAll' &&
      params.source !== 'uiSelectAllCurrentPage'
    )
      return;

    const selectedRows = params.api.getSelectedRows();
    const selectedIds = selectedRows.map((row) => row.id);

    const searchKey =
      params.context.name === 'available' ? 'searchTextForAvailable' : 'searchTextForSelected';

    setSelectedKeys((prev: any) => {
      // persist previous selected id when search is available
      const combinedIds = [...prev[key], ...selectedIds].filter(
        (id) => !currentNodeRef.current.includes(id)
      );

      // show when combined ids when search is available
      const mergedIds = searchText[searchKey] ? combinedIds : selectedIds;
      const uniqueIds = Array.from(new Set(mergedIds));

      return { ...prev, [key]: uniqueIds };
    });
  };

  const searchHandlerAvailable = useCallback(
    (value: string) => {
      const results = searchData(
        serviceFilterForAvailable,
        {
          query: value,
        },
        colDefs
      );
      setSearchText((prev) => ({ ...prev, searchTextForAvailable: value }));
      setAvailableData(results);
    },
    [colDefs, serviceFilterForAvailable, setSearchText]
  );

  const searchHandlerSelected = useCallback(
    (value: string) => {
      const results = searchData(
        serviceFilterForSelected,
        {
          query: value,
        },
        colDefs
      );

      setSearchText((prev) => ({ ...prev, searchTextForSelected: value }));
      setSelectedData(results);
    },
    [colDefs, serviceFilterForSelected, setSearchText]
  );

  const handleSave = async () => {
    try {
      setSearchText({
        searchTextForAvailable: '',
        searchTextForSelected: '',
        searchTextForAssigned: '',
      });
      if (onSave) {
        await onSave(serviceFilterForSelected);
      }
      !preventEditModeOnSave && setIsEdit(false);
    } catch (error) {
      logger.error('ERROR', error as Error);
    }
  };

  const onRowDataUpdated = (params: RowDataUpdatedEvent) => {
    const gridRef = params.context.name === 'available' ? availableGridRef : selectedGridRef;
    const key: keyof typeof selectedKeys =
      params.context.name === 'available' ? 'available' : 'selected';

    if (!gridRef.current) return;

    const nodesToSelect: IRowNode[] = [];

    gridRef.current.api.forEachNode((node) => {
      if (selectedKeys[key].includes(node.data.id)) {
        nodesToSelect.push(node);
      }
    });

    gridRef.current.api.setNodesSelected({
      nodes: nodesToSelect,
      newValue: true,
    });
  };

  const onRowSelected = (params: ExtendedRowSelectedEvent) => {
    // Avoid unnecessary function call
    if (params.source !== 'checkboxSelected' && params.source !== 'uiSelectAll') return;

    const { id } = params.data || {};
    if (!id) return;

    // add or remove id from the deselect array
    currentNodeRef.current = params.node?.selected
      ? currentNodeRef.current.filter((nodeId) => nodeId !== id)
      : Array.from(new Set([...currentNodeRef.current, id]));
  };

  return (
    <div className="mt-2">
      <div className="flex flex-col gap-2">
        {!hideBackNavigation && (
          <PageHeadingComponent
            onBackClick={() => setIsEdit(false)}
            title={mainHeaderTitle}
            parentClassName="flex gap-[10px] pt-2"
            isChildComponent={true}
            classNameChildren="w-[24px] h-[24px] flex justify-center align-middle mt-1 cursor-pointer"
            classNameTitle="text-[20px] font-[600]"
          />
        )}
        <div className="flex gap-5 align-middle w-full pr-4">
          <div className="ag-theme-alpine flex flex-col gap-3 w-[47%]">
            <div className="flex justify-between align-middle">
              <PageHeadingComponent
                title={availableGridHeader}
                classNameTitle="text-[20px] font-[600]"
                children={
                  <SearchFilterComponent
                    advanceFilter={false}
                    onSearch={searchHandlerAvailable}
                    colDefs={colDefs}
                    className="!pt-2"
                    searchInputPlaceholder={availableGridSearchPlaceholder}
                    searchText={availableSearchTextValue}
                    setSearchText={setAvailableSearchTextInput}
                  />

                }
              />
            </div>
            <CustomAgGrid
              gridId={gridProps?.gridId || GridIdConstant.GRID_WRAPPER_FOR_GROUP_MODIFIER}
              {...gridProps}
              className={
                gridProps?.className ||
                `${availableData?.length === 0 ? 'border' : ''} rounded-md ${gridProps?.className || '!h-[65vh] lg:!h-[65vh] 3xl:!h-[67vh] '}`
              }
              gridRef={availableGridRef}
              columnDefs={colDefs}
              rowData={availableData}
              onSelectionChanged={(params) => onSelectionChangeHandler(params)}
              nullStateClassName={availableData.length === 0 ? 'border border-gray-300' : ''}
              rowSelection={{
                mode: 'multiRow',
                headerCheckbox: !(selectedKeys.selected.length > 0) || false,
                isRowSelectable: () => !(selectedKeys.selected.length > 0),
                selectAll: 'currentPage',
              }}
              onRowSelected={onRowSelected}
              onRowDataUpdated={onRowDataUpdated}
              context={{ name: 'available' }}
              emptyState={{
                title: searchText.searchTextForAvailable
                  ? t('common.noMatchesFound')
                  : availableGridEmptyStateTitle,
                description: searchText.searchTextForAvailable
                  ? ''
                  : availableGridEmptyStateDescription,
              }}
            />
          </div>

          <div className="flex flex-col gap-3 justify-center">
            <Button
              onClick={moveToSelected}
              icon={<LeftToRightOutlinedIcon />}
              disabled={selectedKeys.available.length === 0}
            />

            <Button
              onClick={moveToAvailable}
              icon={<RightToLeftOutlined />}
              disabled={selectedKeys.selected.length === 0}
            />
          </div>

          <div className="ag-theme-alpine flex flex-col gap-3 w-[47%]">
            <div className="flex justify-between align-middle">
              <PageHeadingComponent
                title={selectedGridHeader}
                classNameTitle="text-[20px] font-[600]"
                children={
                  <SearchFilterComponent
                    advanceFilter={false}
                    onSearch={searchHandlerSelected}
                    colDefs={colDefs}
                    className="!pt-2"
                    searchInputPlaceholder={selectedGridSearchPlaceholder}
                    searchText={selectedSearchTextValue}
                    setSearchText={setSelectedSearchTextInput}
                  />
                }
              />
            </div>
            <CustomAgGrid
              gridId={gridProps?.gridId || GridIdConstant.GRID_WRAPPER_FOR_GROUP_MODIFIER}
              {...gridProps}
              className={`${selectedData?.length === 0 ? 'border' : ''} rounded-md ${gridProps?.className || '!h-[65vh] lg:!h-[65vh] 3xl:!h-[67vh]'}`}
              gridRef={selectedGridRef}
              rowData={selectedData}
              columnDefs={colDefs}
              onSelectionChanged={(params) => onSelectionChangeHandler(params)}
              rowSelection={{
                mode: 'multiRow',
                headerCheckbox: !(selectedKeys.available.length > 0) || false,
                isRowSelectable: () => !(selectedKeys.available.length > 0),
                selectAll: 'currentPage',
              }}
              nullStateClassName={selectedData?.length === 0 ? 'border border-gray-300' : ''}
              onRowDataUpdated={onRowDataUpdated}
              onRowSelected={onRowSelected}
              context={{ name: 'selected' }}
              emptyState={{
                title: searchText.searchTextForSelected
                  ? t('common.noMatchesFound')
                  : selectedGridEmptyState,
                description: '',
              }}
            />
          </div>
        </div>
        <div className="w-full flex justify-start">
          {isSave && (
            <Button
              type="primary"
              className="mt-2 bg-primary-600 hover:!bg-primary-600"
              onClick={handleSave}
            >
              {saveButtonText || t('common.save')}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TransferGrid;
