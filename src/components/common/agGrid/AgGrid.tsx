import React, { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { CellContextMenuEvent, Column } from 'ag-grid-community';
import { AgGridReact } from 'ag-grid-react';
import { GridStorageKey, IColDef, IWithContextMenu, TGridProps } from '@customTypes/AgGridTypes';
import { useConfig } from '@/contexts/ConfigContext';
import { IContextMenuItems } from '@customTypes/ContextMenuTypes';
import GridContextMenu from '../gridContextMenu/GridContextMenu';
import {
  FirstPageButtonIcon,
  GridPagingIcon,
  LastPageButtonIcon,
  NextPageButtonIcon,
  PreviousPageButtonIcon,
} from '@/assets';
import { on } from '@contexts/PulseContext.tsx';
import Spinner from '../spinner/Spinner';
import { EmptyStatePage } from '../emptyState/EmptyStatePage';
import { IEmptyStatePageProps } from '../emptyState/emptyStateTypes';
import { Select } from 'antd';
import { pageSizeDropdown } from '@/constant/generalConstant';
import { addSetting, useGetSettings } from '@/api/settings/settings.service';
import { createColumnState } from '@/components/specific/columnManage/hooks/useColumnManagement';
import { useGetCurrentUser } from '@/api/auth/auth.service';
import { TrackedError } from '@/types/AxiosTypes';
import { AxiosError } from 'axios';
import { SettingsScope } from '@/constant/SettingsScope';
import { SettingsKeys } from '@/constant/SettingsKeys';

/**
 * A custom ag-Grid component that extends the functionality of ag-Grid by
 * accepting standard grid properties along with custom props for additional features,
 * such as a configurable context menu.
 *
 * @param {TGridProps} props - The properties for configuring the ag-Grid, including standard
 * grid options like `colDefs` and `rowData`, and custom properties for additional features.
 *
 */

const CustomAgGrid: React.FC<TGridProps> = (props: TGridProps) => {
  const {
    columnDefs,
    rowData,
    defaultColDef,
    isContextMenu,
    gridRef,
    className,
    gridName,
    emptyState,
    loading,
    gridId,
    paginationProps,
    nullStateClassName,
  } = props;
  const { contextMenuItem, onContextMenu } = props as IWithContextMenu;

  const { config } = useConfig();
  const [colDefs, setColDefs] = useState<IColDef[]>([]);
  const [isRowDataRendering, setIsRowDataRendering] = useState(true);
  const [currentPageSize, setCurrentPageSize] = useState(pageSizeDropdown[2].value);
  const [contextMenuEvent, setContextMenuEvent] = useState<CellContextMenuEvent<typeof rowData>>();
  const gridWrapperRef = useRef<HTMLDivElement>(null);

  const { data: userInfo } = useGetCurrentUser();

  const hasHandledErrorRef = useRef(false);

  const addColumnStateSettingIfNotExist = (error: AxiosError) => {
    if (hasHandledErrorRef.current) return false;

    const errorStack = error?.response?.data as TrackedError;
    if (errorStack?.code === '414001' && gridName) {
      hasHandledErrorRef.current = true;

      addSetting({
        userId: userInfo?.id as string,
        scope: SettingsScope.USER,
        key: gridName as SettingsKeys,
        value: createColumnState(columnDefs),
      }).then(async () => {
        await refetchSettings();
      });
    }
    return false;
  };

  const {
    data: gridStateFromApi,
    refetch: refetchSettings,
    isFetched,
  } = useGetSettings(gridName as GridStorageKey, userInfo?.id, {
    throwOnError: addColumnStateSettingIfNotExist,
    enabled: Boolean(!!userInfo?.id && gridName),
  });

  const onFirstDataRendered = useCallback(async () => {
    setIsRowDataRendering(false);
    const buildColumnsFromState = (state: any[]) =>
      state.map((storedCol) => {
        const originalCol = columnDefs.find((col) => col.field === storedCol.id);
        return {
          ...originalCol,
          hide: !storedCol.visible,
        };
      });

    if (!gridStateFromApi) {
      setColDefs(columnDefs);
    } else {
      const finalCol = buildColumnsFromState(gridStateFromApi.value || []) as IColDef[];
      setColDefs(finalCol);
    }
  }, [columnDefs, gridStateFromApi]);

  useEffect(() => {
    onFirstDataRendered();
  }, [columnDefs, onFirstDataRendered, gridStateFromApi, isFetched]);

  useLayoutEffect(() => {
    const gridContainer = gridWrapperRef.current;

    if (!gridContainer) return;

    const preventContextMenuBehaviors = (event: MouseEvent) => {
      event.preventDefault();
    };

    gridContainer.addEventListener('contextmenu', preventContextMenuBehaviors);

    return () => {
      gridContainer.removeEventListener('contextmenu', preventContextMenuBehaviors);
    };
  }, []);

  /**
   * Returns the maximum nesting depth of sub-menus in a context menu.
   *
   * @param {IContextMenuItems[]} menuItems - Array of menu items, each potentially with nested sub-menus.
   * @returns {number} - The maximum depth of nested sub-menus.
   * This helps to wrap nested sub-menus, preventing overflow.
   */
  const getMaxNestedLevel = useCallback((menuItems: IContextMenuItems[] = []) => {
    let maxDepthLevel = 0;

    function traverse(items: IContextMenuItems[], level: number) {
      items?.forEach((item) => {
        if (item.subMenu && item.subMenu.length > 0) {
          maxDepthLevel = Math.max(maxDepthLevel, level + 1);
          traverse(item.subMenu, level + 1);
        }
      });
    }

    traverse(menuItems, 0);

    return maxDepthLevel;
  }, []);

  const maxNestedLevel = useMemo(
    () => getMaxNestedLevel(contextMenuItem),
    [contextMenuItem, getMaxNestedLevel]
  );

  const onCellContextMenu = (params: CellContextMenuEvent) => {
    const event = params.event as PointerEvent;
    const gridContainer = gridWrapperRef.current;
    const gridContextMenuContainer = document.getElementById('gridContextMenu') as HTMLElement;
    const subMenus = document.getElementsByClassName('submenu') as HTMLCollectionOf<HTMLElement>;

    if (
      gridContextMenuContainer &&
      gridContainer &&
      config?.ContextMenuConfig?.minimumSubMenuSize
    ) {
      gridContextMenuContainer.style.opacity = `1`;
      gridContextMenuContainer.style.visibility = `visible`;
      gridContextMenuContainer.classList.remove('hide-context-menu');
      document.body.style.overflow = 'hidden';

      const nestedLevel = config.ContextMenuConfig.minimumSubMenuSize * maxNestedLevel;

      const isWidthOverflowingIndicator =
        event.clientX + gridContextMenuContainer.offsetWidth - gridContainer.offsetLeft >
        gridContainer.offsetWidth;

      const isWidthOverflowingIndicatorForSubmenu =
        event.clientX +
          gridContextMenuContainer.offsetWidth -
          gridContainer.offsetLeft +
          nestedLevel >
        gridContainer.offsetWidth;

      const isHeightOverflowingIndicator =
        event.clientY + gridContextMenuContainer.offsetHeight - gridContainer.offsetTop >
        gridContainer.offsetHeight;

      const clientX = isWidthOverflowingIndicator
        ? event.clientX - gridContextMenuContainer.offsetWidth
        : event.clientX;

      const clientY = isHeightOverflowingIndicator
        ? event.clientY - gridContextMenuContainer.offsetHeight
        : event.clientY;

      gridContextMenuContainer.style.top = `${clientY}px`;
      gridContextMenuContainer.style.left = `${clientX}px`;

      // Wraps submenu left if overflowing, otherwise positions right
      Array.from(subMenus).forEach((element) => {
        element.style.left = isWidthOverflowingIndicatorForSubmenu ? `-100%` : '100%';
      });
    }

    onContextMenu(params);

    /**
     * Auto-closes the context menu when the user clicks outside of it.
     */
    const hideContextMenu = (e: MouseEvent) => {
      if (!gridContextMenuContainer.contains(e.target as Node)) {
        gridContextMenuContainer.style.opacity = '0';
        gridContextMenuContainer.style.visibility = `hidden`;
        gridContextMenuContainer.classList.add('hide-context-menu');
      }
    };

    window.addEventListener('click', hideContextMenu);
  };

  const onGridScrollHandler = useCallback(() => {
    const gridContextMenuContainer = document.getElementById('gridContextMenu') as HTMLElement;
    if (gridContextMenuContainer.style.opacity === '1') {
      gridContextMenuContainer.style.opacity = '0';
      gridContextMenuContainer.style.visibility = `hidden`;
      gridContextMenuContainer.classList.add('hide-context-menu');
    }
  }, []);

  on('columnManager:changed', (data) => {
    if (!gridRef) return;

    const columnOrder = data.gridState.map((column: { id: string }) => column.id);
    gridRef?.current?.api.moveColumns(columnOrder, 0);

    data.gridState.forEach((col: { id: string | Column<any>; visible: boolean }) => {
      gridRef?.current?.api.setColumnsVisible([col.id], col.visible);
    });
  });

  /**
   * Customizes icons for the grid component.
   *
   * @returns {object} An object containing customized icon components.
   */
  const icons = useMemo(() => {
    return {
      smallDown: GridPagingIcon(),
    };
  }, []);

  const onContextMenuHandler = (params: CellContextMenuEvent) => {
    onCellContextMenu(params);
    setContextMenuEvent(params);
  };

  const totalRecords = paginationProps?.total || rowData?.length || 0;
  const currentPage = paginationProps?.page || 1;
  const pageSize = paginationProps?.limit || pageSizeDropdown[0].value;
  const to = (paginationProps?.page || 1) * currentPageSize;
  const startRecord = to + 1 - currentPageSize;
  const endRecord = to > totalRecords ? totalRecords : to;

  const handlePageChange = useCallback(
    (newPage: number) => {
      if (
        (newPage > currentPage && paginationProps?.hasNextPage) ||
        (newPage < currentPage && paginationProps?.hasPreviousPage)
      ) {
        paginationProps?.onPaginationChange &&
          paginationProps?.onPaginationChange(newPage, pageSize);
      }
    },
    [currentPage, pageSize, paginationProps]
  );

  const onPaginationLimitChange = useCallback(
    (value: number) => {
      setCurrentPageSize(value);
      paginationProps?.onPaginationChange && paginationProps?.onPaginationChange(1, value);
    },
    [paginationProps]
  );

  return (
    <div id={gridId || 'gridWrapper'} className={`ag-theme-alpine`} ref={gridWrapperRef}>
      {rowData ? (
        rowData.length > 0 ? (
          <div className="border border-[#e1e7ea] rounded-md">
            <AgGridReact
              {...props}
              ref={gridRef}
              rowData={rowData}
              rowBuffer={100}
              animateRows={false}
              suppressColumnVirtualisation
              suppressMovableColumns
              columnDefs={colDefs}
              loading={loading || isRowDataRendering}
              defaultColDef={defaultColDef}
              onGridReady={onFirstDataRendered}
              suppressColumnMoveAnimation={false}
              tooltipShowDelay={500}
              onCellContextMenu={isContextMenu ? onContextMenuHandler : undefined}
              onBodyScroll={onGridScrollHandler}
              icons={icons}
              pagination={props.paginationProps ? false : true}
              className={
                className || '2xsm:!h-[62vh] sm:!h-[65vh] md:!h-[83vh] lg:!h-[83vh] 3xl:!h-[85vh] '
              }
            />
            {props.paginationProps && (
              <div className="flex 3xsm:py-2 3xsm:flex-col md:flex-row min-h-[48px] max-h-[100px] bg-[#E1F4FD] justify-end items-center gap-3  pr-2">
                <div className="flex gap-2 items-center">
                  <span>Page Size:</span>
                  <Select
                    options={pageSizeDropdown}
                    defaultValue={pageSizeDropdown[0].value}
                    value={paginationProps?.limit}
                    onSelect={onPaginationLimitChange}
                  />
                </div>
                <div className="flex gap-2 items-center">
                  <span>
                    {<span className="font-semibold">{startRecord}</span>} to{' '}
                    {<span className="font-semibold">{endRecord}</span>} of{' '}
                    {<span className="font-semibold">{totalRecords}</span>}
                  </span>
                </div>
                <div className="flex gap-2 items-center">
                  <img
                    src={FirstPageButtonIcon}
                    onClick={() => handlePageChange(1)}
                    className={`${!paginationProps?.hasPreviousPage ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'} `}
                  />
                  <img
                    src={PreviousPageButtonIcon}
                    onClick={() => handlePageChange(currentPage - 1)}
                    className={`${!paginationProps?.hasPreviousPage ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'} `}
                    role="button"
                  />
                  <span>
                    Page {<span className="font-semibold">{paginationProps?.page}</span>} of{' '}
                    {<span className="font-semibold">{paginationProps?.totalPages}</span>}
                  </span>
                  <img
                    className={`${!paginationProps?.hasNextPage ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'} `}
                    src={NextPageButtonIcon}
                    onClick={() => handlePageChange(currentPage + 1)}
                  />
                  <img
                    src={LastPageButtonIcon}
                    className={`${!paginationProps?.hasNextPage ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'} `}
                    onClick={() => handlePageChange(paginationProps?.totalPages || 0)}
                  />
                </div>
              </div>
            )}
            <GridContextMenu menuItems={contextMenuItem} contextMenuEvent={contextMenuEvent} />
          </div>
        ) : (
          <div
            className={`h-full w-full flex justify-center items-center rounded-md ${nullStateClassName && nullStateClassName}`}
          >
            <EmptyStatePage {...(emptyState as IEmptyStatePageProps)} />
          </div>
        )
      ) : (
        <div className="h-full w-full flex justify-center items-center">
          <Spinner />
        </div>
      )}
    </div>
  );
};

export default CustomAgGrid;
