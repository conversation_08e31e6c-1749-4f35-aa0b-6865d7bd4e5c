export enum CustomerCategory {
  'Home & Office' = 'Home & Office',
  'Equipment Restaurants' = 'Equipment Restaurants',
  'Retail' = 'Retail',
  'Manufacturing' = 'Manufacturing Restaurants',
  'Services' = 'Services',
}

export interface ICustomer {
  id: string;
  companyName: string;
  accountNumber: string;
  contactName: string;
  addressLine1: string;
  city: string;
  phone: string;
  email: string;
  fax: string;
  status: boolean;
  category: string[];
  province: string;
  country: string;
  phoneNumberPrefix: string;
  faxNumberPrefix: string;
  postalCode: string;
  dateAdded: string;
  dateUpdated: string;
  lastUpdateBy: string;
}
export interface EditCustomerSchema {
  name: string;
  accountNumber: string;
  contactName: string;
  phoneNumberPrefix: string;
  // id: string;
  phoneNumber: string;
  faxNumberPrefix: string;
  faxNumber: string;
  addressLine1: string;
  city: string;
  website: string;
  country: string;
  email: string;
  status: boolean;
  category: string;
  dateAdded: string; // Date string from <PERSON><PERSON>'s date.past
  dateUpdated: string; // Date string from Faker's date.recent
  lastUpdateBy: string;
}

export interface ICustomerContextMenuParams {
  node: {
    data: ICustomer;
  };
}
