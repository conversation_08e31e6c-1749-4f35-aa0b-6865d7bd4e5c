export const customerSchema = {
  companyName: {
    type: 'string',
    faker: 'company.name',
  },
  accountNumber: {
    type: 'string',
    faker: 'finance.accountNumber',
  },
  contactName: {
    type: 'string',
    faker: 'person.fullName',
  },
  addressLine1: {
    type: 'string',
    faker: 'location.streetAddress',
  },
  city: {
    type: 'string',
    faker: 'location.city',
  },
  phone: {
    type: 'string',
    faker: 'phone.number',
    options: { format: '+ 49 ########' },
  },
  email: {
    type: 'string',
    faker: 'internet.email',
  },
  fax: {
    type: 'string',
    faker: 'phone.number',
    options: { format: '+ 49 ########' },
  },
  status: {
    type: 'boolean',
    faker: 'datatype.boolean',
  },
  category: {
    type: 'string',
    faker: 'helpers.arrayElement',
    options: {
      array: ['Home & Office', 'Equipment Restaurants', 'Retail', 'Manufacturing', 'Services'],
    },
  },
  dateAdded: {
    type: 'string',
    faker: 'date.past',
    options: { years: 3 },
  },
  dateUpdated: {
    type: 'string',
    faker: 'date.recent',
    options: { days: 10 },
  },
  lastUpdateBy: {
    type: 'string',
    faker: 'person.fullName',
  },
};

export const editCustomerSchema = {
  name: {
    type: 'string',
    faker: 'person.name',
  },
  accountNumber: {
    type: 'string',
    faker: 'finance.accountNumber',
  },
  contactName: {
    type: 'string',
    faker: 'person.fullName',
  },
  phoneNumberPrefix: {
    type: 'string',
    faker: 'location.countryCode',
  },
  phoneNumber: {
    type: 'string',
    faker: 'phone.number',
  },
  faxNumberPrefix: {
    type: 'string',
    faker: 'location.countryCode',
  },
  faxNumber: {
    type: 'string',
    faker: 'phone.number',
    // options: { format: '(+1)55555 55555' },
  },
  addressLine1: {
    type: 'string',
    faker: 'location.streetAddress',
  },
  city: {
    type: 'string',
    faker: 'location.city',
  },
  website: {
    type: 'string',
    faker: 'internet.url',
    options: { protocol: 'www' },
  },
  country: {
    type: 'string',
    faker: 'location.country',
  },
  email: {
    type: 'string',
    faker: '<EMAIL>',
  },

  status: {
    type: 'boolean',
    faker: 'datatype.boolean',
  },

  dateAdded: {
    type: 'string',
    faker: 'date.past',
  },
  dateUpdated: {
    type: 'string',
    faker: 'date.recent',
  },
  lastUpdateBy: {
    type: 'string',
    faker: 'person.fullName',
  },
};
