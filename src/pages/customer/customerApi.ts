import { faker } from '@faker-js/faker';

export const customerApi = {
  update: async (id: string, data: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return {
      id: id,
      ...data,
    };
  },
  create: async (data: any) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return data;
  },
  get: async (id: string) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return {
      id: id,
      ...generateFakeCustomer(),
    };
  },
  delete: async (id: string) => {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return {
      id: id,
    };
  },
};
export const generateFakeCustomer = () => {
  return {
    companyName: faker.company.name(),
    name: faker.person.firstName(),
    accountNumber: faker.finance.accountNumber(),
    contactName: faker.person.fullName(),
    phoneNumberPrefix: faker.helpers.arrayElement(['USA +1']),
    phoneNumber: faker.helpers.arrayElement(['523-9834', '042-9822']),
    faxNumberPrefix: faker.helpers.arrayElement(['USA +1']),
    faxNumber: faker.helpers.arrayElement(['523-9834', '042-9822']),
    addressLine1: faker.location.streetAddress(),
    city: faker.location.city(),
    website: faker.internet.url({ protocol: 'https' }),
    country: faker.helpers.arrayElement(['Canada', 'USA']),
    email: faker.internet.email(),
    status: faker.datatype.boolean(),
    dateAdded: faker.date.past().toISOString(),
    dateUpdated: faker.date.recent().toISOString(),
    lastUpdateBy: faker.person.fullName(),
    postalCode: faker.location.zipCode({
      format: '### ###',
    }),
  };
};
