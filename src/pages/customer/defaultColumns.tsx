import { ICellRendererParams } from 'ag-grid-community';
import CustomTooltip from '@components/common/customTooltip/CustomTooltip.tsx';
import Icon from '@ant-design/icons/lib/components/Icon';

import { IColDef } from '@/types/AgGridTypes.ts';
import { DeleteIcon, EyeIcon, HistoryIcon } from '@/assets';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { LanguagePath, TranslationVariables } from '@/i18n/languageLoader';
import { dateFormatter } from '@/lib/helper/dateHelper';

export const customerColDefs = (
  searchText: string,
  handleDeletePopup: (params: ICellRendererParams) => void,
  t: (path: LanguagePath, vars?: TranslationVariables | undefined) => string,
  isColumnSortable: (field: string) => boolean,
  navigate?: ReturnType<typeof useNavigate>
): IColDef[] => {
  const handleCustomerEdit = (params: ICellRendererParams) => {
    navigate &&
      navigate(
        ROUTES.CUSTOMER.CUSTOMER_TAB.replace(':id', params.data.id).replace(':tab', 'general')
      );
  };

  return [
    {
      field: 'companyName',
      flex: 1,
      headerName: t('dashboard.customer.columns.companyName'),
      minWidth: 170,
      sortable: isColumnSortable('companyName'),
      unSortIcon: isColumnSortable('companyName'),
      tooltipField: 'companyName',
      type: 'string',
      visible: true,
      cellRenderer: (params: { value: string }) => {
        return searchText ? highlightText(params.value, searchText) : params.value;
      },
    },
    {
      field: 'accountNumber',
      flex: 1,
      headerName: t('dashboard.customer.columns.accountNumber'),
      minWidth: 170,
      sortable: isColumnSortable('accountNumber'),
      unSortIcon: isColumnSortable('accountNumber'),
      visible: true,
      type: 'string',
      cellRenderer: (params: { value: string }) => {
        return searchText ? highlightText(params.value, searchText) : params.value;
      },
    },
    {
      field: 'contactName',
      flex: 1,
      headerName: t('dashboard.customer.columns.contactName'),
      minWidth: 170,
      sortable: isColumnSortable('contactName'),
      unSortIcon: isColumnSortable('contactName'),
      visible: true,
      type: 'string',
      cellRenderer: (params: { value: string }) => {
        return searchText ? highlightText(params.value, searchText) : params.value;
      },
    },
    {
      field: 'addressLine1',
      flex: 1,
      headerName: t('dashboard.customer.columns.addressLine1'),
      minWidth: 170,
      sortable: isColumnSortable('addressLine1'),
      unSortIcon: isColumnSortable('addressLine1'),
      visible: true,
      type: 'string',
      cellRenderer: (params: { value: string }) => {
        return searchText ? highlightText(params.value, searchText) : params.value;
      },
    },
    {
      field: 'city',
      flex: 1,
      type: 'string',
      headerName: t('addressPage.colDefs.city'),
      minWidth: 170,
      sortable: isColumnSortable('contactName'),
      unSortIcon: isColumnSortable('contactName'),
      visible: true,
      cellRenderer: (params: { value: string }) => {
        return searchText ? highlightText(params.value, searchText) : params.value;
      },
    },
    {
      field: 'phoneNumber',
      flex: 1,
      type: 'string',
      headerName: t('addressPage.colDefs.phone'),
      minWidth: 170,
      sortable: isColumnSortable('phoneNumber'),
      unSortIcon: isColumnSortable('phoneNumber'),
      visible: true,
      cellRenderer: (params: { value: string }) => {
        return searchText ? highlightText(params.value, searchText) : params.value;
      },
    },
    {
      field: 'email',
      flex: 1,
      type: 'string',
      headerName: t('auth.email'),
      minWidth: 170,
      sortable: isColumnSortable('email'),
      unSortIcon: isColumnSortable('email'),
      visible: true,
      cellRenderer: (params: { value: string }) => {
        return searchText ? highlightText(params.value, searchText) : params.value;
      },
    },
    {
      field: 'postalCode',
      flex: 1,
      headerName: t('zonePage.operationalForm.postalCodes'),
      type: 'string',
      minWidth: 170,
      sortable: isColumnSortable('postalCode'),
      unSortIcon: isColumnSortable('postalCode'),
      visible: true,
      cellRenderer: (params: { value: string }) => {
        return searchText ? highlightText(params.value, searchText) : params.value;
      },
    },
    {
      field: 'status',
      flex: 1,
      headerName: t('dashboard.customer.columns.status'),
      minWidth: 170,
      sortable: isColumnSortable('status'),
      unSortIcon: isColumnSortable('status'),
      type: 'boolean',
      cellRenderer: (params: ICellRendererParams) => {
        return params.data.status ? (
          <>
            <span className="h-[10px] w-[10px] rounded-full bg-[seagreen] inline-block mr-1" />{' '}
            Active
          </>
        ) : (
          <>
            <span className="h-[10px] w-[10px] rounded-full bg-red-600 inline-block mr-1" />{' '}
            Inactive
          </>
        );
      },
      visible: true,
    },
    {
      field: 'action',
      flex: 1,
      headerName: t('zonePage.colDefs.action'),
      pinned: 'right',
      width: 110,
      sortable: false,
      resizable: false,
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div className="flex gap-2 h-full items-center w-full overflow-hidden">
            <CustomTooltip
              content={
                <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                  Added:{' '}
                  <span className=" block w-fit text-sm font-semibold">
                    {dateFormatter(params.data.createdAt)}
                  </span>
                  <hr className="border-[#0000001c]" />
                  Modified:{' '}
                  <span className=" block w-fit text-sm font-semibold">
                    {dateFormatter(params.data.updatedAt)}
                  </span>
                  <hr className="border-[#0000001c]" />
                </div>
              }
              placement="leftTop"
            >
              <div>
                <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
              </div>
            </CustomTooltip>
            <Icon
              component={EyeIcon}
              onClick={() => {
                handleCustomerEdit(params);
              }}
              className="cursor-pointer"
            />
            <Icon
              onClick={() => handleDeletePopup(params)}
              component={DeleteIcon}
              className="cursor-pointer"
            />
          </div>
        );
      },
      visible: true,
    },
  ];
};
