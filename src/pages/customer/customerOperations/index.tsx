import { TabsComponent } from '@/components/common/customTabs/CustomTabs';

import { DefaultTabsForCustomer } from '../DefaultTabs';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import PageBreadCrumbsComponent from '@/components/specific/pageBreadCrumb/PageBreadCrumbComponent';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { customerHook } from '@/api/customer/useCustomer';

const CustomerTabs = () => {
  const { id, tab } = useParams<{ id: string; tab: string }>();
  const isEditMode = Boolean(id);
  const [companyName, setCompanyName] = useState('');
  const [idExists, setIdExists] = useState(false);
  useEffect(() => {
    if (id) {
      setIdExists(true);
    }
  }, [id]);
  const { t } = useLanguage();
  const breadCrumbObj: { [key: string]: string } = useMemo(() => {
    return {
      general: t('vehiclePage.breedCrumbs.general'),
      contacts: 'Contacts',
      address: 'Address',
      services: 'Services',
      settings: 'Settings',
    };
  }, [t]);
  const { data: customerList } = customerHook.useEntity(id as string, {
    enabled: isEditMode,
  });
  useEffect(() => {
    if (customerList) {
      setCompanyName(customerList.companyName);
    }
  }, [customerList]);
  const pathForBreadCrumb = useMemo(() => {
    const tabKey = tab ? tab : 'general';
    return [
      {
        name: id ? companyName : 'Customers',
        path: ROUTES.CUSTOMER.CUSTOMER_CUSTOMERS,
      },
      {
        name: tabKey ? breadCrumbObj[tabKey || 'general'] : 'general',
        path: id
          ? ROUTES.CUSTOMER.CUSTOMER_TAB.replace(':id', id as string).replace(':tab', tabKey)
          : ROUTES.CUSTOMER.CUSTOMER_ADD.replace(':tab', tabKey),
      },
    ];
  }, [breadCrumbObj, companyName, id, tab]);

  return (
    <div>
      <div className="flex flex-col gap-1 avoid-tab-position">
        <div>
          <PageHeadingComponent
            title={idExists ? t('dashboard.editCustomer') : t('dashboard.addCustomer')}
            isChildComponent
          />
          <PageBreadCrumbsComponent path={pathForBreadCrumb} />
        </div>
        <TabsComponent tabs={DefaultTabsForCustomer(idExists)} />
      </div>
    </div>
  );
};
export default CustomerTabs;
