import { infoCircleOutlined } from '@/assets';
import { formErrorRegex } from '@/constant/Regex';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { Divider, Form, Input, InputRef, Select, Space, Switch, Tooltip, Typography } from 'antd';
import MaskedInput from 'antd-mask-input';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import { useCallback, useEffect, useState } from 'react';
import { useRef } from 'react';
import './ContactsOperations.css';
import { IAddContactForm, ValuesObject } from './contact.types';
import { useLanguage } from '@/hooks/useLanguage';
import { ICustomerContact } from '@/api/customer/customerContact/customerContact.types';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';

const ContactFormOperations: React.FC<IAddContactForm> = (props) => {
  const {
    form,
    onFinish,
    currentData,
    isAddContactModalOpen,
    setSelectedCategories,
    contactCategories,
  } = props;
  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);
  const [formData, setFormData] = useState<ICustomerContact>();
  const inputPhoneRef = useRef<InputRef>(null);
  const { t } = useLanguage();
  const maskingInputPhone = useCallback((value: string) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    setMaskPhoneInput(selectedCountryMask);
    inputPhoneRef?.current?.focus();
  }, []);

  useEffect(() => {
    if (currentData) {
      setFormData(currentData);
    }
  }, [currentData]);
  useEffect(() => {
    if (!formData) return;
    form.setFieldsValue({
      ...formData,
      phoneCountryCode: formData.phoneCountryCode,
      isActive: formData.isActive,
    });

    maskingInputPhone(formData?.phoneCountryCode);
  }, [form, formData, maskingInputPhone]);
  useEffect(() => {
    const data = formData?.phoneNumber;
    form.setFieldValue('phoneNumber', data);
  }, [form, formData?.phoneNumber, maskPhoneInput]);
  useEffect(() => {
    getCurrentCoordinates();
  }, []);
  function getCurrentCoordinates() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        const { latitude, longitude } = position.coords;
        getCountryFromCoordinates(latitude, longitude);
      });
    }
  }

  const getCountryFromCoordinates = async (latitude: number, longitude: number) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`
      );
      const data = await response.json();

      if (data && data.address) {
        const country = data.address.country;
        optionsForPrefix?.find((item) => {
          const countryMatches = item.geoCountryCode === country;
          if (countryMatches && !isAddContactModalOpen.isEdit) {
            form.setFieldValue('phoneCountryCode', item.label);
            maskingInputPhone(item.value);
          }
        });
      }
    } catch (error) {
      console.error('Error fetching country:', error);
    }
  };
  const handleCategoryOnChange = (value: (string | ValuesObject)[], values: ValuesObject[]) => {
    const formattedCategories = value.map((category) => {
      if (typeof category === 'string') {
        const existingCategory = values.find(
          (item) => item.value === category || item.label === category
        );
        if (existingCategory) {
          return { id: existingCategory.value, name: existingCategory.label };
        } else {
          return { name: category };
        }
      } else {
        return category;
      }
    });
    setSelectedCategories(formattedCategories as ValuesObject[]);
  };
  const phoneNumberPrefix = (
    <Form.Item className="contact-general-maskedInput" name={'phoneCountryCode'}>
      <Select
        placeholder="USA +1"
        options={optionsForPrefix}
        onChange={(value) => maskingInputPhone(value)}
      />
    </Form.Item>
  );
  return (
    <div className="text-2xl font-bold p-2 ">
      <Form
        name="add-contact-form"
        layout="vertical"
        form={form}
        onFinish={onFinish}
        className="contact-general-add-form"
      >
        <div className="flex flex-col">
          <div className="grid grid-cols-2 gap-x-5">
            <Form.Item
              rules={[
                {
                  required: true,
                  message: `${t('dashboard.customer.columns.pleaseEnterYourName')}`,
                },
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: `${t('common.errors.noMultipleWhiteSpace')}`,
                },
                {
                  pattern: formErrorRegex.NoSpecialCharacters,
                  message: `${t('common.errors.noSpacialCharacters')}`,
                },
              ]}
              labelAlign="left"
              className="contact-general-form-item"
              name={'name'}
              label={t('zonePage.colDefs.name')}
            >
              <Input
                min={3}
                maxLength={255}
                className="contact-general-input"
                placeholder="Jhon doe"
              />
            </Form.Item>
            <Space.Compact className="combined-masked-input customer-general-form-maskedItem !w-[100%]">
              <Form.Item
                dependencies={['phoneCountryCode']}
                rules={[
                  {
                    validator: validateCountryAndValue(
                      form,
                      'phoneCountryCode',
                      'phone number',
                      true
                    ),
                  },
                  {
                    validator: (_, value) =>
                      validateMaskedInput(
                        value,
                        maskPhoneInput.length,
                        t('addressPage.operationalForm.validPhoneNumberError')
                      ),
                  },
                ]}
                required
                className="contact-general-form-maskedItem !mb-[0px]"
                name="phoneNumber"
                label={t('addressPage.operationalForm.phoneNumber')}
              >
                <MaskedInput
                  ref={inputPhoneRef}
                  addonBefore={phoneNumberPrefix}
                  className="contact-general-maskedInput"
                  placeholder="(*************"
                  mask={maskPhoneInput.mask}
                />
              </Form.Item>
              <Form.Item name="phoneExtension" className="w-[25%] !mb-0">
                <Input
                  onKeyDown={numberFieldValidator}
                  placeholder="00000"
                  maxLength={6}
                  className="customer-general-input mt-[30px]"
                />
              </Form.Item>
            </Space.Compact>

            <Form.Item
              rules={[
                {
                  required: true,
                  message: `${t('auth.emailRequired')}`,
                },
                {
                  pattern: formErrorRegex.ValidEmailOrNot,
                  message: `${t('auth.invalidEmail')}`,
                },
              ]}
              className="contact-general-form-item "
              name={'email'}
              label="Email"
            >
              <Input
                maxLength={255}
                className="contact-general-input"
                placeholder="<EMAIL>"
              />
            </Form.Item>
            <Form.Item
              className="contact-general-form-item"
              name={'categories'}
              rules={[
                { required: false },
                {
                  pattern: formErrorRegex.NoMultipleWhiteSpaces,
                  message: t('common.errors.noMultipleWhiteSpace'),
                },
                {
                  validator: (_, value) => {
                    if (!value || value.length === 0) {
                      return Promise.resolve();
                    }
                    const invalidTags = value?.filter((tag: string) => tag.length > 255);
                    if (invalidTags.length > 0) {
                      return Promise.reject(
                        new Error(t('priceModifiers.maximumValueExceeded', { max: 255 }))
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
              label={
                <span className="flex gap-1">
                  {t('dashboard.customer.columns.department')}
                  <Tooltip title={t('dashboard.customer.selectOrAddDepartment')}>
                    <img src={infoCircleOutlined} alt="info" />
                  </Tooltip>
                </span>
              }
            >
              <Select
                maxTagTextLength={10}
                prefixCls="custom-select"
                mode="tags"
                placeholder={t('dashboard.customer.columns.selectOrAddDepartment')}
                style={{ width: '100%' }}
                tokenSeparators={[',']}
                options={contactCategories}
                onChange={(value, valueString) =>
                  handleCategoryOnChange(value, valueString as ValuesObject[])
                }
              />
            </Form.Item>
          </div>
          <Form.Item
            name="isActive"
            layout="horizontal"
            className="contact-active-key-item"
            label={
              <span className="flex gap-2 ">
                {t('dashboard.customer.columns.contactActive')}
                <Tooltip title={t('dashboard.customer.columns.selectAppropriateCategory')}>
                  <img src={infoCircleOutlined} alt="info" />
                </Tooltip>
              </span>
            }
          >
            <Switch defaultChecked className="" />
          </Form.Item>
        </div>
        <Divider orientation="left" orientationMargin={0} className="permission-divider">
          {t('dashboard.customer.columns.permissionSetting')}
        </Divider>
        <Typography.Title className="contact-permission-typography">
          {t('dashboard.customer.columns.permission')}
        </Typography.Title>
        <Typography.Text className="!font-normal" type="secondary">
          {t('dashboard.customer.columns.permissionSettingDescription')}
        </Typography.Text>
        <div className="flex   w-full justify-between">
          <Form.Item
            name="address"
            layout="horizontal"
            className="contact-switch-key-item"
            label={
              <span className="flex gap-2 font-[600]">
                {t('sidebar.address')}
                <Tooltip title={t('dashboard.customer.allowsAddressAccess')}>
                  <img src={infoCircleOutlined} alt="info" />
                </Tooltip>
              </span>
            }
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name={'prices'}
            layout="horizontal"
            className="contact-switch-key-item"
            label={
              <span className="flex gap-2 font-[600]">
                {t('sidebar.prices')}
                <Tooltip title={t('dashboard.customer.allowsPriceAccess')}>
                  <img src={infoCircleOutlined} alt="info" />
                </Tooltip>
              </span>
            }
          >
            <Switch />
          </Form.Item>
          <Form.Item
            name={'invoice'}
            className="contact-switch-key-item"
            layout="horizontal"
            label={
              <span className="flex gap-2 font-[600]">
                {t('dashboard.customer.columns.invoices')}
                <Tooltip title={t('dashboard.customer.allowsInvoiceAccess')}>
                  <img src={infoCircleOutlined} alt="info" />
                </Tooltip>
              </span>
            }
          >
            <Switch />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default ContactFormOperations;
