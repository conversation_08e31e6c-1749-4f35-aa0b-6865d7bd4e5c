.contact-switch-key-item > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}
.contact-general-add-form {
  @apply w-full flex flex-col gap-3;
}
.contact-general-form-item {
  @apply mb-[15px];
}
.contact-active-key-item {
  @apply mb-0;
}
.permission-divider {
  @apply !m-0;
}
.contact-switch-key-item {
  @apply mb-0  w-[30%] bg-[#F0FAFF] p-2 rounded-[8px];
}
.contact-switch-key-item > .ant-row > .ant-form-item-control {
  display: flex;
  align-items: end;
}
.permission-divider > span {
  @apply text-[400] text-[14px] text-[#96A9B1];
}
.contact-permission-typography {
  @apply !text-[18px] !font-[600] !mb-0;
}

.contact-general-form-item > .ant-form-item-row > .ant-form-item-label > label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  font-weight: 600;
}
.contact-general-form-item > .ant-form-item-row > .ant-form-item-label {
  font-size: 14px;
  font-weight: 500;
  font-family: var(--font-family);
}
.contact-general-form-item > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}
.contact-general-input {
  @apply h-[40px] font-[400];
}
.contact-general-maskedInput {
  @apply mb-0;
}
.contact-general-form-maskedItem > .ant-form-item-row > .ant-form-item-label > label {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 5px;
  font-weight: 600;
}
.contact-general-form-maskedItem > .ant-form-item-row > .ant-form-item-label {
  font-size: 14px;
  font-weight: 500;
}
.contact-general-form-maskedItem > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}
.contact-general-maskedInput > .ant-input-wrapper > .ant-input-group-addon {
  background: #f8fbfc;
  height: 40px;
  padding: 0px;
  @apply 3xsm:w-[40%] md:w-[35%] lg:w-[35%] 2xl:w-[20%];
}
.contact-general-maskedInput > .ant-input-wrapper > .ant-input-outlined {
  height: 40px;
  font-weight: 400;
}
.ant-form-item-has-error
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .contact-general-maskedInput {
  border-color: #ff4d4f;
  border-radius: 7px;
  border-width: 1px;
  border-style: solid;
}
.ant-form-item-has-error
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .contact-general-maskedInput
  > .ant-input-wrapper
  > input:hover {
  border-color: #e5e7eb;
}
.ant-form-item-has-error
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .contact-general-maskedInput
  > .ant-input-wrapper
  > input:focus {
  border-color: #e5e7eb;
  box-shadow: none;
}
.ant-form-item-has-error
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .contact-general-maskedInput:hover {
  border-color: #ffa39e;
}
.contact-active-key-item {
  @apply w-[100%] rounded-[8px] bg-[#F0FAFF] p-2;
}
.contact-active-key-item
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content {
  @apply flex justify-end;
}

.contact-active-key-item > .ant-row > .ant-col {
  font-size: 14px;
  font-weight: 500;
}
.contact-active-key-item > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}
.contact-active-key-item
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .ant-switch-checked {
  background: #088dc1;
}
.contact-switch-key-item
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .ant-switch-checked {
  background: #088dc1;
}
