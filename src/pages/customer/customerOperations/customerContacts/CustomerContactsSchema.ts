// import { Schema } from '@/lib/fakerLib/types';

export const CustomerContactsSchema = {
  name: {
    type: 'string',
    faker: 'person.fullName',
  },
  phone: {
    type: 'string',
    faker: 'phone.number',
  },
  email: {
    type: 'email',
    faker: 'internet.email',
  },
  status: {
    type: 'string',
    faker: 'datatype.boolean',
  },
  invoice: {
    type: 'string',
    faker: 'datatype.boolean',
  },
  prices: {
    type: 'string',
    faker: 'datatype.boolean',
  },
  locations: {
    type: 'string',
    faker: 'datatype.boolean',
  },
  dateAdded: {
    type: 'date',
    faker: 'date.past',
  },
  dateUpdated: {
    type: 'date',
    faker: 'date.past',
  },
  lastUpdatedBy: {
    type: 'string',
    faker: 'person.fullName',
  },
};
