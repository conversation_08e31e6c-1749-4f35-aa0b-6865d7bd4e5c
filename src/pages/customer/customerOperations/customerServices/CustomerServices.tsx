import React, { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { Button } from 'antd';
import { AgGridReact } from 'ag-grid-react';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { IColDef } from '@/types/AgGridTypes';
import { ICellRendererParams, SelectionChangedEvent } from 'ag-grid-community';
import {
  ICustomerService,
  ICustomerServiceComponentProps,
  ISelectedKeys,
  IServiceSearchHighlight,
} from './customerServiceTypes';
import { useLanguage } from '@/hooks/useLanguage';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { EmptyStatePage } from '@/components/common/emptyState/EmptyStatePage';
import { LeftToRightOutlinedIcon, RightToLeftOutlined } from '@/assets';
import { searchServices } from './servicesApi';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { GridIdConstant } from '@/constant/GridIdConstant';

const CustomerServicesComponent: React.FC<ICustomerServiceComponentProps> = (props) => {
  const { setIsEdit, allServices, getAllServices } = props;
  const [availableData, setAvailableData] = useState<ICustomerService[]>([]);
  const [selectedData, setSelectedData] = useState<ICustomerService[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<ISelectedKeys>({
    available: [],
    selected: [],
  });
  const availableGridRef = useRef<AgGridReact>(null);
  const selectedGridRef = useRef<AgGridReact>(null);
  const [rowData, setRowData] = useState<ICustomerService[]>([]);
  const { t } = useLanguage();
  const [searchText, setSearchText] = useState<IServiceSearchHighlight>({
    searchTextForAvailable: '',
    searchTextForSelected: '',
    searchTextForAssigned: '',
  });
  const [serviceFilterForAvailable, setServiceFilterForAvailable] = useState<ICustomerService[]>(
    []
  );

  const [serviceFilterForSelected, setServiceFilterForSelected] = useState<ICustomerService[]>([]);

  const onSelectionChangeHandler = (
    params: SelectionChangedEvent<ICustomerService>,
    key: keyof typeof selectedKeys
  ): void => {
    const selectedRows = params.api.getSelectedRows();
    const selectedIds = selectedRows?.map((row) => row.id);
    setRowData(selectedRows);
    setSelectedKeys({ ...selectedKeys, [key]: selectedIds });
  };

  const handleSave = async () => {
    if (!allServices) return;

    allServices?.forEach((service) => {
      const isInclude = selectedData.some((item) => item.id === service.id);
      service.isSelected = isInclude;
    });
    await getAllServices();
    setIsEdit(false);
  };
  const customerServiceColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'serviceName',
        headerName: t('dashboard.customer.services.colDefs.serviceName'),
        unSortIcon: true,
        tooltipField: 'serviceName',
        type: 'string',
        visible: true,
        flex: 1,
        minWidth: 250,
        cellRenderer: (params: ICellRendererParams<ICustomerService>) => {
          const isAvailableGrid = params.api === availableGridRef.current?.api;
          const relevantSearchText = isAvailableGrid
            ? searchText.searchTextForAvailable
            : searchText.searchTextForSelected;

          return (
            (relevantSearchText && highlightText(params.value, relevantSearchText)) ||
            params.data?.serviceName
          );
        },
      },
      {
        field: 'serviceLevel',
        headerName: t('dashboard.customer.services.colDefs.serviceLevel'),
        unSortIcon: true,
        visible: true,
        flex: 1,
        minWidth: 250,
        type: 'string',
        cellRenderer: (params: ICellRendererParams<ICustomerService>) => {
          const isAvailableGrid = params.api === availableGridRef.current?.api;
          const relevantSearchText = isAvailableGrid
            ? searchText.searchTextForAvailable
            : searchText.searchTextForSelected;

          return (
            <div>
              {(relevantSearchText && highlightText(params.value, relevantSearchText)) || (
                <label>{params.data?.serviceLevel}</label>
              )}
            </div>
          );
        },
      },
    ];
  }, [searchText.searchTextForAvailable, searchText.searchTextForSelected, t]);
  const moveToSelected = () => {
    if (!availableGridRef.current) return;
    if (selectedKeys?.available.length === 0) return;
    const avail = availableData.filter((item) => !selectedKeys.available.includes(item.id));
    const selected = availableData.filter((item) => selectedKeys.available.includes(item.id));

    setAvailableData(avail);
    setSelectedData((prev) => [...prev, ...selected]);
    setServiceFilterForSelected((prev) => [...prev, ...rowData]);
    setSelectedKeys({ available: [], selected: [] });
    setRowData([]);
  };
  const moveToAvailable = () => {
    if (!selectedGridRef.current) return;
    if (selectedKeys.selected.length === 0) return;
    setSelectedData((prev) => prev.filter((item) => !selectedKeys.selected.includes(item.id)));
    setServiceFilterForSelected((prev) =>
      prev.filter((item) => !selectedKeys.selected.includes(item.id))
    );
    setAvailableData((prev) => [...prev, ...rowData]);
    setSelectedKeys({ available: [], selected: [] });
    setRowData([]);
  };
  const searchHandlerAvailable = useCallback(
    (value: string) => {
      const results = searchServices(serviceFilterForAvailable, {
        query: value,
      });
      setSearchText((prev) => ({ ...prev, searchTextForAvailable: value }));
      setAvailableData(results);
    },
    [serviceFilterForAvailable]
  );

  const searchHandlerSelected = useCallback(
    (value: string) => {
      const results = searchServices(serviceFilterForSelected, {
        query: value,
      });
      setSearchText((prev) => ({ ...prev, searchTextForSelected: value }));
      setSelectedData(results);
    },
    [serviceFilterForSelected]
  );

  useEffect(() => {
    if (allServices) {
      setAvailableData(allServices);
      setServiceFilterForAvailable(allServices);
    }
  }, [allServices]);
  return (
    <div>
      {allServices && allServices.length > 0 ? (
        <div className="flex flex-col">
          <PageHeadingComponent
            onBackClick={() => setIsEdit(false)}
            title={t('dashboard.customer.services.assignServices')}
            parentClassName="flex gap-[10px] pt-2"
            isChildComponent={true}
            classNameChildren="w-[24px] h-[24px] flex justify-center align-middle mt-1"
            classNameTitle="text-[20px] font-[600]"
          />
          <div className="flex flex-col md:flex-row gap-5 align-middle w-full pr-6">
            <div className="ag-theme-alpine flex flex-col gap-3 w-full md:w-[47%]">
              <div className="flex justify-between align-middle">
                <PageHeadingComponent
                  title={t('dashboard.customer.services.unassignedServices')}
                  classNameTitle="text-[20px] font-[600]"
                  children={
                    <SearchFilterComponent
                      advanceFilter={false}
                      searchedValues={searchHandlerAvailable}
                      colDefs={customerServiceColDefs}
                      className="!pt-2"
                      searchInputPlaceholder={t('dashboard.customer.services.searchServiceName')}
                    />
                  }
                />
              </div>
              <CustomAgGrid
                gridId={GridIdConstant.GRID_WRAPPER_FOR_GROUP_MODIFIER}
                className={`3xsm:!h-[50vh] md:!h-[60vh] lg:!h-[65vh] 3xl:!h-[66vh] rounded-md`}
                gridRef={availableGridRef}
                columnDefs={customerServiceColDefs}
                rowSelection={{
                  mode: 'multiRow',
                  headerCheckbox: !(selectedKeys.selected.length > 0),
                  isRowSelectable: () => !(selectedKeys.selected.length > 0),
                }}
                rowData={availableData}
                onSelectionChanged={(params) => onSelectionChangeHandler(params, 'available')}
                nullStateClassName={availableData.length === 0 ? 'border border-gray-300' : ''}
                emptyState={{
                  title: t('dashboard.customer.services.noServices'),
                  description: t('dashboard.customer.services.noServicesAvailableToAssign'),
                }}
              />
            </div>

            <div className="flex flex-col gap-3 justify-center">
              <Button
                onClick={moveToSelected}
                icon={<LeftToRightOutlinedIcon />}
                disabled={selectedKeys.available.length === 0}
              />

              <Button
                onClick={moveToAvailable}
                icon={<RightToLeftOutlined />}
                disabled={selectedKeys.selected.length === 0}
              />
            </div>

            <div className="ag-theme-alpine flex flex-col gap-3 w-full md:w-[47%]">
              <div className="flex justify-between align-middle">
                <PageHeadingComponent
                  title={t('dashboard.customer.services.assignedServices')}
                  classNameTitle="text-[20px] font-[600]"
                  children={
                    <SearchFilterComponent
                      advanceFilter={false}
                      searchedValues={searchHandlerSelected}
                      colDefs={customerServiceColDefs}
                      className="!pt-2"
                      searchInputPlaceholder={t('dashboard.customer.services.searchServiceName')}
                    />
                  }
                />
              </div>
              <CustomAgGrid
                gridId={GridIdConstant.GRID_WRAPPER_FOR_GROUP_MODIFIER}
                className={`3xsm:!h-[50vh] md:!h-[60vh] lg:!h-[65vh] 3xl:!h-[66vh] rounded-md `}
                gridRef={selectedGridRef}
                rowData={selectedData}
                onSelectionChanged={(params) => onSelectionChangeHandler(params, 'selected')}
                rowSelection={{
                  mode: 'multiRow',
                  headerCheckbox: !(selectedKeys.available.length > 0),
                  isRowSelectable: () => !(selectedKeys.available.length > 0),
                }}
                columnDefs={customerServiceColDefs}
                nullStateClassName={selectedData.length === 0 ? 'border border-gray-300' : ''}
                emptyState={{
                  title: t('dashboard.customer.services.noServicesAssigned'),
                  description: '',
                }}
              />
            </div>
          </div>
          <div className="w-full flex justify-start mt-4">
            <Button
              type="primary"
              className="mt-2 bg-primary-600 hover:!bg-primary-600"
              disabled={!selectedData.length}
              onClick={handleSave}
            >
              {t('common.save')}
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center h-[80vh]">
          <EmptyStatePage
            title={t('dashboard.customer.services.noServicesAvailable')}
            description=""
          />
        </div>
      )}
    </div>
  );
};

export default CustomerServicesComponent;
