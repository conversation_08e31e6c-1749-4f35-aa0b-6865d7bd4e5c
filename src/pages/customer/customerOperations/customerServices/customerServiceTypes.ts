import { IGetAssignedPriceSets } from '@/api/customer/assignedPriceSets/assignedPriceSet.types';
import { IZoneLookupRow } from '@/pages/location/zone/list/zone.type';
import { IColDef } from '@/types/AgGridTypes';

export interface ICustomerService {
  id: string;
  serviceName: string;
  serviceLevel: string;
  isSelected: boolean;
  paymentOption: string;
  notes: string;
  assignedCustomers?: string[];
  description: string;
  basePriceByZone?: {
    data: IZoneLookupRow[];
  };
  createdAt: string;
  updatedAt: string;
  lastUpdateBy: string;
}
export interface ICustomerServiceComponentProps {
  setIsEdit: React.Dispatch<React.SetStateAction<boolean>>;
  allServices: ICustomerService[] | undefined;
  getAllServices: () => Promise<void>;
}
export interface IServiceSearchHighlight {
  searchTextForAvailable: string;
  searchTextForSelected: string;
  searchTextForAssigned: string;
}
export interface ISelectedKeys {
  available: string[];
  selected: string[];
}

export interface ICustomerServiceGridProps {
  setIsEdit: React.Dispatch<React.SetStateAction<boolean>>;
  allServices: IGetAssignedPriceSets[] | undefined;
  setSearchText: React.Dispatch<React.SetStateAction<IServiceSearchHighlight>>;
  colDefs: IColDef[];
  searchText: IServiceSearchHighlight;
  noServiceAvailableInSystem: boolean;
}
