import { ICustomerService } from './customerServiceTypes';
interface SearchCondition {
  query: string;
}
export function searchServices(
  data: ICustomerService[],
  condition: SearchCondition
): ICustomerService[] {
  const { query } = condition;
  const normalizedQuery = query.toLowerCase();

  return data.filter((item) => {
    return (
      item.serviceName?.toLowerCase().includes(normalizedQuery) ||
      item.serviceLevel?.toLowerCase().includes(normalizedQuery)
    );
  });
}
