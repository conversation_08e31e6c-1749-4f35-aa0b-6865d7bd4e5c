import { Tabs } from 'antd';
import { mobileSmsIcon, emailIcon } from '@/assets';
import NotificationTabContent from './NotificationTabContent';
import { INotificationSettings, INotificationSettingsData, ISettingTabs } from './Settings.types';
import { useEffect, useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { updateSetting, useGetSettings } from '@/api/settings/settings.service';
import { SettingsKeys } from '@/constant/SettingsKeys';
import { useParams } from 'react-router-dom';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { SettingsScope } from '@/constant/SettingsScope';
const SettingsNotificationComponent = () => {
  const { t } = useLanguage();
  const { id } = useParams<{ id: string }>();
  const [notificationStatusData, setNotificationStatusData] = useState<INotificationSettingsData>(
    {}
  );
  const [currentKey, setCurrentKey] = useState<string>('sms');
  const notificationManager = useNotificationManager();

  const { data: notificationSettingsData } = useGetSettings(
    SettingsKeys.customerNotificationSettings,
    id,
    {
      enabled: Boolean(!!id && SettingsKeys.customerNotificationSettings),
    }
  );
  const notificationTypes: ISettingTabs[] = [
    {
      key: 'sms',
      label: t('dashboard.customer.settings.settingsNotification.sms'),
      icon: mobileSmsIcon,
    },
    {
      key: 'email',
      label: t('dashboard.customer.settings.settingsNotification.email'),
      icon: emailIcon,
    },
  ];
  useEffect(() => {
    if (notificationSettingsData?.value) {
      setNotificationStatusData(notificationSettingsData?.value);
    }
  }, [notificationSettingsData]);
  const handleSaveNotificationSettings = async (data: INotificationSettings, key: string) => {
    const changedValues = { ...notificationStatusData, [key]: data };
    const formattedData = {
      userId: id,
      scope: SettingsScope.USER,
      key: SettingsKeys.customerNotificationSettings,
      value: changedValues,
    };
    await updateSetting(notificationSettingsData?.id, formattedData);

    notificationManager.success({
      message: t('common.success'),
      description: t(
        'dashboard.customer.settings.settingsNotification.notificationSettingsUpdated'
      ),
    });
  };
  const renderTabContent = (key: string) => {
    let filteredTabData = {};
    switch (key) {
      case 'sms':
        filteredTabData = notificationStatusData.sms;
        break;
      case 'email':
        filteredTabData = notificationStatusData.email;
        break;
      default:
        filteredTabData = notificationStatusData.sms;
        break;
    }
    if (filteredTabData) {
      return (
        <NotificationTabContent
          notificationStatusData={filteredTabData}
          handleSaveNotificationSettings={handleSaveNotificationSettings}
          currentSelectedKey={currentKey}
        />
      );
    }
  };
  return (
    <div className="flex flex-col  p-2">
      <div className="flex flex-col gap-2 pl-3">
        <span className="text-[14px] font-semibold">
          {t('dashboard.customer.settings.settingsNotification.notificationSettings')}
        </span>
        <span className="text-[14px] text-[#647A83]">
          {t('dashboard.customer.settings.settingsNotification.notificationSettingsDescription')}{' '}
        </span>
      </div>
      <div className="p-2">
        <Tabs
          className="settings-notification-tab"
          defaultActiveKey="sms"
          onChange={(e) => setCurrentKey(e)}
        >
          {notificationTypes.map(({ key, label, icon }) => (
            <Tabs.TabPane
              key={key}
              tab={
                <div className="flex items-center gap-2">
                  <img className="inline" src={icon} alt="" />
                  <span>{label}</span>
                </div>
              }
            >
              {renderTabContent(key)}
            </Tabs.TabPane>
          ))}
        </Tabs>
      </div>
    </div>
  );
};

export default SettingsNotificationComponent;
