import { Tabs } from 'antd';
import './CustomerSettings.css';
import SettingsGeneralComponent from './SettingsGeneral';
import SettingsNotificationComponent from './SettingsNotification';
import SettingsUIConfiguration from './SettingsUIConfiguration';
import { useLanguage } from '@/hooks/useLanguage';

const CustomerSettingsComponent = () => {
  const { t } = useLanguage();
  const tabs = [
    { label: t('sidebar.general'), key: 'general', children: <SettingsGeneralComponent /> },
    {
      label: t('dashboard.customer.settings.notification'),
      key: 'notification',
      children: <SettingsNotificationComponent />,
    },
    {
      label: t('dashboard.customer.settings.uiConfiguration'),
      key: 'UiConfiguration',
      children: <SettingsUIConfiguration />,
    },
  ];

  return (
    <div className="p-2">
      <div className="h-full w-full p-2 rounded-md border-[1px] border-[#CDD7DB]">
        <Tabs className="customer-settings-tab" tabPosition="left" items={tabs} />
      </div>
    </div>
  );
};

export default CustomerSettingsComponent;
