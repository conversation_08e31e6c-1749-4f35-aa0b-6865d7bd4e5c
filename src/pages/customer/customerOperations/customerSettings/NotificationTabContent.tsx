'use client';

import { useEffect, useState } from 'react';
import { <PERSON>ton, Collapse, Switch, theme } from 'antd';
import { ExpandCollapseIcon, ExpandedDownCollapseIcon, infoCircleOutlined } from '@/assets';
import { INotificationSettings, INotificationTabContentProps, ToggleState } from './Settings.types';
import { useLanguage } from '@/hooks/useLanguage';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';

const NotificationTabContent: React.FC<INotificationTabContentProps> = (props) => {
  const { t } = useLanguage();
  const { token } = theme.useToken();

  const { notificationStatusData, handleSaveNotificationSettings, currentSelectedKey } = props;
  const panelStyle: React.CSSProperties = {
    marginBottom: 15,
    borderRadius: token.borderRadiusLG,
    border: 'none',
  };
  useEffect(() => {
    if (!notificationStatusData) return;
    setSettings(notificationStatusData);
  }, [notificationStatusData]);
  const [settings, setSettings] = useState<INotificationSettings>({});
  const handleToggle = (section: string, subsection: string, key: string) => {
    if (!settings) return;
    const updatedSettings = { ...settings };
    const toggleValue = updatedSettings[section]?.[subsection]?.[key];
    if (toggleValue !== undefined) {
      updatedSettings[section][subsection][key] = !toggleValue;
      setSettings(updatedSettings);
    }
  };
  const toolTipContent = (subsection: string) => {
    let subsectionContent = '';
    switch (subsection) {
      case 'orderEdit':
        subsectionContent = t('dashboard.customer.settings.settingsNotification.orderUpdated');
        break;
      case 'orderPickup':
        subsectionContent = t('dashboard.customer.settings.settingsNotification.orderPickedUp');
        break;
      case 'orderPlaced':
        subsectionContent = t('dashboard.customer.settings.settingsNotification.orderCreated');
        break;
      case 'orderCancelled':
        subsectionContent = t('dashboard.customer.settings.settingsNotification.orderCancelled');
        break;
      case 'orderCompleted':
        subsectionContent = t('dashboard.customer.settings.settingsNotification.orderDelivered');
        break;
    }

    return (
      <div className="flex flex-col max-w-[250px] text-wrap">
        <span className="capitalize font-semibold">
          {subsection.replace(/([A-Z])/g, ' $1').trim()}
        </span>
        <span>{subsectionContent}</span>
      </div>
    );
  };
  const handleEnableAll = (section: string, enabled: boolean) => {
    const newSettings = { ...settings };

    Object.keys(newSettings[section]).forEach((subsection) => {
      Object.keys(newSettings[section][subsection]).forEach((key) => {
        newSettings[section][subsection][key] = enabled;
      });
    });
    setSettings(newSettings);
  };

  const renderToggles = (section: string, subsection: string, toggles: ToggleState) => {
    return (
      <div className="space-y-4  px-4 py-2">
        {Object.entries(toggles).map(([key, value]) => (
          <div key={key} className="flex items-center justify-between order-role-card">
            <span className="capitalize">{key}</span>

            <Switch
              checked={value}
              onChange={() => handleToggle(section, subsection, key)}
              className="order-role-switch"
            />
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col justify-between">
      {Object.entries(settings).map(([section, sectionData]) => (
        <div key={section} className="border-b-2">
          <div className="flex items-center justify-between pr-4 py-3 order-status-card">
            <span className="font-semibold capitalize">{section}</span>
            <div className="flex gap-3">
              <span className="font-semibold">
                {t('dashboard.customer.settings.settingsNotification.enableAll')}
              </span>
              <Switch
                checked={Object.values(sectionData).every((subsection) =>
                  Object.values(subsection).every((value) => value)
                )}
                onChange={(checked) => handleEnableAll(section, checked)}
                className="order-status-switch"
              />
            </div>
          </div>
          <Collapse
            bordered={false}
            expandIconPosition={'end'}
            expandIcon={({ isActive }) =>
              isActive ? <ExpandedDownCollapseIcon /> : <ExpandCollapseIcon />
            }
            className="settings-collapsed"
            items={Object.entries(sectionData).map(([subsection, toggles]) => ({
              key: subsection,
              label: (
                <div className="flex gap-2">
                  <span className="capitalize font-semibold">
                    {subsection.replace(/([A-Z])/g, ' $1').trim()}{' '}
                  </span>
                  <CustomTooltip placement="bottom" content={toolTipContent(subsection)}>
                    {' '}
                    <img
                      className="w-[14px] h-[14px] mt-[4px]"
                      src={infoCircleOutlined}
                      alt="info"
                    />
                  </CustomTooltip>
                </div>
              ),
              children: renderToggles(section, subsection, toggles),
              style: panelStyle,
            }))}
          />
        </div>
      ))}
      <div className="sticky bottom-0 flex items-center p-5 bg-white">
        <Button
          className="bg-[#0876A4] text-white hover:!bg-[#0876A4] hover:!text-white p-5"
          type="primary"
          onClick={() => handleSaveNotificationSettings(settings, currentSelectedKey)}
        >
          {t('dashboard.customer.settings.settingsNotification.saveSettings')}
        </Button>
      </div>
    </div>
  );
};

export default NotificationTabContent;
