.customer-settings-tab
  > .ant-tabs-nav
  > .ant-tabs-nav-wrap
  > .ant-tabs-nav-list
  > .ant-tabs-ink-bar-animated {
  display: none;
}
.customer-settings-tab > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-tab {
  margin: 0px;
}
.customer-settings-tab
  > .ant-tabs-nav
  > .ant-tabs-nav-wrap
  > .ant-tabs-nav-list
  > .ant-tabs-tab-active
  > .ant-tabs-tab-btn {
  color: var(--primary-600);
}
.customer-settings-tab
  > .ant-tabs-nav
  > .ant-tabs-nav-wrap
  > .ant-tabs-nav-list
  > .ant-tabs-tab:hover {
  color: var(--primary-600);
}
.customer-settings-tab > .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane-active {
  padding: 0px !important;
}

.settings-notification-tab > .ant-tabs-nav::before {
  display: none !important;
}
.settings-notification-tab
  > .ant-tabs-nav
  > .ant-tabs-nav-wrap
  > .ant-tabs-nav-list
  > .ant-tabs-tab
  > .ant-tabs-tab-btn
  > .ant-tabs-tab-icon {
  margin-inline-end: 8px;
}
.settings-notification-tab
  > .ant-tabs-nav
  > .ant-tabs-nav-wrap
  > .ant-tabs-nav-list
  > .ant-tabs-ink-bar-animated {
  width: 80px !important;
  background: var(--primary-600) !important;
}
.settings-notification-tab
  > .ant-tabs-nav
  > .ant-tabs-nav-wrap
  > .ant-tabs-nav-list
  > .ant-tabs-tab-active
  > .ant-tabs-tab-btn {
  color: black;
}
.settings-collapsed {
  @apply bg-white;
}
.settings-collapsed > .ant-collapse-item-active > .ant-collapse-header {
  background: #f0faff;
  border-bottom: 1px solid #cdd7db !important;
}
.settings-collapsed > .ant-collapse-item {
  border: 1px solid #cdd7db !important;
  padding: 2px;
  background: #ffffff !important;
}
.settings-collapsed > .ant-collapse-item > .ant-collapse-content {
  background: #ffffff;
}

.settings-collapsed > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
  padding: 0px;
}
.settings-notification-tab > .ant-tabs-content-holder {
  max-height: 100%;
  @apply h-[59vh] lg:h-[60vh];
  overflow-y: auto;
}
.customer-settings-tab > .ant-tabs-content-holder {
  height: 76vh;
  max-height: 100%;
}
.order-status-card .ant-switch-checked {
  background: var(--primary-600) !important;
}
.order-role-card > .ant-switch-checked {
  background: var(--primary-600) !important;
}
.switch-container > .ant-switch-checked {
  background: var(--primary-600) !important;
}
.settings-notification-tab > .ant-tabs-content-holder > .ant-tabs-content,
.settings-notification-tab > .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
  height: 100%;
}
.delivery-collection-content .ant-switch-checked {
  background: var(--primary-600) !important;
}
.required-visible-switch > .ant-form-item-row > .ant-form-item-label > label::after {
  display: none;
}
.required-visible-switch > .ant-row {
  display: flex;
  gap: 10px;
}
.package-type-content .ant-switch-checked {
  background: var(--primary-600) !important;
}
.package-type-inputnumber {
  @apply w-full h-[40px];
}
.package-type-inputnumber > .ant-input-number-input-wrap {
  display: flex;
  justify-content: center;
  height: 100%;
}
.package-type-inputnumber .ant-input-number-focused {
  border-color: var(--primary-600) !important;
}
.package-type-form-item
  > .ant-row
  > .ant-form-item-control
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .ant-input-number-focused {
  border-color: var(--primary-600) !important;
}
.package-type-inputnumber:hover {
  border-color: var(--primary-600) !important;
}
.package-type-inputnumber:disabled {
  background-color: #f8fbfc;
  color: #647a83;
}
.checkbox-container > .ant-checkbox-checked > .ant-checkbox-inner {
  background-color: #0876a4 !important;
  border-color: #0876a4 !important;
}
.checkbox-form-item
  > .ant-row
  > .ant-col
  > .ant-form-item-control-input
  > .ant-form-item-control-input-content
  > .ant-checkbox-wrapper-disabled
  > .ant-checkbox-checked
  > .ant-checkbox-inner {
  background-color: #cdd7db !important;
  border-color: #cdd7db !important;
}
