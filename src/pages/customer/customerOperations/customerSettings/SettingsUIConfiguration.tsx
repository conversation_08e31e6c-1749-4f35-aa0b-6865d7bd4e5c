import { Checkbox, Form } from 'antd';
import { ISettingsUiConfigData, ISwitchChanged } from './Settings.types';
import { useEffect, useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { updateSetting, useGetSettings } from '@/api/settings/settings.service';
import { SettingsKeys } from '@/constant/SettingsKeys';
import { useParams } from 'react-router-dom';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { SettingsScope } from '@/constant/SettingsScope';

const SettingsUIConfiguration = () => {
  const { id } = useParams<{ id: string }>();
  const { data: UiConfigurationData } = useGetSettings(
    SettingsKeys.customerUIConfigurationSettings,
    id,
    {
      enabled: Boolean(!!id && SettingsKeys.customerUIConfigurationSettings),
    }
  );
  const notificationManager = useNotificationManager();

  const [form] = Form.useForm();
  const { t } = useLanguage();
  const [configData, setConfigData] = useState<ISettingsUiConfigData[]>([]);
  useEffect(() => {
    if (UiConfigurationData) {
      setConfigData(UiConfigurationData?.value?.UIConfigurationSettings);
      const initialValues: Record<string, ISwitchChanged> = {};

      form.setFieldsValue(initialValues);
    }
  }, [UiConfigurationData, form]);
  const onChangeHandler = async (
    e: boolean,
    type: 'isRequired' | 'isVisible',
    child: ISettingsUiConfigData,
    parentField: string
  ) => {
    const updatedConfigData = [...configData];
    const parentItem = configData?.find((item) => item?.fieldIdentifier === parentField);
    const item = parentItem?.children?.find(
      (item) => item?.fieldIdentifier === child?.fieldIdentifier
    );
    if (item) {
      item[type] = e;
      if (type === 'isVisible' && !e) {
        item.isRequired = false;
      }
    }
    const updateSettingData = {
      userId: id,
      scope: SettingsScope.USER,
      key: SettingsKeys.customerUIConfigurationSettings,
      value: {
        UIConfigurationSettings: updatedConfigData,
      },
    };
    await updateSetting(UiConfigurationData?.id, updateSettingData);
    notificationManager.success({
      message: t('common.success'),
      description: t('dashboard.customer.settings.settingsUIConfiguration.uiConfigUpdated'),
    });
    setConfigData(updatedConfigData as ISettingsUiConfigData[]);

    form.setFieldValue(child?.fieldIdentifier, {
      isRequired: child?.isRequired,
      isVisible: child?.isVisible,
    });
  };

  return (
    <div className="flex flex-col  p-2">
      <div className="flex flex-col gap-2 pl-3">
        <span className="text-[14px] font-semibold">
          {t('dashboard.customer.settings.settingsNotification.formField')}
        </span>
        <span className="text-[14px] text-[#647A83]">
          {t('dashboard.customer.settings.settingsNotification.formFieldDescription')}
        </span>
      </div>
      <div className="p-3">
        <div>
          {configData.length > 0 && (
            <div className="border border-gray-200 rounded-md ">
              <div className="grid grid-cols-4 border-b border-gray-200 bg-[#E1F4FD]">
                <span className="col-span-1 font-semibold p-2 border-r border-gray-200">Form</span>

                <div className="col-span-3">
                  <div className="grid grid-cols-5">
                    <span className="col-span-3 font-semibold p-2 border-r border-gray-200">
                      {t('dashboard.customer.settings.field')}
                    </span>
                    <span className="flex justify-center font-semibold p-2 border-r border-gray-200">
                      {t('dashboard.customer.settings.required')}
                    </span>
                    <span className="flex justify-center font-semibold p-2 border-r border-gray-200">
                      {t('dashboard.customer.settings.visible')}
                    </span>
                  </div>{' '}
                </div>
              </div>{' '}
              <Form className="max-h-[450px] 2xl:max-h-[600px] overflow-y-auto">
                {configData.map((item) => (
                  <div className="grid grid-cols-4">
                    <span className="col-span-1 text-[14px] text-[#334B56] font-[500] border-r border-gray-200 border-b p-2">
                      {item.label}
                    </span>
                    <div className="col-span-3">
                      {item?.children.map((child) => (
                        <div className="grid grid-cols-5">
                          <span className="text-[14px] text-[#20363F] grid col-span-3 font-[500]  border-r border-gray-200 p-2 border-b">
                            {child.label}
                          </span>
                          <div className=" border-r border-gray-200 border-b">
                            <Form.Item className="flex justify-center mb-[10px] checkbox-form-item">
                              <Checkbox
                                className="checkbox-container"
                                disabled={child.isSystemRequired}
                                onChange={(e) =>
                                  onChangeHandler(
                                    e.target.checked,
                                    'isRequired',
                                    child,
                                    item.fieldIdentifier
                                  )
                                }
                                checked={child.isRequired}
                              />
                            </Form.Item>
                          </div>
                          <div className=" border-gray-200 border-b">
                            <Form.Item className="checkbox-form-item flex justify-center mb-[10px]">
                              <Checkbox
                                className="checkbox-container"
                                onChange={(e) =>
                                  onChangeHandler(
                                    e.target.checked,
                                    'isVisible',
                                    child,
                                    item.fieldIdentifier
                                  )
                                }
                                checked={child.isVisible}
                              />
                            </Form.Item>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </Form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
export default SettingsUIConfiguration;
