import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { infoCircleOutlined } from '@/assets';
import { Form, Input, InputNumber, Switch } from 'antd';
import { IPackageTypeFormProps } from './Settings.types';
import { useLanguage } from '@/hooks/useLanguage';

const PackageTypeFormComponent: React.FC<IPackageTypeFormProps> = (props) => {
  const { t } = useLanguage();
  const { form, onFinish } = props;
  return (
    <div>
      <Form
        layout="vertical"
        form={form}
        onFinish={onFinish}
        className="flex flex-col gap-3"
        name="packageType"
      >
        <Form.Item
          layout="horizontal"
          className="customer-active-key-item !mb-0"
          name={'visible'}
          label={
            <span className="flex gap-2 font-[500] text-[#20363F]">
              {t('dashboard.customer.settings.settingsNotification.visible')}
              <CustomTooltip>
                <img src={infoCircleOutlined} alt="info" />
              </CustomTooltip>
            </span>
          }
        >
          <Switch className="customer-general-switch" />
        </Form.Item>
        <Form.Item
          className="w-full !mb-0"
          name={'packagingType'}
          label={
            <span className="flex font-[500] text-[#20363F]">
              {t('dashboard.customer.settings.settingsNotification.packagingType')}
            </span>
          }
        >
          <Input disabled className="h-[40px] package-type-inputnumber" />
        </Form.Item>
        <div className="w-full grid grid-cols-2 gap-x-3 gap-y-3">
          <Form.Item
            className="w-full !mb-0"
            name="weight"
            label={
              <span className="flex font-[500] text-[#20363F]">
                {t('dashboard.customer.settings.settingsNotification.weight')}
              </span>
            }
          >
            <InputNumber className="package-type-inputnumber" />
          </Form.Item>
          <Form.Item
            className="w-full !mb-0"
            name="maxWeight"
            label={
              <span className="flex font-[500] text-[#20363F]">
                {t('dashboard.customer.settings.settingsNotification.maxWeight')}
              </span>
            }
          >
            <InputNumber className="package-type-inputnumber" />
          </Form.Item>
          <Form.Item
            className="w-full !mb-0 package-type-form-item"
            name={'width'}
            label={
              <span className="flex font-[500] text-[#20363F]">
                {t('dashboard.customer.settings.settingsNotification.width')}
              </span>
            }
          >
            <InputNumber className="package-type-inputnumber" />
          </Form.Item>
          <Form.Item
            className="w-full !mb-0"
            name={'height'}
            label={
              <span className="flex font-[500] text-[#20363F]">
                {t('dashboard.customer.settings.settingsNotification.height')}
              </span>
            }
          >
            <InputNumber className="package-type-inputnumber" />
          </Form.Item>
        </div>
        <div>
          <Form.Item
            className="w-full !mb-0"
            name={'length'}
            label={
              <span className="flex font-[500] text-[#20363F]">
                {t('dashboard.customer.settings.settingsNotification.length')}
              </span>
            }
          >
            <InputNumber className="package-type-inputnumber" />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default PackageTypeFormComponent;
