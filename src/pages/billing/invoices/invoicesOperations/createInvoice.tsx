import { CollapseDownIcon, CollapseUpIcon, DateCalendarIcon, RefreshInvoiceIcon } from '@/assets';
import TransferGrid from '@/components/common/transferGrid/TransferGrid';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { Button, Card, DatePicker, Form, InputNumber, Select } from 'antd';
import TextArea from 'antd/es/input/TextArea';

const CreateInvoiceComponent = () => {
    return (
        <div className="flex flex-col">
            <PageHeadingComponent
                title="Create Invoice"
                isChildComponent
                onBackClick={() => window.history.back()}
                children={
                    <Form.Item className="p-3 mt-4 !mb-0">
                        <Select
                            className="w-[200px] h-[40px]"
                            defaultValue={'1'}
                            options={[
                                { value: '1', label: 'Option 1' },
                                { value: '2', label: 'Option 2' },
                            ]}
                        />
                    </Form.Item>
                }
            />

            <div className="invoice-create-content mr-10">
                <Form layout="vertical">
                    <div className="first-part-header w-full flex gap-10 flex-col md:flex-row">
                        <Form.Item className="w-[35%]" label="Customer">
                            <Select
                                className="h-[40px]"
                                placeholder="Select customer"
                                suffixIcon={<CollapseUpIcon />}
                            />
                        </Form.Item>
                        <div className="flex gap-10 w-[70%]">
                            <Form.Item className="w-[35%]" label="Invoice Date">
                                <DatePicker className="w-full h-[40px]" suffixIcon={<DateCalendarIcon />} />
                            </Form.Item>{' '}
                            <Form.Item className="w-[30%]" label="Invoice Number">
                                <InputNumber className="w-full h-[40px]" />
                            </Form.Item>{' '}
                            <Form.Item className="w-[30%]" label="Due Date">
                                <DatePicker className="w-full h-[40px]" suffixIcon={<DateCalendarIcon />} />
                            </Form.Item>
                        </div>
                        <div className="pl-2 pr-[2.5rem] mt-7">
                            <Button className="h-[40px]">
                                <RefreshInvoiceIcon />
                            </Button>
                        </div>
                    </div>
                    <div className="transfer-grid-invoice">
                        <TransferGrid
                            colDefs={[]}
                            initialRowData={[]}
                            setIsEdit={() => { }}
                            setSearchText={() => { }}
                            searchText={{
                                searchTextForAssigned: '',
                                searchTextForAvailable: '',
                                searchTextForSelected: '',
                            }}
                            gridProps={{
                                columnDefs: [],
                                className: '!h-[50vh] lg:!h-[50vh] 3xl:!h-[50vh]',
                                gridId: GridIdConstant.GRID_WRAPPER_FOR_INVOICE,
                            }}
                            isSave={false}
                            hideBackNavigation
                            assignedServices={[]}
                            availableGridEmptyStateDescription=""
                            availableGridEmptyStateTitle=""
                            selectedGridEmptyState=""
                            mainHeaderTitle=""
                            availableGridSearchPlaceholder=""
                            selectedGridSearchPlaceholder=""
                            preventEditModeOnSave={false}
                            availableGridHeader="Unbilled Orders"
                            selectedGridHeader="Invoice Order"
                        />
                    </div>
                    <div className="second-part-footer flex gap-4 w-full mt-1">
                        <div className="left-part-footer flex flex-col w-1/2 gap-5">
                            <Form.Item className='w-[94%] !mb-0' label="Memo">
                                <TextArea rows={4} />
                            </Form.Item>
                            <div className="flex gap-3">
                                <Button className="h-[40px]">Save as draft</Button>
                                <Button className="h-[40px]">Save & Send</Button>
                                <Button className="h-[40px]">Preview</Button>
                                <Button className="h-[40px]">Discard & Close</Button>
                            </div>
                        </div>
                        <div className="right-part-footer w-[47%] flex gap-7">
                            <Card className='w-1/2 bg-[#F5F6FF]'><div className='flex  flex-col'>
                                <span className='flex justify-between'><span className='font-600'>Total orders</span><span className='font-600'>$0.00</span></span></div></Card>
                            <Card className='w-1/2 bg-[#F5F6FF]'><div className='flex  flex-col'><span className='flex justify-between'><span className='font-600'>Subtotal</span><span className='font-600'>$0.00</span></span></div></Card>
                        </div>
                    </div>
                </Form >
            </div >
        </div >
    );
};

export default CreateInvoiceComponent;
