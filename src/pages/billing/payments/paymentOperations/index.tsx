import { customerHook } from '@/api/customer/useCustomer';
import { DateCalendarIcon } from '@/assets';
import SelectDownArrow from '@/assets/icons/selectDownArrow';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { ROUTES } from '@/constant/RoutesConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { numberFieldValidator } from '@/lib/FormValidators';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { numberParser } from '@/lib/helper/formHelper';
import { IColDef } from '@/types/AgGridTypes';
import { Button, DatePicker, Form, Input, InputNumber, Select } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
const invoices = [
  {
    id: '1',
    invoiceNumber: 'INV-2023-001',
    date: new Date().toISOString(),
    amount: 100,
    amountDue: 43,
  },
  {
    id: '2',
    invoiceNumber: 'INV-2023-002',
    amount: 200,
    date: new Date().toISOString(),
    amountDue: 23,
  },
  {
    id: '3',
    invoiceNumber: 'INV-2023-003',
    amount: 100,
    date: new Date().toISOString(),
    amountDue: 43,
  },
  {
    id: '4',
    invoiceNumber: 'INV-2023-004',
    amount: 324,
    date: new Date().toISOString(),
    amountDue: 23,
  },
  {
    id: '5',
    invoiceNumber: 'INV-2023-005',
    amount: 1040,
    date: new Date().toISOString(),
    amountDue: 43,
  },
  {
    id: '6',
    invoiceNumber: 'INV-2023-006',
    amount: 422,
    date: new Date().toISOString(),
    amountDue: 23,
  },
];

const PaymentOperationalForm = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { id: paymentId } = useParams();

  const paymentMethodsOptions = [
    {
      value: 'credit-card',
      label: t('paymentOperationsPage.paymentMethods.creditCard'),
    },
    {
      value: 'PayPal',
      label: t('paymentOperationsPage.paymentMethods.paypal'),
    },
    {
      value: 'bank-transfer',
      label: t('paymentOperationsPage.paymentMethods.bankTransfer'),
    },
    {
      value: 'credit',
      label: t('paymentOperationsPage.paymentMethods.credit'),
    },
  ];

  const { data: customerList } = customerHook.useEntities('all/minimal');

  const customerOptions = useMemo(() => {
    return customerList?.data.map((customer) => ({
      value: customer.id,
      label: `${customer.companyName} - ${customer.contactName} `,
    }));
  }, [customerList?.data]);

  const [paymentForm] = Form.useForm();
  const customerFieldWatcher = Form.useWatch('customerId', paymentForm);

  const paymentInvoicesColDefs: IColDef[] = [
    {
      field: 'invoiceNumber',
      headerName: t('paymentOperationsPage.invoicesGrid.colDefs.invoiceNumber'),
      type: 'string',
      visible: true,
      unSortIcon: true,
      minWidth: 170,
      flex: 1,
    },
    {
      field: 'date',
      headerName: t('paymentOperationsPage.invoicesGrid.colDefs.date'),
      visible: true,
      type: 'date',
      minWidth: 170,
      unSortIcon: true,
      flex: 1,
      valueFormatter: (params: { value: string }) => {
        return dateFormatter(params.value);
      },
    },
    {
      field: 'amount',
      headerName: t('paymentOperationsPage.invoicesGrid.colDefs.amount'),
      visible: true,
      type: 'number',
      minWidth: 170,
      unSortIcon: true,
      flex: 1,
      valueFormatter: (params: { value: string }) => {
        return `$${params.value}`;
      },
    },
    {
      field: 'amountDue',
      headerName: t('paymentOperationsPage.invoicesGrid.colDefs.amountDue'),
      visible: true,
      type: 'number',
      unSortIcon: true,
      minWidth: 170,
      flex: 1,
      valueFormatter: (params: { value: string }) => {
        return `$${params.value}`;
      },
    },
  ];

  return (
    <div className="">
      <header>
        <PageHeadingComponent
          title={
            paymentId
              ? t('paymentOperationsPage.header.editTitle')
              : t('paymentOperationsPage.header.addTitle')
          }
          isChildComponent={true}
          onBackClick={() => navigate(ROUTES.BILLING.BILLING_PAYMENTS_GRID)}
        />
      </header>
      <main className="mt-6">
        <Form name="payment-form" layout="vertical" className="custom-form" form={paymentForm}>
          <div className="form-fields-wrapper flex gap-3 flex-col pr-4">
            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
              <Form.Item
                label={t('paymentOperationsPage.form.labels.customer')}
                name="customerId"
                rules={[
                  { required: true, message: t('addressPage.operationalForm.customerError') },
                ]}
              >
                <Select
                  options={customerOptions}
                  allowClear
                  placeholder={t('addressPage.operationalForm.customerPlaceholder')}
                  prefixCls="custom-select"
                  disabled={Boolean(paymentId)}
                  aria-readonly={Boolean(paymentId)}
                  suffixIcon={<SelectDownArrow />}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
              <Form.Item
                label={t('paymentOperationsPage.form.labels.dateReceived')}
                name="dateReceived"
                rules={[
                  {
                    required: true,
                    message: t('paymentOperationsPage.form.validation.dateReceivedRequired'),
                  },
                ]}
              >
                <DatePicker
                  className="custom-date-picker"
                  format={'DD/MM/YYYY'}
                  suffixIcon={<DateCalendarIcon />}
                />
              </Form.Item>
            </div>
            {customerFieldWatcher && (
              <div className={invoices.length === 0 ? 'border border-gray-300 rounded-lg' : ''}>
                <CustomAgGrid
                  rowSelection={{
                    mode: 'multiRow',
                  }}
                  className="!h-[50vh]"
                  gridId={'gridWrapperForPayment'}
                  rowData={invoices}
                  columnDefs={paymentInvoicesColDefs}
                  emptyState={{
                    title: t('paymentOperationsPage.invoicesGrid.emptyState.title'),
                    description: t('paymentOperationsPage.invoicesGrid.emptyState.description'),
                  }}
                />
              </div>
            )}
            <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-3">
              <Form.Item
                label={'Reference Number'}
                name="referenceNumber"
                rules={[
                  {
                    required: true,
                    message: 'Reference number is required',
                  },
                ]}
              >
                <Input placeholder={'012345678901'} maxLength={16} />
              </Form.Item>
              <Form.Item
                label={t('paymentOperationsPage.form.labels.paymentMethod')}
                name="paymentMethod"
                rules={[
                  {
                    required: true,
                    message: t('paymentOperationsPage.form.validation.paymentMethodRequired'),
                  },
                ]}
              >
                <Select
                  options={paymentMethodsOptions}
                  placeholder={t('paymentOperationsPage.form.placeholders.selectPaymentMethod')}
                  prefixCls="custom-select"
                  suffixIcon={<SelectDownArrow />}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
              <Form.Item
                label={t('paymentOperationsPage.form.labels.paymentAmount')}
                name="paymentAmount"
                rules={[
                  {
                    required: true,
                    message: t('paymentOperationsPage.form.validation.paymentAmountRequired'),
                  },
                ]}
              >
                <InputNumber
                  inputMode="decimal"
                  formatter={(value) => (value ? `$${value}` : '')}
                  maxLength={12}
                  className="bulk-adjust-input w-full"
                  placeholder={t('paymentOperationsPage.form.placeholders.amountPlaceholder')}
                  onKeyDown={(event) => numberFieldValidator(event, { allowDecimals: true })}
                  parser={numberParser}
                  min={1}
                  step={0.01}
                />
              </Form.Item>
            </div>
            <Form.Item label={t('paymentOperationsPage.form.labels.memo')} name="memo">
              <TextArea
                placeholder={t('paymentOperationsPage.form.placeholders.memoPlaceholder')}
              />
            </Form.Item>
          </div>
          <Button htmlType="submit" type="primary" className="mt-4 px-7 py-4">
            {paymentId ? t('common.update') : t('common.add')}
          </Button>
        </Form>
      </main>
    </div>
  );
};

export default PaymentOperationalForm;
