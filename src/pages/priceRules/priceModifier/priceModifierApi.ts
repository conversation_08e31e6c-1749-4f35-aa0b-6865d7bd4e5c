import { IPriceModifiersListing } from '@/api/priceModifier/priceModifier.types';

interface SearchCondition {
  query: string;
}
export function searchServices(
  data: IPriceModifiersListing[],
  condition: SearchCondition
): IPriceModifiersListing[] {
  const { query } = condition;
  const normalizedQuery = query.toLowerCase();

  return data.filter((item) => {
    return item.name?.toLowerCase().includes(normalizedQuery);
  });
}
