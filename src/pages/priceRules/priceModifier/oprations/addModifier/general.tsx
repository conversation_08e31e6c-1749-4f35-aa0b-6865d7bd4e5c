import { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Form, Input, InputNumber, Select } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import {
  CalculationType,
  IConfigureTierFormData,
  ITierForConfigure,
} from '../../priceModifiers.types';
import { useLanguage } from '@/hooks/useLanguage';
import CustomDivider from '@/components/common/divider/CustomDivider';
import '../../modifiers.css';
import { infoCircleOutlined, SelectSuffixIcon } from '@/assets';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { ROUTES } from '@/constant/RoutesConstant';
import { formErrorRegex } from '@/constant/Regex';
import CustomModal from '@/components/common/modal/CustomModal';
import ConfigureTieredForm from './configureTiersForm';
import { priceModifierHook } from '@/api/priceModifier/usePriceModifier';
import { CalculationField, IPriceModifier } from '@/api/priceModifier/priceModifier.types';
import usePreventExits from '@/hooks/usePreventExits';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { isFormChangedHandler } from '@/lib/helper';

const GeneralForm = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useLanguage();
  const [form] = Form.useForm();
  const [formData, setFormData] = useState<IPriceModifier>();
  const [selectedPricingMethod, setSelectedPricingMethod] = useState('');
  const [isFixedCalculationType, setIsFixedCalculationType] = useState(false);
  const [onChangeApplicableRangeFrom, setOnChangeApplicableRangeFrom] = useState(0);
  const [summaryText, setSummaryText] = useState('');
  const [isConfigureTierModal, setIsConfigureTierModal] = useState(false);
  const [configureTiersFormData, setConfigureTiersFormData] = useState<IConfigureTierFormData>();
  const [configureTiersForm] = Form.useForm();
  const { data: priceModifierData } = priceModifierHook.useEntity(id as string);
  const [descriptionValues] = useState({
    value: 0,
    calculationBasedOn: '',
    comparisonForField: '',
    initialValue: 0,
    applicableRangeFrom: 0,
    applicableRangeTo: 0,
    unitOfMeasurement: '',
  });

  const navigate = useNavigate();
  useEffect(() => {
    form.setFieldsValue(formData);
    setSelectedPricingMethod(formData?.calculationType ?? '');
  }, [descriptionValues, form, formData]);
  const notificationManager = useNotificationManager();
  const InitialValue = useMemo(() => form.getFieldsValue(true), [form]);
  const { setPreventExit } = usePreventExits();
  const { setIsBlocked } = useNavigationContext();
  const calculationTypes = [
    { label: t('priceModifiers.fixedAmountLabel'), value: CalculationType.FixedAmount },
    { label: t('priceModifiers.fixedPercent'), value: CalculationType.FixedPercentage },
    { label: t('priceModifiers.fixedOverageAmount'), value: CalculationType.FixedOverageAmount },
    {
      label: t('priceModifiers.fixedOveragePercent'),
      value: CalculationType.FixedOveragePercentage,
    },
    {
      label: t('priceModifiers.tieredFixedOverageAmount'),
      value: CalculationType.TieredFixedOverageAmount,
    },
    {
      label: t('priceModifiers.tieredFixedOveragePercent'),
      value: CalculationType.TieredFixedOveragePercentage,
    },
    {
      label: t('priceModifiers.incrementalOverageAmount'),
      value: CalculationType.IncrementalOverageAmount,
    },
    {
      label: t('priceModifiers.incrementalOveragePercent'),
      value: CalculationType.IncrementalOveragePercentage,
    },
    {
      label: t('priceModifiers.tieredIncrementalOverageAmount'),
      value: CalculationType.TieredIncrementalOverageAmount,
    },
    {
      label: t('priceModifiers.tieredIncrementalOveragePercent'),
      value: CalculationType.TieredIncrementalOveragePercentage,
    },
  ];

  const baseFields = [
    {
      label: t('priceModifiers.basePrice'),
      value: CalculationField.BasePrice,
      comparison: t('priceModifiers.formulaDescription.exceeds'),
      unit: '€',
    },
    {
      label: t('priceModifiers.declaredValue'),
      value: CalculationField.DeclaredValue,
      comparison: t('priceModifiers.formulaDescription.exceeds'),
      unit: '€',
    },
    {
      label: t('priceModifiers.cubicDimensions'),
      value: CalculationField.CubicDimensions,
      comparison: t('priceModifiers.formulaDescription.exceeds'),
      unit: 'm³',
    },
    {
      label: t('priceModifiers.distance'),
      value: CalculationField.Distance,
      comparison: t('priceModifiers.formulaDescription.exceeds'),
      unit: 'km',
    },
    {
      label: t('priceModifiers.height'),
      value: CalculationField.Height,
      comparison: t('priceModifiers.formulaDescription.isGreaterThan'),
      unit: 'm',
    },
    {
      label: t('priceModifiers.width'),
      value: CalculationField.Width,
      comparison: t('priceModifiers.formulaDescription.isGreaterThan'),
      unit: 'm',
    },
    {
      label: t('priceModifiers.length'),
      value: CalculationField.Length,
      comparison: t('priceModifiers.formulaDescription.isGreaterThan'),
      unit: 'm',
    },
    {
      label: t('priceModifiers.quantity'),
      value: CalculationField.Quantity,
      comparison: t('priceModifiers.formulaDescription.isMoreThan'),
      unit: 'pcs',
    },
    {
      label: t('priceModifiers.collectionWaitTime'),
      value: CalculationField.CollectionWaitTime,
      comparison: t('priceModifiers.formulaDescription.isMoreThan'),
      unit: 'min',
    },
    {
      label: t('priceModifiers.deliveryWaitTime'),
      value: CalculationField.DeliveryWaitTime,
      comparison: t('priceModifiers.formulaDescription.isMoreThan'),
      unit: 'min',
    },
  ];
  const operatorsForFrom = [
    { label: '>', value: 'greater_than' },
    { label: '>=', value: 'greater_than_or_equal' },
  ];
  const operatorsForTo = [
    { label: '<', value: 'less_than' },
    { label: '<=', value: 'less_than_or_equal' },
  ];
  useEffect(() => {
    if (id && priceModifierData) {
      setFormData(priceModifierData as IPriceModifier);
    }
  }, [id, priceModifierData]);

  const formulaCalculationOnCalculationType = useCallback(() => {
    let sentence = '';
    const surchargeSymbol =
      selectedPricingMethod === CalculationType.FixedOverageAmount ? '$' : '%';

    switch (selectedPricingMethod) {
      case CalculationType.FixedAmount:
        if (descriptionValues?.value)
          setSummaryText(
            t('priceModifiers.formulaDescription.forFlatRate', { rate: descriptionValues.value })
          );
        else setSummaryText('');
        break;
      case CalculationType.FixedPercentage:
        if (descriptionValues?.value && descriptionValues?.calculationBasedOn) {
          setSummaryText(
            t('priceModifiers.formulaDescription.forFlatPercent', {
              percent: descriptionValues.value,
              calculationBasedOn: descriptionValues.calculationBasedOn,
            })
          );
        } else setSummaryText('');
        break;
      case CalculationType.FixedOverageAmount:
      case CalculationType.FixedOveragePercentage:
        if (
          descriptionValues.calculationBasedOn &&
          descriptionValues.comparisonForField &&
          descriptionValues.initialValue &&
          descriptionValues.value
        ) {
          sentence = `If the ${descriptionValues.calculationBasedOn.toLowerCase()} 
          ${descriptionValues.comparisonForField} ${descriptionValues.initialValue}${descriptionValues.unitOfMeasurement}, 
          add a ${descriptionValues.value}${surchargeSymbol} surcharge`;
          if (
            descriptionValues.applicableRangeFrom > 0 &&
            descriptionValues.applicableRangeTo > 0
          ) {
            sentence += ` from ${descriptionValues.applicableRangeFrom} to ${descriptionValues.applicableRangeTo}`;
          } else {
            sentence += '';
          }

          sentence += '.';
        } else {
          setSummaryText('');
        }
        setSummaryText(sentence);
        break;

      default:
        setSummaryText('');
        break;
    }
  }, [
    descriptionValues.applicableRangeFrom,
    descriptionValues.applicableRangeTo,
    descriptionValues.calculationBasedOn,
    descriptionValues.comparisonForField,
    descriptionValues.initialValue,
    descriptionValues.unitOfMeasurement,
    descriptionValues.value,
    selectedPricingMethod,
    t,
  ]);
  useEffect(() => {
    if (!selectedPricingMethod) return;
    if (
      selectedPricingMethod === CalculationType.FixedAmount ||
      selectedPricingMethod === CalculationType.FixedPercentage
    ) {
      setIsFixedCalculationType(true);
    } else {
      setIsFixedCalculationType(false);
    }
    formulaCalculationOnCalculationType();
  }, [formulaCalculationOnCalculationType, selectedPricingMethod]);
  const createPriceModifier = priceModifierHook.useCreate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('priceModifiers.priceModifierSaved'),
      });
    },
  });
  const updatePriceModifierMutation = priceModifierHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('priceModifiers.priceModifierSaved'),
      });
    },
  });
  const handleSave = async (e: IPriceModifier) => {
    const fieldName = form.getFieldValue('fieldName');
    const data = {
      ...e,
      fieldName: fieldName,
      applicableRangeMin: Number(e.applicableRangeMin),
      applicableRangeMax: Number(e.applicableRangeMax),
      calculationBase: Number(e.calculationBase),
      increment: Number(e.increment),
      amount: Number(e.amount),
      tieredRanges: configureTiersFormData?.tieredRanges as ITierForConfigure[],
      tieredDefaultValue: configureTiersFormData?.defaultAmount,
    };
    if (!id) {
      const addPriceModifier = await createPriceModifier.mutateAsync(data);
      navigate(
        ROUTES.PRICES.PRICES_PRICE_MODIFIER_EDIT.replace(':id', addPriceModifier.id).replace(
          ':tab',
          'general'
        ),
        {
          replace: true,
        }
      );
    } else {
      await updatePriceModifierMutation.mutateAsync({
        id: id,
        data: data,
      });
    }
  };
  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button
          className="rounded-lg border-[#96A9B1]"
          onClick={() => setIsConfigureTierModal(false)}
        >
          {t('common.cancel')}
        </Button>
        <Button
          form="configureTiersForm"
          htmlType="submit"
          type="primary"
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
        >
          {id ? t('common.update') : t('common.save')}
        </Button>
      </footer>
    ),
    [id, t]
  );
  const onModalClose = () => {
    setIsConfigureTierModal(false);
    configureTiersForm.resetFields();
  };

  const handleOnChangeCalculationType = (value: CalculationType) => {
    switch (value) {
      case CalculationType.FixedAmount:
        form.setFieldsValue({
          fieldName: null,
          applicableRangeMin: null,
          applicableRangeMax: null,
          calculationBase: null,
          increment: null,
        });
        break;
      case CalculationType.FixedPercentage:
        form.setFieldsValue({
          applicableRangeMin: null,
          applicableRangeMax: null,
          calculationBase: null,
          increment: null,
        });
        break;
      case CalculationType.FixedOverageAmount || CalculationType.FixedOveragePercentage:
        form.setFieldsValue({
          increment: null,
        });
        break;
      case CalculationType.TieredFixedOverageAmount || CalculationType.TieredFixedOveragePercentage:
        form.setFieldsValue({
          amount: null,
          increment: null,
          applicableRangeMin: null,
          applicableRangeMax: null,
        });
        break;
      default:
        break;
    }
    setSelectedPricingMethod(value);
  };
  const ifRequiredOrNot = useCallback(
    (fieldName: string) => {
      if (!selectedPricingMethod) return false;

      const isTiered = selectedPricingMethod.includes('Tiered');
      const isIncrement = selectedPricingMethod.includes('Increment');
      const isFixedAmount = selectedPricingMethod === CalculationType.FixedAmount;

      switch (fieldName) {
        case 'fieldName':
          return !isFixedAmount;

        case 'applicableRangeMin':
        case 'applicableRangeMax':
          return !(isFixedCalculationType || isTiered);

        case 'calculationBase':
          return !isFixedCalculationType;

        case 'increment':
          return isIncrement;

        case 'amount':
          return !isTiered;

        default:
          return true;
      }
    },
    [selectedPricingMethod, isFixedCalculationType]
  );

  return (
    <>
      <CustomModal
        modalTitle={t('priceModifiers.configureTiersForm.basePriceTiers')}
        modalDescription={
          Object?.values(calculationTypes)?.find((item) => item.value === selectedPricingMethod)
            ?.label as string
        }
        open={isConfigureTierModal}
        onClose={onModalClose}
        onCancel={onModalClose}
        maskClosable={false}
        footer={Footer}
      >
        <ConfigureTieredForm
          setIsConfigureTierModal={setIsConfigureTierModal}
          selectedType={selectedPricingMethod?.includes('Amount') ? '$' : '%'}
          configureTiersForm={configureTiersForm}
          setConfigureTiersFormData={
            setConfigureTiersFormData as React.Dispatch<
              React.SetStateAction<IConfigureTierFormData>
            >
          }
          formData={formData as IPriceModifier}
        />
      </CustomModal>
      <div className="p-3 max-h-[550px] lg:max-h-[600px] 2xl:max-h-[750px] overflow-y-auto">
        <Form
          form={form}
          onFinish={(e) => {
            handleSave(e);
          }}
          onFieldsChange={(changesFields) => {
            if (changesFields.length <= 1) {
              const isIsChange = isFormChangedHandler(InitialValue, form.getFieldsValue(true), [
                'priceModifier',
                'description',
              ]);
              setPreventExit(isIsChange);
            } else if (changesFields.length > 1) {
              setIsBlocked(false);
              setPreventExit(false);
            }
          }}
          className="space-y-8"
          layout="vertical"
        >
          <div>
            <div className="flex w-full">
              <Form.Item
                labelAlign="left"
                className="general-form-item w-full text-sm font-normal text-gray-600 mb-1"
                name={'name'}
                label={t('priceSetPage.colDefs.name')}
                required
                rules={[
                  { whitespace: true, message: `${t('zonePage.operationalForm.nameError')}` },
                  { required: true, message: `${t('zonePage.operationalForm.nameError')}` },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: `${t('common.errors.noMultipleWhiteSpace')}`,
                  },
                ]}
              >
                <Input
                  min={3}
                  maxLength={255}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                  placeholder={t('priceModifiers.commonTwoHoursSkidPriceCalculation')}
                />
              </Form.Item>
            </div>
            <CustomDivider className="py-4" label={t('priceModifiers.calculationType')} />
            <div className="flex w-full  flex-col">
              <Form.Item
                className="general-form-item mb-2"
                name="calculationType"
                label={
                  <span className="flex gap-1">
                    {t('priceModifiers.pricingMethod')} <img src={infoCircleOutlined} />
                  </span>
                }
                rules={[
                  {
                    required: true,
                    message: t('priceModifiers.formulaDescription.pricingMethodRequired'),
                  },
                ]}
              >
                <Select
                  className="h-[40px]"
                  options={calculationTypes}
                  placeholder={t('priceModifiers.selectPriceMethod')}
                  onChange={(value) => {
                    handleOnChangeCalculationType(value);
                  }}
                  suffixIcon={<SelectSuffixIcon />}
                />
              </Form.Item>
              <div className="flex gap-8 w-full ">
                <Form.Item
                  className="general-form-item w-[49%] mb-2"
                  name="fieldName"
                  label={t('priceModifiers.calculationBasedOn')}
                  rules={[
                    {
                      required: ifRequiredOrNot('fieldName'),
                      message: t('priceModifiers.formulaDescription.calculationFieldRequired'),
                    },
                  ]}
                >
                  <Select
                    className="h-[40px]"
                    placeholder={t('priceModifiers.selectBaseField')}
                    options={baseFields}
                    disabled={selectedPricingMethod === CalculationType.FixedAmount}
                    suffixIcon={<SelectSuffixIcon />}
                    onChange={(_value, option) => {
                      if (option && !Array.isArray(option)) {
                        // setDescriptionValues({
                        //   ...descriptionValues,
                        //   calculationBasedOn: option.label,
                        //   comparisonForField: option.comparison,
                        //   unitOfMeasurement: option.unit as string,
                        // });
                        form.setFieldValue('fieldName', _value);
                      }
                    }}
                  />
                </Form.Item>
                <Form.Item
                  className="general-form-item w-[49%] mb-2"
                  name="calculationBase"
                  label={t('priceModifiers.initialValue')}
                  rules={[
                    {
                      required: ifRequiredOrNot('calculationBase'),
                      message: t('priceModifiers.formulaDescription.initialValueRequired'),
                    },
                    {
                      validator: (_, value) => {
                        if (value > 999999999) {
                          return Promise.reject(
                            new Error(t('priceModifiers.maximumValueExceeded'))
                          );
                        } else {
                          return Promise.resolve();
                        }
                      },
                    },
                  ]}
                >
                  <InputNumber
                    type="number"
                    className="initial-value-input w-full "
                    placeholder="10"
                    disabled={isFixedCalculationType}
                    min={0}
                    // onChange={(e) => {
                    //   // setDescriptionValues({ ...descriptionValues, initialValue: e as number });
                    //   // form.setFieldValue('calculationBase', e);
                    // }}
                  />
                </Form.Item>
              </div>

              <div className="flex gap-3 lg:gap-8 w-full flex-wrap lg:flex-nowrap">
                <div className="flex gap-5 w-full lg:w-[49%]">
                  <Form.Item
                    name="applicableRangeMin"
                    className="applicable-range-item1 general-form-item w-1/2 mb-2"
                    label={t('priceModifiers.applicableRange')}
                    rules={[
                      {
                        required: ifRequiredOrNot('applicableRangeMin'),
                        message: t('priceModifiers.formulaDescription.minApplicableRangeRequired'),
                      },
                      {
                        validator: (_, value) => {
                          if (value > 999999999) {
                            return Promise.reject(
                              new Error(t('priceModifiers.maximumValueExceeded'))
                            );
                          } else {
                            return Promise.resolve();
                          }
                        },
                      },
                    ]}
                  >
                    <InputNumber
                      type="number"
                      addonBefore={
                        <Form.Item name="From" className="h-[40px] mb-0 ">
                          <Select
                            className="select-item h-[40px] max-w-[100%]"
                            options={operatorsForFrom}
                            placeholder=">="
                            disabled={
                              isFixedCalculationType ||
                              (selectedPricingMethod as string)?.includes('Tiered')
                            }
                            suffixIcon={<SelectSuffixIcon />}
                            defaultValue={operatorsForFrom[0].value}
                          />
                        </Form.Item>
                      }
                      className="applicable-range-input !h-[40px] w-full"
                      disabled={
                        isFixedCalculationType ||
                        (selectedPricingMethod as string)?.includes('Tiered')
                      }
                      onChange={(value) => {
                        setOnChangeApplicableRangeFrom(value as number);
                        // setDescriptionValues({
                        //   ...descriptionValues,
                        //   applicableRangeFrom: value as number,
                        // });
                      }}
                      min={0}
                    />
                  </Form.Item>
                  <span className="flex h-[65px] items-end text-[16px]">
                    {t('priceModifiers.to')}
                  </span>
                  <Form.Item
                    name="applicableRangeMax"
                    className="applicable-range-item2 w-1/2 mb-2"
                    dependencies={['applicableRangeMin']}
                    label="p"
                    rules={[
                      {
                        required: ifRequiredOrNot('applicableRangeMin'),
                        message: t('priceModifiers.formulaDescription.maxApplicableRangeRequired'),
                      },
                      {
                        validator: (_, value) => {
                          if (
                            ifRequiredOrNot('applicableRangeMin') &&
                            onChangeApplicableRangeFrom > value
                          ) {
                            return Promise.reject(
                              new Error(
                                t('priceModifiers.valueMustBeGreaterThanApplicableRangeFrom')
                              )
                            );
                          } else if (value > 999999999) {
                            return Promise.reject(
                              new Error(t('priceModifiers.maximumValueExceeded'))
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <InputNumber
                      addonBefore={
                        <Form.Item name="To" className="h-[40px] mb-0">
                          <Select
                            className="select-item h-[40px]"
                            options={operatorsForTo}
                            placeholder="<"
                            disabled={
                              isFixedCalculationType ||
                              (selectedPricingMethod as string)?.includes('Tiered')
                            }
                            suffixIcon={<SelectSuffixIcon />}
                            defaultValue={operatorsForTo[0].value}
                          />
                        </Form.Item>
                      }
                      className="applicable-range-input h-[40px] w-full"
                      disabled={
                        isFixedCalculationType ||
                        (selectedPricingMethod as string)?.includes('Tiered')
                      }
                      min={onChangeApplicableRangeFrom}
                      // onChange={(value) => {
                      //   // setDescriptionValues({
                      //   //   ...descriptionValues,
                      //   //   applicableRangeTo: value as number,
                      //   // });
                      //   // form.setFieldValue('applicableRangeMax', value);
                      // }}
                      type="number"
                    />
                  </Form.Item>
                </div>
                <Form.Item
                  rules={[
                    {
                      required: ifRequiredOrNot('increment'),
                      message: t('priceModifiers.formulaDescription.stepValueRequired'),
                    },
                    {
                      validator: (_, value) => {
                        if (value > 999999999) {
                          return Promise.reject(
                            new Error(t('priceModifiers.maximumValueExceeded'))
                          );
                        } else {
                          return Promise.resolve();
                        }
                      },
                    },
                  ]}
                  className="general-form-item w-full lg:w-[49%] mb-2"
                  name="increment"
                  label={t('priceModifiers.stepValue')}
                >
                  <InputNumber
                    max={999999999}
                    min={0}
                    disabled={!selectedPricingMethod?.includes('Increment')}
                    className="initial-value-input w-full h-[40px]"
                    placeholder="0"
                    type="number"
                  />
                </Form.Item>
              </div>
            </div>
            <div className="flex gap-8">
              <Form.Item
                rules={[
                  {
                    required: ifRequiredOrNot('amount'),
                    message: t('priceModifiers.configureTiersForm.adjustmentAmountIsRequired'),
                  },
                  {
                    validator: (_, value) => {
                      if (selectedPricingMethod?.includes('Amount')) {
                        if (value > 999999999) {
                          return Promise.reject(
                            new Error(t('priceModifiers.maximumValueExceeded'))
                          );
                        } else {
                          return Promise.resolve();
                        }
                      } else {
                        if (value > 999) {
                          return Promise.reject(
                            new Error(t('priceModifiers.maximumPercentExceeded'))
                          );
                        } else {
                          return Promise.resolve();
                        }
                      }
                    },
                  },
                ]}
                label={t('priceModifiers.adjustmentTypeLabel')}
                className="general-form-item w-[49%] mb-4"
                name="amount"
              >
                <InputNumber
                  // onChange={(value) => {
                  //   form.setFieldValue('amount', value);
                  //   setDescriptionValues({ ...descriptionValues, value: value as number });
                  // }}
                  min={0}
                  disabled={(selectedPricingMethod as string)?.includes('Tiered')}
                  max={selectedPricingMethod?.includes('Amount') ? 999999999 : 999}
                  addonBefore={selectedPricingMethod?.includes('Amount') ? '$' : '%'}
                  className="applicable-range-input w-full h-[40px]"
                  type="number"
                />
              </Form.Item>

              <Form.Item
                dependencies={form.getFieldValue('pricingMethod')}
                className="w-[49%] mb-2"
                label={<span className="text-white">{t('priceModifiers.configureTiers')}</span>}
              >
                <Button
                  disabled={!(selectedPricingMethod as string)?.includes('Tiered')}
                  className="h-[40px] w-full hover:!text-black hover:!border-gray-300"
                  onClick={() => setIsConfigureTierModal(true)}
                >
                  {t('priceModifiers.configureTiers')}...{' '}
                </Button>
              </Form.Item>
            </div>
            <Form.Item className="mb-2">
              <Button
                htmlType="submit"
                className="w-[105px] h-[40px] text-white bg-primary-600 hover:!text-white hover:!bg-primary-600"
              >
                {id ? t('common.update') : t('common.save')}
              </Button>
            </Form.Item>
          </div>

          <div className="!mt-0 ">
            <CustomDivider className="py-3" label={t('priceModifiers.summaryDetails')} />
            <span className="text-sm text-black font-[600]">
              {t('priceModifiers.summary')}
            </span>: <span className="text-md font-semibold">{summaryText}</span>
          </div>
        </Form>
      </div>
    </>
  );
};

export default GeneralForm;
