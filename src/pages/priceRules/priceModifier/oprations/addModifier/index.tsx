import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { useParams } from 'react-router-dom';
import GeneralForm from './general';
import { useLanguage } from '@/hooks/useLanguage';

const AddModifier = () => {
  const { id } = useParams();
  const isEditMode = Boolean(id);
  const { t } = useLanguage();
  return (
    <div>
      <div className="flex flex-col gap-1 ">
        <div>
          <PageHeadingComponent
            title={
              isEditMode ? t('priceModifiers.updateModifier') : t('priceModifiers.addModifier')
            }
            isChildComponent
          />
        </div>
        <GeneralForm />
      </div>
    </div>
  );
};
export default AddModifier;
