import { ModifierGridIcon, ModifierGroupGridIcon, PlusButtonIcon } from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Button } from 'antd';
import { IColDef } from '@/types/AgGridTypes';
import { ICellRendererParams } from 'ag-grid-community';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { IPriceModifiersListing } from '@/api/priceModifier/priceModifier.types';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { searchModifiers } from './GroupModifiersApi';

interface ISavedGroupModifiersListProps {
  setIsEdit: React.Dispatch<React.SetStateAction<boolean>>;
  savedData: IPriceModifiersListing[];
  setSelectedData: React.Dispatch<React.SetStateAction<IPriceModifiersListing[]>>;

  setPagination: React.Dispatch<React.SetStateAction<{ pageNumber: number; pageSize: number }>>;
}
const SavedGroupModifiersList: React.FC<ISavedGroupModifiersListProps> = (props) => {
  const { setIsEdit, savedData, setSelectedData } = props;
  const [searchText, setSearchText] = useState<string>('');
  const [assignedGroupModifierFilter, setAssignedGroupModifierFilter] = useState<
    IPriceModifiersListing[]
  >([]);
  const [currentGridData, setCurrentGridData] = useState<IPriceModifiersListing[]>([]);
  const searchHandler = useCallback(
    (value: string) => {
      const results = searchModifiers(assignedGroupModifierFilter, {
        query: value,
      });
      setSearchText(value);
      setCurrentGridData(results);
    },
    [assignedGroupModifierFilter]
  );
  const triggerSearch = useCallback(
    (value: string) => {
      searchHandler(value);
    },
    [searchHandler]
  );
  useEffect(() => {
    if (savedData) {
      setSelectedData(savedData);
      setCurrentGridData(savedData);
      setAssignedGroupModifierFilter(savedData);
    }
  }, [savedData, setAssignedGroupModifierFilter, setSelectedData]);
  const { t } = useLanguage();

  const priceModifierColDefs: IColDef[] = useMemo(
    () => [
      {
        headerName: t('priceModifiers.colDefs.kind'),
        key: 'type',
        field: 'isGroup',
        unSortIcon: true,
        type: 'string',
        flex: 1,
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.isGroup ? (
            <div className="flex gap-1 items-center">
              <ModifierGroupGridIcon />
              {t('priceModifiers.groupModifiers.priceModifierGroup')}
            </div>
          ) : (
            <div className="flex gap-1 items-center">
              <ModifierGridIcon />
              {t('priceModifiers.groupModifiers.priceModifier')}
            </div>
          );
        },
      },
      {
        headerName: t('priceModifiers.colDefs.name'),
        key: 'Name',
        field: 'name',
        unSortIcon: true,
        type: 'string',
        flex: 1,
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.data.name, searchText) : params.data.name;
        },
      },
    ],
    [searchText, t]
  );
  return (
    <div className="flex flex-col gap-3 w-[99%]">
      <div className="flex w-[98%] gap-3 items-end">
        <PageHeadingComponent
          title={t('priceModifiers.groupModifiers.modifiersGroupList')}
          classNameTitle="text-[22px] font-semibold flex items-center"
          children={
            <SearchFilterComponent
              advanceFilter={false}
              colDefs={priceModifierColDefs}
              searchInputPlaceholder={t('priceModifiers.groupModifiers.searchModifiers')}
              onSearch={triggerSearch}
            />
          }
        />
        <Button
          className="bg-primary-600 text-white h-[40px] hover:!bg-primary-600 hover:!text-white "
          onClick={() => setIsEdit(false)}
        >
          <PlusButtonIcon />
          {t('priceModifiers.groupModifiers.assignModifiers')}
        </Button>
      </div>
      <CustomAgGrid
        gridId={GridIdConstant.GRID_WRAPPER_FOR_GROUP_MODIFIER_CHILDREN}
        className="3xsm:!h-[50vh] md:!h-[65vh] lg:!h-[59vh] 3xl:!h-[59vh]"
        rowData={currentGridData}
        columnDefs={priceModifierColDefs}
        emptyState={{
          title: searchText
            ? t('common.noMatchesFound')
            : t('priceModifiers.groupModifiers.noSavedGroupModifiersFound'),
          description: '',
        }}
        pagination
      />
    </div>
  );
};
export default SavedGroupModifiersList;
