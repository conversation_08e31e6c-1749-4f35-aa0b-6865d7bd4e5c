import { IPriceModifiersListing } from '@/api/priceModifier/priceModifier.types';
interface SearchCondition {
  query: string;
}
export function searchModifiers(
  data: IPriceModifiersListing[],
  condition: SearchCondition
): IPriceModifiersListing[] {
  const { query } = condition;
  const normalizedQuery = query.toLowerCase();
  const newData = data?.filter((item) => {
    return item.name?.toLowerCase().includes(normalizedQuery);
  });
  return newData;
}
