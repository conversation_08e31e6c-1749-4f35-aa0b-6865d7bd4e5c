import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { useNavigate } from 'react-router-dom';
import { ICellRendererParams, IColDef } from '@/types/AgGridTypes.ts';
import { usePriceModifierColDefs } from '@pages/priceRules/priceModifier/usePriceModifiersColDefs.tsx';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { Button, Divider } from 'antd';
import { deleteSvg, ModifierGridIcon, ModifierGroupIcon, ModifierIcon } from '@/assets';
import CustomAgGrid from '@components/common/agGrid/AgGrid.tsx';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { ROUTES } from '@/constant/RoutesConstant';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { useLanguage } from '@/hooks/useLanguage';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { CellContextMenuEvent } from 'ag-grid-community';
import { priceModifierHook } from '@/api/priceModifier/usePriceModifier';
import { IPriceModifier, IPriceModifiersListing } from '@/api/priceModifier/priceModifier.types';
import { groupModifierHook } from '@/api/groupModifiers/useGroupModifier';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { searchModifiers } from './oprations/groupModifier/GroupModifiersApi';

const PriceModifierComponent = () => {
  const gridRef = useRef<AgGridReact<IPriceModifier>>(null);
  const navigate = useNavigate();
  const [getAllPriceModifiers, setGetAllPriceModifiers] = useState<IPriceModifiersListing[]>();
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IPriceModifiersListing[]>(
    []
  );

  const [loading, setLoading] = useState(false);
  const [selectedCellData, setSelectedCellData] = useState<
    CellContextMenuEvent<ICellRendererParams>
  >({} as CellContextMenuEvent<ICellRendererParams>);
  const { data: priceModifiersList, refetch } = priceModifierHook.useEntities('combined');
  const [searchText, setSearchText] = useState('');
  const notificationManager = useNotificationManager();
  const { t } = useLanguage();

  const deletePriceModifierMutation = priceModifierHook.useDelete({
    onSuccess: async () => {
      await refetch();
      notificationManager.success({
        message: t('common.success'),
        description: t('priceModifiers.priceModifierDeleted'),
      });
      customAlert.destroy();
      refetch();
    },
  });

  const deletePriceModifierGroupMutation = groupModifierHook.useDelete({
    onSuccess: async () => {
      await refetch();
      notificationManager.success({
        message: t('common.success'),
        description: t(
          'systemErrors.businessErrors.priceModifiers.groupModifierDeletedSuccessfully'
        ),
      });
      customAlert.destroy();
    },
  });

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.priceModifier.sortable.includes(field);
  }, []);

  const handleDeletePriceModifier = useCallback(
    async (data: IPriceModifiersListing) => {
      customAlert.error({
        title: t('priceModifiers.confirmDeletePriceModifier'),
        message: t('priceModifiers.deleteModifierWarning'),
        firstButtonFunction: async () => {
          if (data?.isGroup) {
            await deletePriceModifierGroupMutation.mutateAsync(data?.id as string);
          } else {
            await deletePriceModifierMutation.mutateAsync(data?.id as string);
          }
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
      });
    },
    [deletePriceModifierGroupMutation, deletePriceModifierMutation, t]
  );

  const priceModifierColDefs: IColDef[] = usePriceModifierColDefs({
    searchText,
    onEdit: (data: IPriceModifiersListing) => {
      if (data?.isGroup) {
        navigate(
          ROUTES.PRICES.PRICES_PRICE_GROUP_MODIFIER_EDIT.replace(':id', data?.id as string).replace(
            ':tab',
            'general'
          )
        );
      } else {
        navigate(
          ROUTES.PRICES.PRICES_PRICE_MODIFIER_EDIT.replace(':id', data?.id as string).replace(
            ':tab',
            'general'
          )
        );
      }
    },
    onDelete: (data: IPriceModifier) => {
      handleDeletePriceModifier(data);
    },
    isColumnSortable,
  });

  useEffect(() => {
    if (priceModifiersList?.data) {
      setLoading(true);
      setGetAllPriceModifiers(priceModifiersList?.data);
      setSelectedQuickFilterData(priceModifiersList?.data);
      setLoading(false);
    }
  }, [priceModifiersList]);

  const priceModifierContextMenuItems = useMemo(() => {
    return [
      {
        label: t('priceModifiers.addModifier'),
        key: 'addModifier',
        icon: ModifierGridIcon as React.ElementType,
        onClick: () => navigate(ROUTES.PRICES.PRICES_PRICE_MODIFIER_ADD.replace(':tab', 'general')),
      },
      {
        label: t('priceModifiers.addModifierGroup'),
        icon: ModifierGroupIcon as React.ElementType,
        key: 'addModifierGroup',
        onClick: () => navigate(ROUTES.PRICES.PRICES_PRICE_GROUP_MODIFIER_ADD),
      },

      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} alt="delete" />) as unknown as React.ElementType,
        key: 'delete',
        onClick: () =>
          handleDeletePriceModifier(selectedCellData?.data as unknown as IPriceModifiersListing),
      },
    ];
  }, [handleDeletePriceModifier, navigate, selectedCellData?.data, t]);

  const searchHandler = useCallback(
    (value: string) => {
      const results = searchModifiers(selectedQuickFilterData, {
        query: value,
      });
      setSearchText(value);
      setGetAllPriceModifiers(results);
    },
    [selectedQuickFilterData]
  );
  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
          <div className="md:w-1/3 flex flex-col 3xsm:w-full">
            <PageHeadingComponent title="Price Modifiers" />
          </div>

          <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
            <div className="flex gap-3">
              <SearchFilterComponent
                advanceFilter={false}
                onSearch={triggerSearch}
                colDefs={priceModifierColDefs.filter((col) => col.field !== 'isGroupModifier')}
                onFilterApply={() => {}}
                isSetQuickFilter={false}
                searchInputPlaceholder={t('priceSetPage.priceMod.searchPlaceholder')}
              />
            </div>
            <div className="pt-5">
              <Divider type="vertical" className="hidden md:flex h-[40px] !m-0" />
            </div>
            <div className="3xsm:pt-0 md:pt-5 flex items-center gap-3">
              <Button
                className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-[#0876A4] text-white font-[500] hover:!bg-[#0876A4] hover:!text-white"
                icon={<ModifierIcon />}
                onClick={() =>
                  navigate(ROUTES.PRICES.PRICES_PRICE_MODIFIER_ADD.replace(':tab', 'general'))
                }
              >
                {t('priceModifiers.addModifier')}
              </Button>
              <Button
                disabled={getAllPriceModifiers?.length === 0}
                className=" h-[40px] border-[1px] rounded-[8px] hover:!border-[#d9d9d9]  font-[500]  hover:!text-black "
                icon={<ModifierGroupIcon />}
                onClick={() => navigate(ROUTES.PRICES.PRICES_PRICE_GROUP_MODIFIER_ADD)}
              >
                {t('priceModifiers.addModifierGroup')}
              </Button>
            </div>
          </div>
        </div>
        <main className="overflow-x-hidden overflow-y-auto bg-white">
          <div className="mx-auto pr-6 py-5 h-full flex justify-center items-center">
            <CustomAgGrid
              gridRef={gridRef}
              className={'md:!h-[90vh]'}
              loading={loading}
              columnDefs={priceModifierColDefs}
              rowData={getAllPriceModifiers}
              isContextMenu
              pagination
              contextMenuItem={priceModifierContextMenuItems}
              onContextMenu={(cellData) => setSelectedCellData(cellData)}
              emptyState={{
                title:
                  searchText || selectedQuickFilterData.length > 0
                    ? t('common.noMatchesFound')
                    : t('priceModifiers.noPriceModifierFound'),
                description:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('priceModifiers.pleaseAddNewPriceModifier'),
                link:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('priceModifiers.addPriceModifier'),
                onLinkAction: () => navigate(ROUTES.PRICES.PRICES_PRICE_MODIFIER_ADD),
              }}
            />
          </div>
        </main>
      </div>
    </div>
  );
};
export default PriceModifierComponent;
