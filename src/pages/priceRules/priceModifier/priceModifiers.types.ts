export enum CalculationType {
  FixedAmount = 'FlatAmount',
  FixedPercentage = 'FlatPercentage',
  FixedOverageAmount = 'FlatOverageAmount',
  FixedOveragePercentage = 'FlatOveragePercentage',
  TieredFixedOverageAmount = 'TieredFixedOverageAmount',
  TieredFixedOveragePercentage = 'TieredFixedOveragePercentage',
  IncrementalOverageAmount = 'IncrementalOverageAmount',
  IncrementalOveragePercentage = 'IncrementalOveragePercentage',
  TieredIncrementalOverageAmount = 'TieredIncrementalOverageAmount',
  TieredIncrementalOveragePercentage = 'TieredIncrementalOveragePercentage',
}

export enum CalculationField {
  BasePrice = 'BasePrice',
  DeclaredValue = 'DeclaredValue',
  CubicDimensions = 'CubicDimensions',
  Distance = 'Distance',
  Height = 'Height',
  Width = 'Width',
  Length = 'Length',
  Quantity = 'Quantity',
  CollectionWaitTime = 'CollectionWaitTime',
  DeliveryWaitTime = 'DeliveryWaitTime',
  Weight = 'Weight',
  CustomAmount = 'CustomAmount',
}

export enum RangeFromOperator {
  GreaterThan = 'GreaterThan',
  GreaterThanOrEqual = 'GreaterThanOrEqual',
}

export enum RangeToOperator {
  LessThan = 'LessThan',
  LessThanOrEqual = 'LessThanOrEqual',
}

export enum ModifierGroupBehavior {
  UseSum = 'UseSum',
  UseHighest = 'UseHighest',
  UseLowest = 'UseLowest',
}

export interface IIndividualPriceModifier {
  id: string;
  Name: string;
  PublicName: string;
  CalculationType: CalculationType;
  CalculationField: CalculationField;
  calcutionObject: { [key: string]: any };
  Increment: number;
  Value: number;
  TieredRange?: Array<{
    From: { Value: number; Operator: RangeFromOperator };
    To: { Value: number; Operator: RangeToOperator };
    Value: number;
  }>;
  ApplicableRange?: {
    From: { Value: number; Operator: RangeFromOperator };
    To: { Value: number; Operator: RangeToOperator };
  };
  CalculationStartAfter?: number;
  Description: string;
  IsEnabled: boolean;
  IsDriverCommission: boolean;
  isGroupModifier?: false;
  isSelected: boolean;
}

export interface IGroupPriceModifier {
  id?: string;
  Name: string;
  Description: string;
  Behavior: ModifierGroupBehavior;
  Modifiers: IIndividualPriceModifier[];
  isGroupModifier: true;
  isSelected: boolean;
}

export type IPriceModifier = IIndividualPriceModifier | IGroupPriceModifier;

export interface ITierForConfigure {
  fromValue: number;
  toValue: number;
  value: number;
  fromOperator: RangeFromOperator;
  toOperator: RangeToOperator;
}

export interface IConfigureTierFormData {
  tieredRanges: ITierForConfigure[];
  defaultAmount: number;
}
