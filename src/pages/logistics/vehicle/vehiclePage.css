.custom-form .ant-form-item {
  margin-bottom: 0px !important;
}

.custom-form .ant-form-item-label {
  margin-bottom: 10px;
  font-family: var(--font-family);
  font-weight: 500;
  padding: 0 0 5px !important;
}

.custom-form .ant-form-item-label > label {
  display: flex !important;
  flex-direction: row-reverse !important;
  justify-content: flex-end !important;
  width: fit-content !important;
  gap: 4px !important;
  color: var(--black-text);
}

.custom-form .ant-select-selection-item,
.custom-form .ant-select-item-option {
  font-family: var(--font-family);
}

.custom-form .form-fields-wrapper {
  margin-top: 12px;
}

.custom-form .ant-input {
  font-family: var(--font-family);
  height: 40px;
  border-radius: 8px;
}
.combined-input .ant-form-item:last-child {
  margin-top: 27px;
}
.combined-input .ant-input-group > .ant-input:last-child {
  border-radius: 0px !important;
  border-right: transparent;
}
.custom-form textarea {
  min-height: 54px !important;
  max-height: 200px !important;
}
.custom-form .large-textarea {
  min-height: 94px !important;
  max-height: 250px !important;
}

.custom-form .ant-form-item .ant-form-item-label > label::after {
  display: none;
}

.custom-form .ant-form-item-explain-error {
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}
