import { Form, Tooltip } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import PageBreadCrumbsComponent from '@/components/specific/pageBreadCrumb/PageBreadCrumbComponent';
import { ROUTES } from '@/constant/RoutesConstant';
import { TabsProps } from 'antd/lib';
import { TabsComponent } from '@/components/common/customTabs/CustomTabs';
import VehicleGeneral from './vehicleGeneral';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import VehicleTimeClockSessions from './timeClockSessions';
import { useLanguage } from '@/hooks/useLanguage';
import usePreventExits from '@/hooks/usePreventExits';
import { vehicleService, vehicleServiceHooks } from '@/api/vehicle/useVehicle';
import { CreateVehicleDto, GetVehicleDto } from '@/api/vehicle/vehicle.types';
import useThrottle from '@/hooks/useThrottle';

const VehicleOperation: React.FC = () => {
  const [vehicleForm] = Form.useForm();
  const { vehicleId, tab } = useParams();
  const [currentVehicleDetails, setCurrentVehicleDetails] = useState<GetVehicleDto>(
    {} as GetVehicleDto
  );
  const notificationManager = useNotificationManager();
  const navigate = useNavigate();
  const { setPreventExit } = usePreventExits();
  const { t } = useLanguage();

  const {
    data: vehicleDetails,
    refetch: refetchVehicles,
    isLoading,
  } = vehicleServiceHooks.useEntity(vehicleId as string, {
    enabled: Boolean(vehicleId),
  });

  const createMutation = vehicleServiceHooks.useCreate({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('vehiclePage.notificationMessages.successAdded'),
      });
    },
  });

  const updateMutation = vehicleServiceHooks.useUpdate({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('vehiclePage.notificationMessages.successUpdate'),
      });
      await refetchVehicles();
    },
  });

  const isButtonLoading = createMutation.isPending || updateMutation.isPending || isLoading;

  const getVehicle = useCallback(async () => {
    if (vehicleId) {
      if (vehicleDetails) {
        vehicleForm.setFieldsValue({ ...vehicleDetails });
        setCurrentVehicleDetails(vehicleDetails);
      }
    } else {
      const response = await vehicleService.generateFleetId();
      vehicleForm.setFieldsValue({ ...response });
    }
  }, [vehicleDetails, vehicleForm, vehicleId]);

  useEffect(() => {
    getVehicle();
  }, [getVehicle, vehicleForm, vehicleId]);

  const breadCrumbObj: { [key: string]: string } = useMemo(() => {
    return {
      general: t('vehiclePage.breedCrumbs.general'),
      timeClockSession: t('vehiclePage.breedCrumbs.timeClockSession'),
    };
  }, [t]);

  const onFinish = useThrottle(async (formValues: CreateVehicleDto) => {
    if (isButtonLoading) return;

    const isUpdate = Boolean(vehicleId);

    if (isUpdate) {
      await updateMutation.mutateAsync({ id: vehicleId as string, data: formValues });
    } else {
      const response = await createMutation.mutateAsync(formValues);
      if (response.id) {
        navigate(
          ROUTES.LOGISTIC.LOGISTICS_VEHICLE_EDIT.replace(':vehicleId', response.id).replace(
            ':tab',
            'general'
          )
        );
      }
    }
    setPreventExit(false);
  }, 3000);

  const vehicleGeneralTabs: TabsProps['items'] = useMemo(() => {
    return [
      {
        key: 'general',
        label: t('vehiclePage.tabs.general'),
        children: (
          <VehicleGeneral
            form={vehicleForm}
            onFinish={onFinish}
            currentVehicleDetails={currentVehicleDetails || []}
            isButtonLoading={isButtonLoading}
          />
        ),
      },
      {
        key: 'timeClockSession',
        label: vehicleId ? (
          t('vehiclePage.tabs.timeClockSession')
        ) : (
          <Tooltip title={t('vehiclePage.tabs.tooltipText')}>
            {t('vehiclePage.tabs.timeClockSession')}
          </Tooltip>
        ),
        children: (
          <VehicleTimeClockSessions
            currentVehicleDetails={currentVehicleDetails}
            getVehicle={getVehicle}
          />
        ),
        disabled: !vehicleId,
      },
    ];
  }, [currentVehicleDetails, getVehicle, isButtonLoading, onFinish, t, vehicleForm, vehicleId]);

  const breedCrumbPath = useMemo(() => {
    const tabKey = tab || 'general';

    return [
      {
        name: vehicleId ? currentVehicleDetails.fleetId : t('vehiclePage.breedCrumbs.vehicle'),
        path: ROUTES.LOGISTIC.LOGISTICS_VEHICLE,
      },
      {
        name: tab ? breadCrumbObj[tab || 'general'] : t('vehiclePage.breedCrumbs.general'),
        path: vehicleId
          ? ROUTES.LOGISTIC.LOGISTICS_VEHICLE_EDIT.replace(':vehicleId', vehicleId!).replace(
              ':tab',
              tabKey
            )
          : ROUTES.LOGISTIC.LOGISTICS_VEHICLE_ADD.replace(':tab', tabKey),
      },
    ];
  }, [breadCrumbObj, currentVehicleDetails.fleetId, t, tab, vehicleId]);

  const onTabClickHandler = (tab: string) => {
    const currentRoute = vehicleId
      ? `${ROUTES.LOGISTIC.LOGISTICS_VEHICLE_EDIT.replace(':vehicleId', vehicleId)}`
      : ROUTES.LOGISTIC.LOGISTICS_VEHICLE_ADD;

    navigate(currentRoute.replace(':tab', tab));
  };

  return (
    <>
      <PageHeadingComponent
        title={vehicleId ? t('vehiclePage.generalHeader.edit') : t('vehiclePage.generalHeader.add')}
        isChildComponent
        onBackClick={() => navigate(ROUTES.LOGISTIC.LOGISTICS_VEHICLE)}
      />
      <PageBreadCrumbsComponent path={breedCrumbPath} />
      <div className="pr-5 pt-4">
        <TabsComponent tabs={vehicleGeneralTabs} onChange={onTabClickHandler} />
      </div>
    </>
  );
};

export default VehicleOperation;
