import React, { memo, useCallback, useMemo } from 'react';
import { Button, Checkbox, Form, Input, Select, Space } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import {
  minimumValueValidator,
  noWhitespaceValidator,
  numberFieldValidator,
} from '@/lib/FormValidators';
import CustomDivider from '@/components/common/divider/CustomDivider';
import { useParams } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import usePreventExits from '@/hooks/usePreventExits';
import { isFormChangedHandler } from '@/lib/helper';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { formErrorRegex } from '@/constant/Regex';
import { useConfig } from '@/contexts/ConfigContext';
import { infoCircleOutlined } from '@/assets';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import SelectDownArrow from '@/assets/icons/selectDownArrow';
import { vehicleTypeServiceHook } from '@/api/vehicle/vehicleTypes/useVehicleTypes';
import { IVehicleGeneral } from '../../vehicleTypes';

const VehicleGeneral: React.FC<IVehicleGeneral> = (props) => {
  const { form, onFinish, currentVehicleDetails = form.getFieldsValue(), isButtonLoading } = props;
  const { config } = useConfig();
  const { vehicleId } = useParams();
  const { t } = useLanguage();

  const { data: vehicleTypes } = vehicleTypeServiceHook.useList({ pageNumber: 1, pageSize: 100 });

  const vehicleTypesOptions = useMemo(() => {
    return vehicleTypes?.data?.map((type) => ({ value: type.id, label: type.name }));
  }, [vehicleTypes]);

  const packageTypeOptions = useMemo(
    () => [
      {
        value: 'Box',
        label: 'Box',
      },
      {
        value: 'Skid',
        label: 'Skid',
      },
      {
        value: 'Custom',
        label: 'Custom',
      },
    ],
    []
  );

  const { setIsBlocked } = useNavigationContext();
  const { setPreventExit } = usePreventExits();

  const onFieldsChangeHandler = (changesFields: any) => {
    if (changesFields.length <= 1) {
      const isIsChange = isFormChangedHandler(currentVehicleDetails, form.getFieldsValue(true), [
        'fleetId',
      ]);
      setPreventExit(isIsChange);
    } else if (changesFields.length > 1) {
      setIsBlocked(false);
      setPreventExit(false);
    }
  };

  const createYearOptions = useCallback(() => {
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let year = 1995; year <= currentYear; year++) {
      years.push({ value: year.toString(), label: year.toString() });
    }

    return years;
  }, []);

  const yearOptions = useMemo(() => createYearOptions(), [createYearOptions]);

  return (
    <Form
      name="vehicle-form"
      layout="vertical"
      className="custom-form"
      form={form}
      onFinish={onFinish}
      preserve={false}
      onFieldsChange={onFieldsChangeHandler}
    >
      <div className="form-fields-wrapper flex gap-2.5 flex-col">
        <CustomDivider label={t('vehiclePage.labels.basicDetails')} />
        <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
          <Form.Item
            label={t('vehiclePage.labels.vehicleType')}
            name="vehicleTypeId"
            rules={[{ required: true, message: t('vehiclePage.messages.vehicleTypeRequired') }]}
          >
            <Select
              options={vehicleTypesOptions}
              placeholder={t('vehiclePage.placeholders.selectType')}
              prefixCls="custom-select"
              suffixIcon={<SelectDownArrow />}
            />
          </Form.Item>
          <Form.Item
            label={t('vehiclePage.labels.fleetId')}
            name="fleetId"
            rules={[
              { required: true, message: t('vehiclePage.messages.fleetIdRequired') },
              {
                validator: (_, value) =>
                  noWhitespaceValidator(_, value, t('common.errors.noWhiteSpace')),
              },
            ]}
          >
            <Input
              placeholder={t('vehiclePage.placeholders.fleetId')}
              maxLength={255}
              disabled
              readOnly
            />
          </Form.Item>
          <Form.Item
            label={t('vehiclePage.labels.make')}
            name="make"
            validateFirst
            rules={[
              { required: true, message: t('vehiclePage.messages.makeRequired') },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              { whitespace: true, message: t('vehiclePage.messages.makeRequired') },
            ]}
          >
            <Input placeholder={t('vehiclePage.placeholders.make')} maxLength={20} />
          </Form.Item>
          <Form.Item
            label={t('vehiclePage.labels.model')}
            name="model"
            validateFirst
            rules={[
              { required: true, message: t('vehiclePage.messages.modelRequired') },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              { whitespace: true, message: t('vehiclePage.messages.modelRequired') },
            ]}
          >
            <Input placeholder={t('vehiclePage.placeholders.model')} maxLength={20} />
          </Form.Item>
          <Form.Item
            label={t('vehiclePage.labels.year')}
            name="year"
            rules={[{ required: true, message: t('vehiclePage.messages.yearRequired') }]}
          >
            <Select
              placeholder={t('vehiclePage.placeholders.year')}
              prefixCls="custom-select"
              options={yearOptions}
              suffixIcon={<SelectDownArrow />}
            />
          </Form.Item>
          <Form.Item
            label={t('vehiclePage.labels.license')}
            validateFirst
            name="licensePlate"
            rules={[
              { required: true, message: t('vehiclePage.messages.licenseRequired') },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              { whitespace: true, message: t('vehiclePage.messages.licenseRequired') },
            ]}
          >
            <Input placeholder={t('vehiclePage.placeholders.license')} maxLength={20} />
          </Form.Item>
        </div>
        <Form.Item
          validateFirst
          label={t('vehiclePage.labels.vin')}
          name="vin"
          rules={[
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
            { whitespace: true, message: t('vehiclePage.messages.vinRequired') },
          ]}
        >
          <Input placeholder={t('vehiclePage.placeholders.vin')} maxLength={17} />
        </Form.Item>
        <div className="grid gap-x-6 gap-y-2.5 grid-cols-1 sm:grid-cols-2 3xl:grid-cols-2">
          <Form.Item
            label={
              <span className="flex gap-1">
                {t('vehiclePage.labels.packageType')}
                <CustomTooltip title={t('vehiclePage.tooltip.packageType')}>
                  <img src={infoCircleOutlined} alt="info" />
                </CustomTooltip>
              </span>
            }
            name="packageType"
            rules={[{ required: true, message: t('vehiclePage.messages.packageTypeRequired') }]}
          >
            <Select
              options={packageTypeOptions}
              placeholder={t('vehiclePage.placeholders.selectType')}
              mode="multiple"
              suffixIcon={<SelectDownArrow />}
              menuItemSelectedIcon=""
              prefixCls="custom-select"
              optionRender={(option) => {
                return (
                  <Space>
                    <Checkbox checked={form.getFieldValue('packageType')?.includes(option.key)} />
                    {option.label}
                  </Space>
                );
              }}
            />
          </Form.Item>
          <Form.Item
            validateFirst
            label={
              <span className="flex gap-1">
                {t('vehiclePage.labels.capacity')}
                <span className="text-[#96A9B1] font-normal">({config.units?.weight})</span>
                <CustomTooltip title={t('vehiclePage.tooltip.capacity')}>
                  <img src={infoCircleOutlined} alt="info" />
                </CustomTooltip>
              </span>
            }
            name="maxWeight"
            rules={[
              { required: true, message: t('vehiclePage.messages.capacityRequired') },
              {
                validator: minimumValueValidator,
              },
            ]}
          >
            <Input
              placeholder={t('vehiclePage.placeholders.capacity')}
              onKeyDown={numberFieldValidator}
              maxLength={5}
            />
          </Form.Item>
          <Form.Item
            validateFirst
            label={
              <>
                <CustomTooltip title={t('vehiclePage.tooltip.branches')}>
                  <img src={infoCircleOutlined} alt="info" />
                </CustomTooltip>
                {t('vehiclePage.labels.branches')}
              </>
            }
            name="branch"
            rules={[
              {
                required: true,
                message: t('vehiclePage.messages.branchesRequired'),
              },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
              { whitespace: true, message: t('vehiclePage.messages.branchesRequired') },
            ]}
          >
            <Input placeholder={t('vehiclePage.placeholders.branches')} maxLength={255} />
          </Form.Item>
          <Form.Item
            validateFirst
            label={
              <>
                <span className="text-[#96A9B1] font-normal">({config.units?.distance})</span>
                {t('vehiclePage.labels.odometer')}
              </>
            }
            name="currentOdometer"
            rules={[
              { required: true, message: t('vehiclePage.messages.odometerRequired') },
              {
                validator: minimumValueValidator,
              },
            ]}
          >
            <Input
              placeholder={t('vehiclePage.placeholders.odometer')}
              onKeyDown={numberFieldValidator}
              maxLength={6}
            />
          </Form.Item>
        </div>
        <Form.Item
          validateFirst
          label={
            <>
              <CustomTooltip title={t('vehiclePage.tooltip.ownedBy')}>
                <img src={infoCircleOutlined} alt="info" />
              </CustomTooltip>
              {t('vehiclePage.labels.ownedBy')}
            </>
          }
          name={'ownedBy'}
          rules={[
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
            { whitespace: true, message: t('vehiclePage.messages.ownedByRequired') },
          ]}
        >
          <Input placeholder={t('vehiclePage.placeholders.ownedBy')} maxLength={100} />
        </Form.Item>

        <CustomDivider label={t('vehiclePage.labels.commentSection')} />

        <Form.Item
          label={
            <>
              <CustomTooltip title={t('vehiclePage.tooltip.notes')}>
                <img src={infoCircleOutlined} alt="info" />
              </CustomTooltip>
              {t('vehiclePage.labels.comments')}
            </>
          }
          name="notes"
        >
          <TextArea
            placeholder={t('vehiclePage.placeholders.comments')}
            maxLength={2500}
            className="custom-text-area"
          />
        </Form.Item>
      </div>
      <footer className="custom-modals-footer flex gap-4 justify-start py-4">
        <Button
          form="vehicle-form"
          loading={isButtonLoading}
          htmlType="submit"
          type="primary"
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600 px-6 h-[40px]"
        >
          {vehicleId ? t('common.update') : t('common.save')}
        </Button>
      </footer>
    </Form>
  );
};

export default memo(VehicleGeneral);
