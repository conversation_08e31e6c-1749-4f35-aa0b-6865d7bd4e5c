interface SearchCondition {
  query: string;
}
export function searchOrders(data: any[], condition: SearchCondition): any[] {
  const { query } = condition;

  return data.filter((item) => {
    // Normalize search string to lowercase for case-insensitive matching
    const normalizedQuery = query.toLowerCase();

    // Check all relevant fields for match
    return (
      item.trackingNumber?.toLowerCase().includes(normalizedQuery) ||
      item.assignee?.toLowerCase().includes(normalizedQuery) ||
      item.collectionCompanyName?.toLowerCase().includes(normalizedQuery) ||
      item.collectionTime?.toLowerCase().includes(normalizedQuery) ||
      item.customer?.toLowerCase().includes(normalizedQuery) ||
      item.deliveryCompanyName?.toLowerCase().includes(normalizedQuery) ||
      item.deliveryTime?.toLowerCase().includes(normalizedQuery) ||
      item.serviceLevel?.toLowerCase().includes(normalizedQuery)
    );
  });
}
