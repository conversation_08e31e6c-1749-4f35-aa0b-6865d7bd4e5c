.order-changes-collapse > .ant-collapse-item > .ant-collapse-content {
  border-top: 0px !important;
  background-color: #fffaeb;
}
.order-changes-collapse > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
  padding: 5px !important;
}
.orders-stepper > .ant-steps-item {
  width: 22% !important;
}
/* @media screen and (max-width: 1000px) {
  .orders-stepper > .ant-steps-item {
    width: 30% !important;
    padding-inline-start: 0px;
  }
} */
.orders-stepper > .ant-steps-item-active > .ant-steps-item-container > .ant-steps-item-icon {
  width: 30px !important;
  height: 30px !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #2d3484 !important;
  display: flex !important;
  border-color: #2d3484 !important;
}
.orders-stepper > .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-icon {
  width: 30px !important;
  height: 30px !important;
  align-items: center !important;
  justify-content: center !important;
  display: flex !important;
  border-color: #2d3484 !important;
  background-color: white !important;
}
.orders-stepper
  > .ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-icon
  > .ant-steps-icon {
  color: #2d3484 !important;
}
.orders-stepper > .ant-steps-item > .ant-steps-item-container > .ant-steps-item-icon > span {
  font-size: 13px;
}
.orders-stepper > .ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-icon {
  border-color: #2d3484 !important;
  background-color: white !important;
  width: 30px !important;
  height: 30px !important;
  align-items: center !important;
  justify-content: center !important;
  display: flex !important;
}
.orders-stepper
  > .ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title::after {
  background-color: #2d3484 !important;
}

.orders-stepper
  > .ant-steps-item-active
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title::after {
  background-color: #bfc2df !important;
}

.orders-stepper
  > .ant-steps-item-wait
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title::after {
  background-color: #bfc2df !important;
}
.orders-stepper
  > .ant-steps-item
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #090a1a !important;
}
.orders-stepper > .ant-steps-item > .ant-steps-item-container {
  display: flex;
  gap: 5px;
}
@media screen and (max-width: 1000px) {
  .orders-stepper > .ant-steps-item > .ant-steps-item-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0px;
  }
}
.order-changes-collapse > .ant-collapse-item > .ant-collapse-header > .ant-collapse-header-text {
  width: 97%;
}
@media screen and (max-width: 1000px) {
  .order-changes-collapse > .ant-collapse-item > .ant-collapse-header {
    width: 95%;
  }

  .orders-stepper
    > .ant-steps-item
    > .ant-steps-item-container
    > .ant-steps-item-content
    > .ant-steps-item-description {
    display: none !important;
  }
}
.order-details-collapse {
  display: flex;
  flex-direction: column;
  gap: 14px;
  background-color: white;
  padding: 10px;
}
.order-details-collapse > .ant-collapse-item {
  border: 1px solid #bfc2df !important;
  border-radius: 8px !important;
  padding-top: 2px;
}
.order-details-collapse > .ant-collapse-item > .ant-collapse-header {
  border-bottom: 1px solid #bfc2df !important;
  border-radius: 4px !important;
  background-color: #f5f6ff !important;
  font-weight: 600;
  color: #20363f;
}
.order-collapse-switch.ant-switch-checked {
  background-color: #2d3484 !important;
  border-color: #2d3484 !important;
}
.fields-dropdown-icon > svg {
  width: 20px;
  height: 20px;
  color: #2d3484;
}
.orders-general-datepicker-dropdown
  > .ant-picker-panel-container
  > .ant-picker-panel-layout
  > div
  > .ant-picker-footer
  > .ant-picker-ranges
  > .ant-picker-ok
  > .ant-btn {
  background-color: #2d3484 !important;
  border-color: #2d3484 !important;
  padding: 5px 10px !important;
}
.orders-general-datepicker-dropdown
  > .ant-picker-panel-container
  > .ant-picker-panel-layout
  > div
  .ant-picker-panel
  > .ant-picker-datetime-panel
  > .ant-picker-date-panel
  > .ant-picker-body
  > .ant-picker-content
  > tbody
  > tr
  > .ant-picker-cell-selected
  > .ant-picker-cell-inner {
  background-color: #2d3484 !important;
}
