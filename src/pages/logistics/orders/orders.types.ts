export interface IOrders {
  trackingNumber: string;
  status: string;
  assignee: string;
  collectionCompanyName: string;
  collectionTime: string;
  customer: string;
  dateSubmitted: string;
  deliveryCompanyName: string;
  deliveryTime: string;
  serviceLevel: string;
}
export interface IAssignedFilters {
  field: string;
  operator: string;
  value: string | any;
  label: string;
  type?: string;
}
export interface IQuickFilterKey {
  key: string;
  label: string;
}
