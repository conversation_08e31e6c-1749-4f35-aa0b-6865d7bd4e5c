import React, {
  ComponentType,
  SVGProps,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { IContextMenuItems, onContextMenuItemClickParams } from '@customTypes/ContextMenuTypes';
import { ITooltipParams, CellContextMenuEvent, ICellRendererParams } from 'ag-grid-community';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import {
  AssignToOutlined,
  ClearCloseIcon,
  DeleteIcon,
  EyeIcon,
  infoCircleOutlined,
  NotificationBellIcon,
  UnAssignOutlined,
  UpdateStatusIcon,
} from '@/assets';
import { GridNames } from '@/types/AppEvents';
import { IColDef } from '@/types/AgGridTypes';
import { Button, Divider, Dropdown, MenuProps, Select, Tag } from 'antd';
import ColumnManage from '@/components/specific/columnManage';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import Icon, { DownOutlined } from '@ant-design/icons';
import { searchOrders } from './ordersApi';
import { FilterCondition, filterData } from '@/pages/location/address/addressApi';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import CustomModal from '@/components/common/modal/CustomModal';
import { useLanguage } from '@/hooks/useLanguage';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { IQuickFilterKey } from './orders.types';
import { AgGridReact } from 'ag-grid-react';
import { on } from '@/contexts/PulseContext';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { defaultPagination } from '@/constant/generalConstant';

interface IOrders {
  trackingNumber: string;
  status: string;
  assignee: string;
  collectionCompanyName: string;
  collectionTime: string;
  customer: string;
  dateSubmitted: string;
  deliveryCompanyName: string;
  deliveryTime: string;
  serviceLevel: string;
}
interface IAssignedFilters {
  field: string;
  operator: string;
  value: string;
  label: string;
}

const OrdersPage: React.FC = () => {
  const [quickFilters, setQuickFilters] = useState<
    { label: string; key: string; assignedFilters: IAssignedFilters[] }[]
  >([]);
  const { t } = useLanguage();
  const [rowData, setRowData] = useState<IOrders[]>([]);
  const [ordersFilter, setOrdersFilter] = useState<IOrders[]>([]);
  const [searchText, setSearchText] = useState('');
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_filterParams, setFilterParams] = useState(defaultPagination);
  const [currentSelectedFilter, setCurrentSelectedFilter] = useState<string>(
    t('ordersPage.quickFilter')
  );
  const [isModalOpenToAssign, setIsModalOpenToAssign] = useState(false);
  const navigate = useNavigate();
  useEffect(() => {
    const transportOrders = localStorage.getItem('ordersGrid');
    const parsedOrders = JSON.parse(transportOrders ?? '[]');
    setRowData(parsedOrders);
    setOrdersFilter(parsedOrders);
  }, []);
  const gridRef = useRef<AgGridReact<IOrders>>(null);

  const ordersContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('contextMenuItems.customer.open'),
        key: 'Open',
        onClick: () => {
          alert('from click');
        },
        icon: EyeIcon,
      },
      {
        label: t('contextMenuItems.customer.assignTo'),
        icon: AssignToOutlined,
        key: 'assignTo',
        onClick: async () => {
          setIsModalOpenToAssign(true);
        },
      },
      {
        label: t('ordersPage.unassign'),
        icon: UnAssignOutlined,
        key: 'unAssign',
        onClick: async (params: onContextMenuItemClickParams) => {
          params.closeContextMenu();
        },
      },
      {
        label: t('ordersPage.updateStatus'),
        icon: UpdateStatusIcon,
        key: 'updateStatus',
        subMenu: [
          { label: t('ordersPage.setToInTransit'), key: 'SetToInTransit' },
          { label: t('ordersPage.setToDelivered'), key: 'SetToInDelivered' },
          { label: t('ordersPage.setToPickup'), key: 'SetToInPickup' },
        ],
      },
      {
        label: t('ordersPage.sendNotification'),
        icon: NotificationBellIcon,
        key: 'SendNotification',
        subMenu: [
          {
            label: t('ordersPage.sendStatusToCustomer'),
            key: 'SendStatusToCustomer',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
          {
            label: t('ordersPage.sendStatusToReceiver'),
            key: 'SendStatusToReceiver',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
          {
            label: t('ordersPage.sendStatusToCollector'),
            key: 'SendStatusToCollector',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
          {
            label: t('ordersPage.sendStatusToDriver'),
            key: 'SendStatusToDriver',
            subMenu: [
              {
                label: t('dashboard.customer.settings.settingsNotification.sms'),
                key: 'CustomerSms',
              },
              {
                label: t('dashboard.customer.settings.settingsNotification.email'),
                key: 'CustomerEmail',
              },
            ],
          },
        ],
      },
      {
        label: <span className="text-red-500">{t('common.delete')}</span>,
        icon: DeleteIcon,
        key: 'delete',
      },
    ];
  }, [t]);

  const tooltipValueGetter = useCallback(
    (params: ITooltipParams) =>
      params.value == null || params.value === '' ? '- Missing -' : params.value,
    []
  );

  const ordersColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'trackingNumber',
        headerName: t('ordersPage.trackingNumber'),
        unSortIcon: true,
        type: 'string',
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'assignee',
        minWidth: 170,
        unSortIcon: true,
        type: 'string',
        headerName: t('ordersPage.assignee'),
        tooltipValueGetter: tooltipValueGetter,
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'collectionCompanyName',
        headerName: t('ordersPage.collectionTime'),
        tooltipValueGetter: tooltipValueGetter,
        unSortIcon: true,
        type: 'string',
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'collectionTime',
        headerName: t('ordersPage.collectionTime'),
        unSortIcon: true,
        type: 'string',
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'customer',
        headerName: t('sidebar.customer'),
        unSortIcon: true,
        type: 'string',
        visible: true,
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'dateSubmitted',
        headerName: t('ordersPage.dateSubmitted'),
        visible: true,
        unSortIcon: true,
        type: 'string',
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'deliveryCompanyName',
        headerName: t('ordersPage.deliveryCompany'),
        visible: true,
        unSortIcon: true,
        type: 'string',
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'deliveryTime',
        headerName: t('ordersPage.deliveryTime'),
        visible: true,
        unSortIcon: true,
        type: 'string',
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'serviceLevel',
        headerName: t('dashboard.customer.services.colDefs.serviceLevel'),
        visible: true,
        unSortIcon: true,
        type: 'string',
        cellRenderer: (params: ICellRendererParams) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'status',
        headerName: t('dashboard.customer.columns.status'),
        visible: true,
        unSortIcon: true,
        type: 'string',
        cellRenderer: (params: ICellRendererParams) => {
          return params.data.status ? (
            <div className="flex gap-3 h-full items-center">
              <span className="h-[10px] w-[10px] rounded-full bg-[seagreen]  flex" />
              {t('dashboard.customer.columns.active')}
            </div>
          ) : (
            <div className="flex gap-3 h-full items-center">
              <span className="h-[10px] w-[10px] rounded-full bg-error-500  flex" />
              {t('dashboard.customer.columns.inactive')}
            </div>
          );
        },
      },

      {
        field: 'actions',
        headerName: t('zonePage.colDefs.action'),
        width: 90,
        visible: true,
        pinned: 'right',
        cellRenderer: (params: ICellRendererParams) => {
          return (
            <div className="flex gap-2 h-full items-center w-full overflow-hidden">
              <Icon
                component={EyeIcon as ComponentType<SVGProps<SVGSVGElement>>}
                className="cursor-pointer"
                onClick={() => {
                  navigate(
                    ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(
                      ':id',
                      params.data.trackingNumber
                    ).replace(':tab', 'general')
                  );
                }}
              />
              <Icon component={DeleteIcon} className="cursor-pointer" />
            </div>
          );
        },
      },
    ];
  }, [searchText, t, tooltipValueGetter]);
  const onContextMenu = useCallback((_params: CellContextMenuEvent) => {}, []);
  const searchHandler = useCallback(
    (value: string) => {
      const results = searchOrders(ordersFilter, {
        query: value,
      });
      setSearchText(value);
      setRowData(results);
    },
    [ordersFilter]
  );
  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.vehicleGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filteredResults = filterData(ordersFilter, data.filters as FilterCondition[]);
      setRowData(filteredResults);
      for (let i = 0; i < data.filters.length; i++) {
        const column = data.filters[i];
        const columnDef = ordersColDefs.find((col) => col.field === column.field);
        if (columnDef) {
          setSelectedQuickFilterData(data.filters);
        }
      }
    },
    [ordersColDefs, ordersFilter]
  );

  const clearAllToDefault = () => {
    setSearchText('');
    setCurrentSelectedFilter(t('ordersPage.quickFilter'));
    setSelectedQuickFilterData([]);
    setRowData(ordersFilter);
  };
  const handlerQuickFilter = useCallback(
    (filters: string, data: IAssignedFilters[]) => {
      setQuickFilters([...quickFilters, { label: filters, key: filters, assignedFilters: data }]);
      setCurrentSelectedFilter(filters);
      for (let i = 0; i < data.length; i++) {
        const column = data[i];
        const columnDef = ordersColDefs.find((col) => col.field === column.field);
        column.label = columnDef?.headerName as string;
        if (columnDef) {
          setSelectedQuickFilterData(data);
        }
      }
    },
    [ordersColDefs, quickFilters]
  );
  const handleQuickFilterClick = useCallback(
    (selectedFilter: { key: string; label: string }) => {
      const filter = quickFilters.find((filter) => filter?.key === selectedFilter?.key);
      setCurrentSelectedFilter(filter?.label as string);
      const filteredResults = filterData(
        ordersFilter,
        filter?.assignedFilters as FilterCondition[]
      );
      setRowData(filteredResults);
      setSelectedQuickFilterData(filter?.assignedFilters as IAssignedFilters[]);
    },
    [ordersFilter, quickFilters]
  );
  const handleDeleteQuickFilter = useCallback(
    (selectedFilter: string) => {
      customAlert.error({
        message: t('ordersPage.areYouSureDeleteQuickFilter'),
        title: t('ordersPage.deleteQuickFilter'),
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
        firstButtonFunction: () => {
          const filter = quickFilters.find((filter) => filter?.key === selectedFilter);
          if (filter) {
            const filteredQuickFilters = quickFilters.filter(
              (filteredItem) => filteredItem?.key !== filter?.key
            );
            setQuickFilters(filteredQuickFilters);
          }
          clearAllToDefault();

          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      });
    },
    [quickFilters]
  );

  const clearQuickFilterFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const menuItems: MenuProps['items'] =
    quickFilters?.length > 0
      ? quickFilters.map((filter) => ({
          key: filter.key,
          label: (
            <div className="flex gap-2 justify-between items-center ">
              <span>{filter.label}</span>
              <Icon
                component={DeleteIcon}
                className="cursor-pointer w-[14px] h-[14px]"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteQuickFilter(filter.key);
                }}
              />
            </div>
          ),
        }))
      : [
          {
            label: t('ordersPage.noQuickFiltersAvailable'),
            key: '1',
            onClick: (e: any) => {
              e.preventDefault();
            },
          },
        ];

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button
          onClick={() => {
            setIsModalOpenToAssign(false);
          }}
          className="custom-antd-outlined"
        >
          {t('common.cancel')}
        </Button>
        <Button
          htmlType="submit"
          type="primary"
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
        >
          {t('ordersPage.assign')}
        </Button>
      </footer>
    ),
    [t]
  );
  return (
    <>
      <CustomModal
        className="!w-[450px]"
        modalTitle={t('contextMenuItems.customer.assignTo')}
        modalDescription={t('ordersPage.selectDriverToAssign')}
        footer={Footer}
        open={isModalOpenToAssign}
        onCancel={() => {
          setIsModalOpenToAssign(false);
        }}
      >
        <div className="flex flex-col w-full gap-2">
          <div className="flex gap-2 items-center">
            <span className="font-semibold">{t('ordersPage.driver')}</span>
            <CustomTooltip>
              <img src={infoCircleOutlined} />
            </CustomTooltip>
          </div>
          <Select
            placeholder={t('ordersPage.selectDriver')}
            className="h-[40px]"
            options={[
              { label: 'Driver A', value: 'DriverA' },
              { label: 'Driver B', value: 'DriverB' },
              { label: 'Driver C', value: 'DriverC' },
            ]}
          />
        </div>
      </CustomModal>
      <div className="flex h-screen">
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
            <div className="md:w-1/3 flex flex-col 3xsm:w-full">
              <PageHeadingComponent title={t('sidebar.orders')} />
            </div>
            <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
              <div className="flex gap-3">
                <SearchFilterComponent
                  searchedValues={searchHandler}
                  colDefs={ordersColDefs}
                  onFilterApply={applyFilters}
                  isSetQuickFilter={true}
                  searchInputPlaceholder={t('ordersPage.searchOrders')}
                  setQuickFilters={(filters: string, data: IAssignedFilters[]) =>
                    handlerQuickFilter(filters, data)
                  }
                  setSelectedQuickFilterData={setSelectedQuickFilterData}
                  quickFilterEventKey={'OrderQuickFilter'}
                  quickFilterSettingsKey={'OrderQuickFilter'}
                  quickFilterTitleEventKey={'OrderQuickFilterTitleEvent'}
                  clearAllFunctionRef={clearQuickFilterFunctionRef}
                  setFilterParams={setFilterParams}
                />

                <ColumnManage colDefs={ordersColDefs} gridName={GridNames.orderGrid} />
              </div>
              <div className="pt-5">
                <Divider type="vertical" className="hidden md:flex h-[40px] !m-0" />
              </div>
              <div className="pt-0 md:pt-5">
                <Dropdown
                  className="h-[40px]"
                  trigger={['click']}
                  menu={{
                    selectedKeys: [currentSelectedFilter],
                    selectable: true,
                    items: menuItems,
                    onClick: (e) => {
                      handleQuickFilterClick(e as unknown as IQuickFilterKey);
                    },
                  }}
                >
                  <Button iconPosition="end" icon={<DownOutlined />}>
                    {currentSelectedFilter}
                  </Button>
                </Dropdown>
              </div>
            </div>
          </div>
          <main className=" h-screen overflow-x-hidden overflow-y-auto bg-white">
            {selectedQuickFilterData?.length > 0 && (
              <div className="w-full flex pt-3 gap-2 items-center max-w-[95%] flex-wrap">
                <span className="text-[14px] text-[#20363F] font-[500]">
                  {t('ordersPage.appliedFilter')}:
                </span>
                {selectedQuickFilterData.map((filter) => {
                  const matchingColumn = ordersColDefs.find((col) => col.field === filter.field);
                  return (
                    <Tag key={filter.field} className="flex gap-2 h-[32px] items-center">
                      <span className="text-[#0876A4] text-[14px]">
                        {matchingColumn ? matchingColumn.headerName : filter.label}:
                      </span>
                      <span className="text-[14px]">{filter.value}</span>
                    </Tag>
                  );
                })}
                <Tag
                  onClick={() => clearAllToDefault()}
                  className="flex gap-2 h-[32px] items-center cursor-pointer"
                >
                  <img src={ClearCloseIcon} className="w-[20px] h-[20px]" />
                  <span className="text-[14px]">{t('searchFilterBox.clearAll')}</span>
                </Tag>
              </div>
            )}

            <div className="mx-auto pr-6 py-5 flex justify-center items-center">
              <CustomAgGrid
                columnDefs={ordersColDefs}
                rowData={rowData}
                pagination
                tooltipMouseTrack
                isContextMenu={true}
                contextMenuItem={ordersContextMenuItems}
                onContextMenu={onContextMenu}
                gridName={GridNames.orderGrid}
                emptyState={{
                  title: t('ordersPage.noOrdersFound'),
                  description: '',
                }}
                gridId="gridWrapperForChildren"
                gridRef={gridRef}
              />
            </div>
          </main>
        </div>
      </div>
    </>
  );
};

export default OrdersPage;
