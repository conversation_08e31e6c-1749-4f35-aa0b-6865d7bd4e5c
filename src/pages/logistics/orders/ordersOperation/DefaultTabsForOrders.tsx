import { TabsProps } from 'antd';
import OrdersGeneralComponent from './OrdersGeneral';
import OrdersPricesBreakdownComponent from './OrderPriceBreakdown/OrdersPricesBreakdown';
import OrdersPackagesComponent from './orderPackages/OrdersPackages';

import OrdersHistoryComponent from './OrdersHistory';
import { translator } from '@/i18n/languageLoader';
import OrdersAttachmentsComponent from './orderAttachments/OrdersAttachments';

export const DefaultTabsForOrders = (): TabsProps['items'] => [
  {
    key: 'general',
    label: translator('sidebar.general'),
    children: <OrdersGeneralComponent />,
    tabKey: 'General',
  },
  {
    key: 'pricesBreakdown',
    label: translator('ordersPage.pricesBreakdown'),
    children: <OrdersPricesBreakdownComponent />,
    tabKey: 'Prices Breakdown',
  },
  {
    key: 'packages',
    label: translator('ordersPage.packages'),
    children: <OrdersPackagesComponent />,
    tabKey: 'Packages',
  },
  {
    key: 'attachments',
    label: translator('ordersPage.attachments'),
    children: <OrdersAttachmentsComponent />,
    tabKey: 'Attachments',
  },
  {
    key: 'history',
    label: translator('priceSetPage.tabs.history'),
    children: <OrdersHistoryComponent />,
    tabKey: 'History',
  },
];
