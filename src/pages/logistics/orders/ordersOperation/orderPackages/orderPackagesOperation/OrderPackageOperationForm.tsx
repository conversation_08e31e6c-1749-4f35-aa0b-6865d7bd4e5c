import { useLanguage } from '@/hooks/useLanguage';
import { IOrderPackageOperationFormProps } from './orderPackagesOperationForm.type';
import { Form, Input, InputNumber, Select } from 'antd';
import { useConfig } from '@/contexts/ConfigContext';
import { numberParser } from '@/lib/helper/formHelper';
import { Rule } from 'antd/es/form';
import CustomUpload from '@/components/common/customUpload/CustomUpload';

const packagesOptions = [
  {
    value: 'Envelop',
    label: 'Envelop',
  },
  {
    value: 'Small Box',
    label: 'Small Box',
  },
  {
    value: 'Large Box',
    label: 'Large Box',
  },
  {
    value: 'Container',
    label: 'Container',
  },
];

const OrderPackageOperationForm = ({ form, onFinish }: IOrderPackageOperationFormProps) => {
  const dimensionValidator = (_: Rule, value: number) => {
    if (value > 100000) {
      return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
    }
    if (value <= 0) {
      return Promise.reject(new Error(t('ordersPage.packagesTab.mustBeMoreThan1')));
    }

    return Promise.resolve();
  };

  const { t } = useLanguage();
  const { config } = useConfig();
  const dimensionFieldsWatcher = Form.useWatch([], form);

  return (
    <div className="w-full md:w-[586px] max-w-[684px]">
      <Form
        name="addCustomPrice"
        className="custom-form"
        layout={'vertical'}
        form={form}
        preserve={false}
        onFinish={onFinish}
      >
        <div className="flex flex-col gap-4">
          <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:grid-cols-2">
            <Form.Item
              label={t('ordersPage.packagesTab.FormLabels.packageType')}
              validateFirst
              name="packageType"
              rules={[
                { required: true, message: t('ordersPage.packagesTab.requiredPackageType') },
                {
                  validator: dimensionValidator,
                },
              ]}
            >
              <Select
                options={packagesOptions}
                placeholder={t('ordersPage.packagesTab.placeholderPackageType')}
                prefixCls="custom-select"
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
            <Form.Item
              label={t('ordersPage.packagesTab.FormLabels.quantity')}
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredQuantity'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
              name="quantity"
            >
              <InputNumber
                inputMode="decimal"
                pattern="[0-9]*[.,]?[0-9]*"
                type="number"
                className="bulk-adjust-input w-full"
                placeholder="0"
                step={1}
                parser={numberParser}
                min={1}
              />
            </Form.Item>
            <Form.Item
              label={`${t('ordersPage.packagesTab.FormLabels.length')} (${config.units?.dimension})`}
              name="length"
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredLength'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
            >
              <InputNumber
                inputMode="decimal"
                pattern="[0-9]*[.,]?[0-9]*"
                type="number"
                className="bulk-adjust-input w-full"
                placeholder="0"
                step={0.01}
                parser={numberParser}
                min={0}
              />
            </Form.Item>
            <Form.Item
              label={`${t('ordersPage.packagesTab.FormLabels.width')} (${config.units?.dimension})`}
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredWidth'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
              name="width"
            >
              <InputNumber
                inputMode="decimal"
                pattern="[0-9]*[.,]?[0-9]*"
                type="number"
                className="bulk-adjust-input w-full"
                placeholder="0"
                step={0.01}
                parser={numberParser}
                min={0}
              />
            </Form.Item>
            <Form.Item
              label={`${t('ordersPage.packagesTab.FormLabels.height')} (${config.units?.dimension})`}
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredHeight'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
              name="height"
            >
              <InputNumber
                inputMode="decimal"
                pattern="[0-9]*[.,]?[0-9]*"
                type="number"
                className="bulk-adjust-input w-full"
                placeholder="0"
                step={0.01}
                parser={numberParser}
                min={0}
              />
            </Form.Item>
            <Form.Item
              label={`${t('ordersPage.packagesTab.FormLabels.CombinedWeight')} (${config.units?.weight})`}
              validateFirst
              rules={[
                {
                  required: true,
                  message: t('ordersPage.packagesTab.requiredWeight'),
                },
                {
                  validator: dimensionValidator,
                },
              ]}
              name="combinedWeight"
            >
              <InputNumber
                maxLength={3}
                inputMode="decimal"
                pattern="[0-9]*[.,]?[0-9]*"
                type="number"
                className="bulk-adjust-input w-full"
                placeholder="0"
                step={0.01}
                parser={numberParser}
                min={0}
              />
            </Form.Item>
          </div>
          <Form.Item label={t('ordersPage.packagesTab.FormLabels.CubicDimension')}>
            <Input
              disabled
              value={`${dimensionFieldsWatcher?.length || 0} x ${dimensionFieldsWatcher?.width || 0} x ${dimensionFieldsWatcher?.height || 0} `}
            />
          </Form.Item>

          <Form.Item name={'image'}>
            <CustomUpload name="image" form={form} />
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default OrderPackageOperationForm;
