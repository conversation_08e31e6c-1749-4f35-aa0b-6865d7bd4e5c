import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import CustomModal from '@/components/common/modal/CustomModal';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { IndexedDBStore } from '@/lib/indexedDB';
import { StoreName } from '@/lib/indexedDB/databaseConfig';
import { IColDef } from '@/types/AgGridTypes';
import { ValueFormatterParams, ICellRendererParams } from 'ag-grid-community';
import { Button, Form, Image } from 'antd';
import { useEffect, useState } from 'react';
import { IPriceBreakdown } from '../OrderPriceBreakdown/orderPriceBreakdown.types';
import { EyeIcon, PlusButtonIcon } from '@/assets';
import Icon from '@ant-design/icons';
import { IPackage, IPackageModelState } from './orderPackages.types';
import { useConfig } from '@/contexts/ConfigContext';
import { ImageFormateIcon } from '@/assets/icons/imageFormateIcon';
import OrderPackageOperationForm from './orderPackagesOperation/OrderPackageOperationForm';

const packagesStore = new IndexedDBStore<IPackage>(StoreName.packageType);

export interface IImagePreviewState {
  isPreviewVisible: boolean;
  src: undefined | string;
}

const OrdersPackagesComponent = () => {
  const [priceBreakdownList, setPriceBreakdownList] = useState<IPackage[]>();
  const [isModelOpen, setIsModelOpen] = useState<IPackageModelState>({
    isOpen: false,
    isEdit: false,
    packageId: undefined,
  });
  const { t } = useLanguage();

  const [packagesForm] = Form.useForm();
  const [visible, setVisible] = useState<IImagePreviewState>({
    isPreviewVisible: false,
    src: undefined,
  });

  const getPackagesList = async () => {
    try {
      const response = await packagesStore.getAll();
      setPriceBreakdownList(response);
    } catch (error) {
      // console.log({ error });
    }
  };

  const { config } = useConfig();

  useEffect(() => {
    getPackagesList();
  }, []);

  const onFinish = async (values: any) => {
    try {
      if (isModelOpen.packageId) {
        await packagesStore.update(isModelOpen.packageId, {
          ...values,
          image: 'Envelop.png',
        });
      } else {
        await packagesStore.put({
          ...values,
          id: packagesStore.generateKey(),
          image: 'Envelop.png',
        });
      }
      await getPackagesList();
      setIsModelOpen({ isOpen: false, isEdit: false, packageId: undefined });
    } catch (error) {
      // console.log(error);
    }
  };

  const colDefs: IColDef[] = [
    {
      field: `packageType`,
      headerName: t('ordersPage.packagesTab.colDefs.packageType'),
      type: 'text',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 130,
      flex: 1,
    },
    {
      field: `quantity`,
      headerName: t('ordersPage.packagesTab.colDefs.quantity'),
      type: 'number',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 130,
      flex: 1,
    },
    {
      field: `combinedWeight`,
      headerName: `${t('ordersPage.packagesTab.colDefs.CombinedWeight')} (${config.units?.weight})`,
      type: 'number',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 130,
      flex: 1,
    },
    {
      headerName: `${t('ordersPage.packagesTab.colDefs.CubicDimension')} (${config.units?.dimension})`,
      type: 'text',
      visible: true,
      sortable: false,
      unSortIcon: false,
      minWidth: 130,
      flex: 1,
      valueFormatter: (params: ValueFormatterParams<IPackage>) => {
        return `${params.data?.length} x ${params.data?.width} x ${params.data?.height}`;
      },
    },
    {
      //TODO:
      field: `image`,
      headerName: t('ordersPage.packagesTab.colDefs.image'),
      type: 'text',
      visible: true,
      sortable: false,
      unSortIcon: false,
      minWidth: 130,
      flex: 1,
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={() => setVisible({ isPreviewVisible: true, src: params.data.image })}
          >
            <Icon component={ImageFormateIcon} />
            {params.value}
          </div>
        );
      },
    },
    {
      headerName: t('vehiclePage.colDefs.action'),
      pinned: 'right',
      maxWidth: 70,
      minWidth: 70,
      sortable: false,
      resizable: false,
      visible: true,
      cellRenderer: (params: ICellRendererParams<IPriceBreakdown>) => {
        return (
          <div className="flex gap-2 h-full justify-center items-center">
            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view"
              value={'view'}
              onClick={() => {
                packagesForm.setFieldsValue(params.data);
                setIsModelOpen({
                  isOpen: true,
                  isEdit: true,
                  packageId: params.data?.id,
                });
              }}
            />
          </div>
        );
      },
    },
  ];

  return (
    <div className="h-full">
      <CustomModal
        modalTitle={
          isModelOpen.isEdit
            ? t('ordersPage.packagesTab.viewPackage')
            : t('ordersPage.packagesTab.addPackage')
        }
        modalDescription={
          isModelOpen.isEdit
            ? t('ordersPage.packagesTab.viewPackageDesc')
            : t('ordersPage.packagesTab.addPackageDesc')
        }
        open={isModelOpen.isOpen}
        destroyOnClose
        maskClosable={false}
        onCancel={() => setIsModelOpen({ isOpen: false, isEdit: false, packageId: undefined })}
        className="!h-[80%] flex items-center"
        footer={
          <div className="flex gap-2 w-full justify-end">
            <Button
              onClick={() =>
                setIsModelOpen({
                  isOpen: false,
                  isEdit: false,
                  packageId: undefined,
                })
              }
              className="hover:!text-black hover:!border-gray-300"
            >
              {t('common.cancel')}
            </Button>
            {
              <Button
                className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
                htmlType="submit"
                form="addCustomPrice"
                onClick={() => {
                  packagesForm.getFieldError('image');
                }}
              >
                {isModelOpen.isEdit ? t('common.update') : t('common.add')}
              </Button>
            }
          </div>
        }
      >
        <OrderPackageOperationForm form={packagesForm} onFinish={onFinish} />
      </CustomModal>
      <div className="pr-4">
        <Image
          width={200}
          style={{ display: 'none' }}
          src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?x-oss-process=image/blur,r_50,s_50/quality,q_1/resize,m_mfit,h_200,w_200"
          preview={{
            visible: visible.isPreviewVisible,
            src:
              // visible.src ||
              'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
            onVisibleChange: (value) => {
              setVisible({ ...visible, isPreviewVisible: value });
            },
          }}
        />
        <header className="flex justify-end items-center py-4">
          <Button
            className=" h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
            icon={<PlusButtonIcon />}
            onClick={() => {
              setIsModelOpen({ isOpen: true, isEdit: false, packageId: undefined });
            }}
          >
            {t('ordersPage.packagesTab.addPackage')}
          </Button>
        </header>
        <main>
          <CustomAgGrid
            rowData={priceBreakdownList}
            columnDefs={colDefs}
            className="!h-[74vh] lg:!h-[75vh]"
            gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
          />
        </main>
      </div>
    </div>
  );
};
export default OrdersPackagesComponent;
