import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { IndexedDBStore } from '@/lib/indexedDB';
import { StoreName } from '@/lib/indexedDB/databaseConfig';
import { IColDef } from '@/types/AgGridTypes';
import { ICellRendererParams, ValueFormatterParams } from 'ag-grid-community';
import { Button, Form, Input, InputNumber } from 'antd';
import { IModelOpenState, IPriceBreakdown } from './orderPriceBreakdown.types';
import { useEffect, useState } from 'react';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { EditPopupWhiteIcon } from '@/assets/icons/editPopupIconWhite';
import CustomModal from '@/components/common/modal/CustomModal';
import { useLanguage } from '@/hooks/useLanguage';
import { formErrorRegex } from '@/constant/Regex';
import Icon from '@ant-design/icons';
import { EyeIcon, HistoryIcon } from '@/assets';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { Rule } from 'antd/es/form';
import { numberParser } from '@/lib/helper/formHelper';

const priceBreakdownStore = new IndexedDBStore<IPriceBreakdown>(StoreName.priceBreakdown);

const OrdersPricesBreakdownComponent = () => {
  const [priceBreakdownList, setPriceBreakdownList] = useState<IPriceBreakdown[]>();
  const [isModifyPricingModelOpen, setIsModifyPricingModelOpen] = useState<IModelOpenState>({
    open: false,
    isEdit: false,
    customPriceId: undefined,
  });

  const getPriceBreakdownList = async () => {
    try {
      const response = await priceBreakdownStore.getAll();
      setPriceBreakdownList(response);
    } catch (error) {
      // console.log({ error });
    }
  };

  const { t } = useLanguage();

  useEffect(() => {
    getPriceBreakdownList();
  }, []);

  const addNewCustomPrice = async (values: IPriceBreakdown) => {
    try {
      if (isModifyPricingModelOpen.customPriceId) {
        await priceBreakdownStore.update(isModifyPricingModelOpen.customPriceId, {
          ...values,
          description: values.description || 'NA',
        });
      } else {
        await priceBreakdownStore.put({
          ...values,
          id: priceBreakdownStore.generateKey(),
          description: 'NA',
        });
      }
      await getPriceBreakdownList();
      setIsModifyPricingModelOpen({ open: false, isEdit: false, customPriceId: undefined });
    } catch (error) {
      // console.log(error);
    }
  };

  const [customPricingForm] = Form.useForm();

  const colDefs: IColDef[] = [
    {
      field: `appliedModifier`,
      headerName: t('ordersPage.priceBreakDown.colDefs.appliedModifier'),
      type: 'text',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 300,
      flex: 1,
    },
    {
      field: `appliedCharges`,
      headerName: t('ordersPage.priceBreakDown.colDefs.appliedCharges'),
      type: 'text',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 130,
      flex: 1,
      valueFormatter: (params: ValueFormatterParams<IPriceBreakdown>) => {
        return `$${params.value}`;
      },
    },
    {
      field: `description`,
      headerName: t('ordersPage.priceBreakDown.colDefs.description'),
      type: 'text',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 150,
      flex: 1,
    },
    {
      field: 'action',
      headerName: t('vehiclePage.colDefs.action'),
      pinned: 'right',
      maxWidth: 80,
      minWidth: 80,
      sortable: false,
      resizable: false,
      visible: true,
      cellRenderer: (params: ICellRendererParams<IPriceBreakdown>) => {
        return (
          <div className="flex gap-2 h-full items-center">
            <CustomTooltip
              content={
                <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                  {t('common.added')}:{' '}
                  <span className="block w-fit text-sm font-semibold">
                    {dateFormatter(params?.data?.createdAt as string)}
                  </span>
                  <hr className="border-[#0000001c]" />
                  {t('common.modified')}:{' '}
                  <span className="block w-fit text-sm font-semibold">
                    {dateFormatter(params?.data?.updatedAt as string)}
                  </span>
                  <hr className="border-[#0000001c]" />
                  {t('common.lastUpdatedBy')}:
                  <span className="block w-fit text-sm font-semibold">
                    {(params.data?.updatedBy as string) || t('common.notUpdatedYet')}
                  </span>
                </div>
              }
              placement="leftTop"
            >
              <div>
                <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
              </div>
            </CustomTooltip>
            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view"
              value={'view'}
              onClick={() => {
                customPricingForm.setFieldsValue(params.data);
                setIsModifyPricingModelOpen({
                  open: true,
                  isEdit: true,
                  customPriceId: params.data?.id,
                });
              }}
            />
          </div>
        );
      },
    },
  ];

  const amountValidator = (_: Rule, value: number) => {
    if (value > 100000) {
      return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
    }
    if (value === 0) {
      return Promise.reject(new Error(t('ordersPage.priceBreakDown.negativeOrPositiveValue')));
    }
    return Promise.resolve();
  };

  return (
    <div className="h-full flex flex-col gap-4 pr-4">
      <CustomModal
        modalTitle={
          isModifyPricingModelOpen.isEdit
            ? t('ordersPage.priceBreakDown.customPricingModelTitleEditMode')
            : t('ordersPage.priceBreakDown.customPricingModelTitle')
        }
        modalDescription={
          isModifyPricingModelOpen.isEdit
            ? t('ordersPage.priceBreakDown.customPricingModelDescEditMode')
            : t('ordersPage.priceBreakDown.customPricingModelDesc')
        }
        open={isModifyPricingModelOpen.open}
        destroyOnClose
        maskClosable={false}
        onCancel={() =>
          setIsModifyPricingModelOpen({ open: false, isEdit: false, customPriceId: undefined })
        }
        className="!w-[450px] !h-[80%] flex items-center"
        footer={
          <div className="flex gap-2 w-full justify-end">
            <Button
              onClick={() =>
                setIsModifyPricingModelOpen({
                  open: false,
                  isEdit: false,
                  customPriceId: undefined,
                })
              }
              className="hover:!text-black hover:!border-gray-300"
            >
              {t('common.cancel')}
            </Button>
            {!isModifyPricingModelOpen.isEdit && (
              <Button
                className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
                htmlType="submit"
                form="addCustomPrice"
              >
                {t('common.add')}
              </Button>
            )}
          </div>
        }
        width={450}
      >
        <div className="w-full md:min-w-[400px]">
          <Form
            name="addCustomPrice"
            className="custom-form"
            layout={'vertical'}
            form={customPricingForm}
            preserve={false}
            onFinish={addNewCustomPrice}
          >
            <div className="flex flex-col gap-2">
              <Form.Item
                name="appliedModifier"
                label={t('ordersPage.priceBreakDown.nameLabel')}
                validateFirst
                rules={[
                  {
                    required: true,
                    message: t('ordersPage.priceBreakDown.requiredName'),
                  },
                  {
                    whitespace: true,
                    message: t('ordersPage.priceBreakDown.requiredName'),
                  },
                  {
                    pattern: formErrorRegex.NoMultipleWhiteSpaces,
                    message: t('common.errors.noMultipleWhiteSpace'),
                  },
                ]}
              >
                <Input
                  placeholder={t('ordersPage.priceBreakDown.namePlaceholder')}
                  disabled={isModifyPricingModelOpen.isEdit}
                  maxLength={255}
                />
              </Form.Item>
              <Form.Item
                label={t('ordersPage.priceBreakDown.amountLabel')}
                rules={[
                  {
                    required: true,
                    message: t('ordersPage.priceBreakDown.requiredAmount'),
                  },
                  {
                    validator: amountValidator,
                  },
                ]}
                name="appliedCharges"
              >
                <InputNumber
                  inputMode="decimal"
                  pattern="[0-9]*[.,]?[0-9]*"
                  type="number"
                  disabled={isModifyPricingModelOpen.isEdit}
                  addonBefore={'$'}
                  name="Amount"
                  className="bulk-adjust-input w-full"
                  placeholder="00.00"
                  parser={numberParser}
                  step={0.01}
                />
              </Form.Item>
            </div>
          </Form>
        </div>
      </CustomModal>
      <header className="flex justify-between items-center pt-4">
        <h1 className="font-semibold text-xl">Tesla Next Day Charges</h1>
        <Button
          className=" h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
          icon={<EditPopupWhiteIcon />}
          onClick={() => {
            setIsModifyPricingModelOpen({ open: true, isEdit: false, customPriceId: undefined });
          }}
        >
          {t('ordersPage.priceBreakDown.modifierPricingButton')}
        </Button>
      </header>
      <main>
        <CustomAgGrid
          rowData={priceBreakdownList}
          columnDefs={colDefs}
          className="!h-[73vh] lg:!h-[75vh]"
          gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
          emptyState={{
            title: t('ordersPage.priceBreakDown.emptyState.title'),
            description: t('ordersPage.priceBreakDown.emptyState.description'),
          }}
        />
      </main>
    </div>
  );
};
export default OrdersPricesBreakdownComponent;
