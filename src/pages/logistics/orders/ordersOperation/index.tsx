import { TabsComponent } from '@/components/common/customTabs/CustomTabs';
import PageBreadCrumbsComponent from '@/components/specific/pageBreadCrumb/PageBreadCrumbComponent';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { breadCrumbsPathForPages } from '@/constant/BreadCrumbConstant';
import { useCallback, useEffect, useState } from 'react';
import { DefaultTabsForOrders } from './DefaultTabsForOrders';
import { ROUTES } from '@/constant/RoutesConstant';
import { IBreadCrumb } from '@/types/BreadCrumbTypes';
import { on } from '@/contexts/PulseContext';
import { useParams } from 'react-router-dom';
import { Tag } from 'antd';
import { OrderStatusEnums } from '@/types/enums/orderStatus';
import { useLanguage } from '@/hooks/useLanguage';

const OrdersInfoComponent = () => {
  const { id, tab } = useParams<{ id: string; tab: string }>();
  const { t } = useLanguage();
  const [breadcrumbPath, setBreadcrumbPath] = useState<IBreadCrumb[]>([]);
  const [childBreadCrumbForPath, setChildBreadCrumbForPath] = useState<IBreadCrumb>();

  on('breadCrumb:changed', (data) => {
    const childBreadcrumb = breadCrumbsPathForPages.Orders.find(
      (item) => item.name === data.breadCrumbs
    );
    setChildBreadCrumbForPath(childBreadcrumb);
  });
  const status = 'Deleted';
  const orderStatusToReturn = {
    InTransit: {
      status: OrderStatusEnums.IN_TRANSIT,
      className: 'border-[#FDB022] bg-[#FFFAEB] text-[#F79009]',
    },
    Delivered: {
      status: OrderStatusEnums.DELIVERED,
      className: 'border-[#12B76A] text-[#12B76A] bg-[#ECFDF3]',
    },
    Cancelled: {
      status: OrderStatusEnums.CANCELLED,
      className: 'text-[#F04438] border-[#F04438] bg-[#FEF3F2]',
    },
    Cancelled_billed: {
      status: OrderStatusEnums.CANCELLED_BILLED,
      className: 'text-[#F04438] border-[#F04438] bg-[#FEF3F2]',
    },
    Deleted: {
      status: OrderStatusEnums.DELETED,
      className: 'text-[#F04438] border-[#F04438] bg-[#FEF3F2]',
    },
    Draft: {
      status: OrderStatusEnums.DRAFT,
      className: 'text-[#20363F] border-[#CDD7DB] bg-[#FCFCFD]',
    },
    PendingApproval: {
      status: OrderStatusEnums.PENDING_APPROVAL,
      className: 'border-[#FDB022] bg-[#FFFAEB] text-[#F79009]',
    },
    Ready: {
      status: OrderStatusEnums.READY,
      className: 'border-[#242A6A] bg-[#E3E5F6] text-[#242A6A]',
    },
    Rejected: {
      status: OrderStatusEnums.REJECTED,
      className: 'text-[#F04438] border-[#F04438] bg-[#FEF3F2]',
    },
    OnHold: {
      status: OrderStatusEnums.ON_HOLD,
      className: 'border-[#FDB022] bg-[#FFFAEB] text-[#F79009]',
    },
    Submitted: {
      status: OrderStatusEnums.SUBMITTED,
      className: 'border-[#242A6A] bg-[#E3E5F6] text-[#242A6A]',
    },
    Refunded: {
      status: OrderStatusEnums.REFUNDED,
      className: 'border-[#12B76A] text-[#12B76A] bg-[#ECFDF3]',
    },
  };
  const loadCustomerInfo = useCallback(async () => {
    if (id) {
      if (tab) {
        if (childBreadCrumbForPath) {
          const newname: IBreadCrumb = {
            name: id,
            path: ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION.replace(':id', id).replace(
              ':tab',
              tab
            ),
          };
          if (childBreadCrumbForPath) {
            const updatedPath = [newname, childBreadCrumbForPath];
            setBreadcrumbPath(updatedPath);
          }
        }
      }
    }
  }, [id, tab, childBreadCrumbForPath]);
  useEffect(() => {
    if (id) {
      loadCustomerInfo();
    } else {
      setBreadcrumbPath([breadCrumbsPathForPages.Orders[0], breadCrumbsPathForPages.Orders[1]]);
    }
  }, [id, loadCustomerInfo, tab]);
  return (
    <div>
      <div className="flex flex-col gap-1 avoid-tab-position">
        <div>
          <PageHeadingComponent
            title={t('ordersPage.viewOrder')}
            isChildComponent
            children={
              <div className="flex items-center gap-2 pt-[30px]">
                <div className="flex items-center gap-2">
                  <span className="h-[10px] w-[10px] rounded-full bg-error-500  flex" />
                  <span className="font-[16px] text-[#D92D20]"> Due in 30 minutes</span>
                </div>
                <Tag
                  className={`${orderStatusToReturn[status].className ?? ' border-[#FDB022] bg-[#FFFAEB] text-[#FDB022]'} rounded-md h-[32px] w-[122px] flex align-middle items-center justify-center font-[14px]`}
                >
                  {orderStatusToReturn[status].status}
                </Tag>
              </div>
            }
          />
          <PageBreadCrumbsComponent path={breadcrumbPath} />
        </div>
        <TabsComponent
          editableRoute={ROUTES.LOGISTIC.LOGISTICS_ORDERS_OPERATION}
          tabs={DefaultTabsForOrders()}
        />
      </div>
    </div>
  );
};

export default OrdersInfoComponent;
