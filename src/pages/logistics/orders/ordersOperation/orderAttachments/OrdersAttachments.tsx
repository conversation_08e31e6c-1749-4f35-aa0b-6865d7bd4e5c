import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import CustomModal from '@/components/common/modal/CustomModal';
import { GridIdConstant } from '@/constant/GridIdConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { IndexedDBStore } from '@/lib/indexedDB';
import { StoreName } from '@/lib/indexedDB/databaseConfig';
import { IColDef } from '@/types/AgGridTypes';
import { ICellRendererParams } from 'ag-grid-community';
import { Button, Form, Image, Select } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { DeleteIcon } from '@/assets';
import Icon from '@ant-design/icons';
import { ImageFormateIcon } from '@/assets/icons/imageFormateIcon';
import { IPackageModelState } from '../orderPackages/orderPackages.types';
import { UploadIcon } from '@/assets/icons/uploadIcon';
import { IAttachments, IImagePreviewState } from './orderAttachments.types';
import { PdfIcon } from '@/assets/icons/pdfIcon';
import { DownloadIcon } from '@/assets/icons/downloadIcon';
import CustomUpload from '@/components/common/customUpload/CustomUpload';
import { useWatch } from 'antd/es/form/Form';
import { customAlert } from '@/components/common/customAlert/CustomAlert';

const attachmentsStore = new IndexedDBStore<IAttachments>(StoreName.orderAttachments);

const OrdersAttachmentsComponent = () => {
  const [priceBreakdownList, setPriceBreakdownList] = useState<IAttachments[]>();
  const [isModelOpen, setIsModelOpen] = useState<IPackageModelState>({
    isOpen: false,
    isEdit: false,
    packageId: undefined,
  });
  const { t } = useLanguage();

  const uploadTypeOptions = useMemo(() => {
    return [
      {
        value: 'Collection signature',
        label: t('ordersPage.attachmentsTab.uploadTypeOptions.collectionSignature'),
      },
      {
        value: 'Delivery signature',
        label: t('ordersPage.attachmentsTab.uploadTypeOptions.deliverySignature'),
      },
      {
        value: 'File attachment',
        label: t('ordersPage.attachmentsTab.uploadTypeOptions.fileAttachment'),
      },
    ];
  }, [t]);

  const [packagesForm] = Form.useForm();
  const [visible, setVisible] = useState<IImagePreviewState>({
    isPreviewVisible: false,
    src: undefined,
  });

  const getPackagesList = async () => {
    try {
      const response = await attachmentsStore.getAll();
      setPriceBreakdownList(response);
    } catch (error) {
      // console.log({ error });
    }
  };

  const uploadTypeWatcher = useWatch('type', packagesForm);

  useEffect(() => {
    getPackagesList();
  }, []);

  const onFinish = async (values: any) => {
    try {
      await attachmentsStore.put({
        ...values,
        id: attachmentsStore.generateKey(),
        dateAdded: new Date(),
        file: values?.file?.file?.name,
      });
      await getPackagesList();
      setIsModelOpen({ isOpen: false, isEdit: false, packageId: undefined });
    } catch (error) {
      // console.log(error);
    }
  };

  const deleteAttachmentConfirmation = useCallback(
    (cellData: IAttachments) => {
      if (cellData?.id) {
        customAlert.error({
          title: `${t('zonePage.confirmDeleteZone')}`.replace('this', cellData.file),
          message: t('ordersPage.attachmentsTab.deleteConfirmationMessage'),
          secondButtonFunction: () => customAlert.destroy(),
          firstButtonFunction: async () => {
            await attachmentsStore.delete(cellData.id);
            await getPackagesList();
            customAlert.destroy();
          },
          firstButtonTitle: `${t('common.delete')}`,
          secondButtonTitle: `${t('common.cancel')}`,
        });
      }
    },
    [t]
  );

  const colDefs: IColDef[] = [
    {
      field: `file`,
      headerName: t('ordersPage.attachmentsTab.attachmentFile'),
      type: 'text',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 130,
      flex: 1,
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={() =>
              params.data.file.includes('.png') &&
              setVisible({ isPreviewVisible: true, src: params.data.image })
            }
          >
            <Icon component={params.data.file.includes('.png') ? ImageFormateIcon : PdfIcon} />
            <span className={`underline  cell-values`}>{params.value}</span>
          </div>
        );
      },
    },
    {
      field: `type`,
      headerName: t('ordersPage.attachmentsTab.type'),
      type: 'text',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 130,
      flex: 1,
    },
    {
      field: `dateAdded`,
      headerName: t('ordersPage.attachmentsTab.dateAdded'),
      type: 'date',
      visible: true,
      sortable: true,
      unSortIcon: true,
      minWidth: 130,
      flex: 1,
    },
    {
      headerName: t('vehiclePage.colDefs.action'),
      pinned: 'right',
      maxWidth: 70,
      minWidth: 70,
      sortable: false,
      resizable: false,
      visible: true,
      cellRenderer: (params: ICellRendererParams<IAttachments>) => {
        return (
          <div className="flex gap-2 h-full justify-center items-center">
            <Icon
              component={DownloadIcon}
              className="cursor-pointer"
              alt="download"
              value={'download'}
              onClick={() => {}}
            />
            <Icon
              component={DeleteIcon}
              className="cursor-pointer"
              alt="delete"
              value={'delete'}
              onClick={() => deleteAttachmentConfirmation(params.data as IAttachments)}
            />
          </div>
        );
      },
    },
  ];

  return (
    <div className="h-full">
      <CustomModal
        modalTitle={t('ordersPage.attachmentsTab.uploadAttachment')}
        modalDescription={''}
        open={isModelOpen.isOpen}
        destroyOnClose
        maskClosable={false}
        onCancel={() => setIsModelOpen({ isOpen: false, isEdit: false, packageId: undefined })}
        className="!h-[80%] flex items-center"
        footer={
          <div className="flex gap-2 w-full justify-end">
            <Button
              onClick={() =>
                setIsModelOpen({
                  isOpen: false,
                  isEdit: false,
                  packageId: undefined,
                })
              }
              className="hover:!text-black hover:!border-gray-300"
            >
              {t('common.cancel')}
            </Button>
            {
              <Button
                className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
                htmlType="submit"
                form="attachmentsForm"
                onClick={() => {
                  packagesForm.getFieldError('image');
                }}
              >
                {isModelOpen.isEdit ? t('common.update') : t('common.add')}
              </Button>
            }
          </div>
        }
      >
        <Form
          name="attachmentsForm"
          className="custom-form"
          layout={'vertical'}
          form={packagesForm}
          preserve={false}
          onFinish={onFinish}
        >
          <div className="grid gap-x-6 gap-y-3.5 grid-cols-1 sm:w-[500px]">
            <Form.Item
              label={t('ordersPage.attachmentsTab.uploadType')}
              validateFirst
              name="type"
              rules={[
                {
                  required: true,
                  message: t('ordersPage.attachmentsTab.requiredUploadType'),
                },
              ]}
            >
              <Select
                options={uploadTypeOptions}
                placeholder={t('ordersPage.attachmentsTab.selectPlaceholder')}
                prefixCls="custom-select"
                onSelect={() => packagesForm.setFieldValue('file', undefined)}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
            <Form.Item
              name={'file'}
              rules={[
                {
                  required: true,
                  message: t('ordersPage.attachmentsTab.requiredAttachmentOrFile'),
                },
              ]}
              validateTrigger={['onChange', 'beforeUpload']}
            >
              <CustomUpload
                name="file"
                label={t('ordersPage.attachmentsTab.uploadLabel')}
                form={packagesForm}
                uploadComponentProps={{
                  accept: uploadTypeWatcher === 'File attachment' ? '.csv,.pdf,image/*' : 'image/*',
                }}
              />
            </Form.Item>
          </div>
        </Form>
      </CustomModal>
      <div className="pr-4">
        <Image
          width={200}
          style={{ display: 'none' }}
          src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png?x-oss-process=image/blur,r_50,s_50/quality,q_1/resize,m_mfit,h_200,w_200"
          preview={{
            visible: visible.isPreviewVisible,
            src:
              // visible.src ||
              'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
            onVisibleChange: (value) => {
              setVisible({ ...visible, isPreviewVisible: value });
            },
          }}
        />
        <header className="flex justify-end items-center py-4">
          <Button
            className=" h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
            icon={<UploadIcon />}
            onClick={() => {
              setIsModelOpen({ isOpen: true, isEdit: false, packageId: undefined });
            }}
          >
            {t('ordersPage.attachmentsTab.uploadFiles')}
          </Button>
        </header>
        <main>
          <CustomAgGrid
            rowData={priceBreakdownList}
            columnDefs={colDefs}
            className="!h-[74vh] lg:!h-[75vh]"
            gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
          />
        </main>
      </div>
    </div>
  );
};
export default OrdersAttachmentsComponent;
