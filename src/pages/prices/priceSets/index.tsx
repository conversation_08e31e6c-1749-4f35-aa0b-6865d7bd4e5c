import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  AssignToOutlined,
  DeleteIcon,
  deleteSvg,
  DuplicateCustomerIcon,
  EyeIcon,
  HistoryIcon,
  PlusButtonIcon,
} from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import Icon from '@ant-design/icons';
import { GridNames } from '@/types/AppEvents';
import { useLanguage } from '@/hooks/useLanguage';
import { IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Button, Divider } from 'antd';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { IContextMenuItems } from '@/types/ContextMenuTypes';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { getPaginationData } from '@/lib/helper';
import {
  advanceFilterObjectMapper,
  highlightText,
  maskQuickFilterData,
} from '@/lib/SearchFilterTypeManage';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { ROUTES } from '@/constant/RoutesConstant';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { dateFormatter } from '@/lib/helper/dateHelper';
import { priceSetHook } from '@/api/priceSet/usePriceSet';
import { IPriceSet } from '@/api/priceSet/priceSet.types';
import { defaultPagination } from '@/constant/generalConstant';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';
import { IAssignedFilters } from '@/pages/logistics/orders/orders.types';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import AddPriceSetIcon from '@/assets/icons/addPriceSetIcon';

const PriceSetComponent = () => {
  const [searchText, setSearchText] = useState('');
  const [priceSet, setPriceSet] = useState<IPriceSet[]>();
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);

  const [cellData, setCellData] = useState<IPriceSet>({} as IPriceSet);
  const {
    data: priceSetList,
    refetch: refetchPriceSets,
    isFetching,
    isLoading,
  } = priceSetHook.useList(filterParams);

  const notificationManager = useNotificationManager();

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.priceSets.sortable.includes(field);
  }, []);

  const { t } = useLanguage();
  const { navigate } = useNavigationContext();
  useEffect(() => {
    if (priceSetList && priceSetList?.data) {
      setPriceSet(priceSetList?.data);
    }
  }, [priceSetList]);

  const deletePriceSetMutation = priceSetHook.useDelete({
    onSuccess: async () => {
      await refetchPriceSets();
      notificationManager.success({
        message: t('common.success'),
        description: t('priceSetPage.notification.successDelete'),
      });
      customAlert.destroy();
    },
  });

  const deletePriceSetConfirmation = useCallback(
    (id: string) =>
      customAlert.error({
        title: t('priceSetPage.alert.deleteConfirmation'),
        message: t('priceSetPage.alert.deleteConfirmationMessage'),
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
        secondButtonFunction: () => {
          customAlert.destroy();
        },
        firstButtonFunction: async () => {
          customAlert.destroy();
          await deletePriceSetMutation.mutateAsync(id);
        },
      }),
    [deletePriceSetMutation, t]
  );

  const priceSetColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'name',
        headerName: t('priceSetPage.colDefs.name'),
        visible: true,
        type: 'string',
        minWidth: 170,
        flex: 1,
        sortable: isColumnSortable('name'),
        unSortIcon: isColumnSortable('name'),
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'internalName',
        headerName: t('priceSetPage.colDefs.service'),
        sortable: isColumnSortable('internalName'),
        unSortIcon: isColumnSortable('internalName'),
        minWidth: 170,
        visible: true,
        type: 'string',
        flex: 1,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'action',
        headerName: `${t('dashboard.customer.columns.action')}`,
        pinned: 'right',
        width: 110,
        sortable: false,
        resizable: false,
        visible: true,
        cellRenderer: (params: { data: IPriceSet; value: string }) => (
          <div className="flex gap-2 h-full items-center">
            <CustomTooltip
              content={
                <div className="text-[#20363f] flex flex-col text-xs font-medium gap-1 w-full">
                  {t('common.added')}:{' '}
                  <span className="block w-fit text-sm font-semibold">
                    {dateFormatter(params.data.createdAt)}
                  </span>
                  <hr className="border-[#0000001c]" />
                  {t('common.modified')}:{' '}
                  <span className="block w-fit text-sm font-semibold">
                    {dateFormatter(params.data.updatedAt)}
                  </span>
                  <hr className="border-[#0000001c]" />
                  {t('common.lastUpdatedBy')}:
                  <span className="block w-fit text-sm font-semibold">
                    {params.data.updatedBy || t('common.notUpdatedYet')}
                  </span>
                </div>
              }
              placement="leftTop"
            >
              <div>
                <Icon component={HistoryIcon} className="cursor-pointer mt-2" alt="history" />
              </div>
            </CustomTooltip>

            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view"
              value="view"
              onClick={() =>
                navigate(
                  ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':id', params.data.id).replace(
                    ':tab',
                    'general'
                  )
                )
              }
            />

            <Icon
              component={DeleteIcon}
              onClick={() => deletePriceSetConfirmation(params.data?.id)}
              className="cursor-pointer"
              alt={t('common.delete')}
            />
          </div>
        ),
      },
    ];
  }, [deletePriceSetConfirmation, isColumnSortable, navigate, searchText, t]);

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);

    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = () => {
    setSearchText('');
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearAllFunctionRef.current.handleClearAll();
  };

  const duplicatePriceSetMutation = priceSetHook.useDuplicate({
    onSuccess: async () => {
      refetchPriceSets();
    },
  });
  const duplicatePriceSetConfirmation = useCallback(
    () =>
      customAlert.warning({
        title: t('priceSetPage.alert.duplicateConfirmation'),
        message: t('customerAddressPage.notifications.confirmDuplicateMessage'),
        firstButtonTitle: t('common.cancel'),
        secondButtonTitle: t('common.duplicate'),
        secondButtonFunction: async () => {
          duplicatePriceSetMutation.mutate(cellData.id as string);

          customAlert.destroy();
        },
        firstButtonFunction: () => {
          customAlert.destroy();
        },
      }),
    [cellData.id, duplicatePriceSetMutation, t]
  );

  const customerContextMenuItems: IContextMenuItems[] = useMemo(() => {
    return [
      {
        label: t('priceSetPage.contextMenu.addPriceSet'),
        key: 'addNewPriceSet',
        icon: AddPriceSetIcon as React.ElementType,
        onClick: () => navigate(ROUTES.PRICES.PRICES_PRICE_SETS_ADD),
      },
      {
        label: t('priceSetPage.contextMenu.duplicatePriceSet'),
        icon: DuplicateCustomerIcon as React.ElementType,
        key: 'duplicatePriceSet',
        onClick: () => duplicatePriceSetConfirmation(),
      },
      {
        label: t('priceSetPage.contextMenu.assignUnassign'),
        icon: AssignToOutlined as React.ElementType,
        key: 'unAssign',
        onClick: () =>
          navigate(
            ROUTES.PRICES.PRICES_PRICE_SETS_EDIT.replace(':id', cellData.id as string).replace(
              ':tab',
              'customers'
            )
          ),
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} />) as unknown as React.ElementType,
        onClick: () => deletePriceSetConfirmation(cellData.id as string),
        key: 'deletePriceSet',
      },
    ];
  }, [cellData.id, deletePriceSetConfirmation, duplicatePriceSetConfirmation, navigate, t]);

  const paginationData = useMemo(() => getPaginationData(priceSetList), [priceSetList]);

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
          <div className="md:w-1/3 flex flex-col 3xsm:w-full">
            <PageHeadingComponent title={t('priceSetPage.header.title')} />
          </div>
          <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
            <div className="flex gap-3">
              <SearchFilterComponent
                searchedValues={() => {}}
                colDefs={priceSetColDefs}
                isSetQuickFilter={false}
                searchInputPlaceholder={t('priceSetPage.header.search')}
                onFilterApply={applyFilters}
                onSearch={searchHandler}
                setSelectedQuickFilterData={setSelectedQuickFilterData}
                supportedFields={filterableModules.priceSets.advanceFilter}
                clearAllFunctionRef={clearAllFunctionRef}
                setFilterParams={setFilterParams}
              />
            </div>
            <div className="pt-5">
              <Divider type="vertical" className="3xsm:hidden md:block h-[40px] !m-0" />
            </div>
            <div className="3xsm:pt-0 md:pt-5">
              <Button
                className="h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                icon={<PlusButtonIcon />}
                onClick={() => navigate(ROUTES.PRICES.PRICES_PRICE_SETS_ADD)}
              >
                {t('priceSetPage.header.addPriceSetBtn')}
              </Button>
            </div>
          </div>
        </div>
        <main className=" h-screen overflow-x-hidden overflow-y-auto bg-white">
          <ActiveFilters
            selectedQuickFilterData={selectedQuickFilterData}
            clearAllToDefault={clearAllToDefault}
            colDefs={priceSetColDefs}
          />
          <div className="mx-auto pr-6 pt-5 flex justify-center items-center">
            <CustomAgGrid
              rowData={priceSet}
              className={selectedQuickFilterData.length > 0 ? 'md:!h-[79vh]' : ''}
              columnDefs={priceSetColDefs}
              loading={isFetching || isLoading}
              isContextMenu
              contextMenuItem={customerContextMenuItems}
              onContextMenu={(params) => setCellData(params.data)}
              gridName={GridNames.priceSetsGrid}
              emptyState={{
                title:
                  searchText || selectedQuickFilterData.length > 0
                    ? t('common.noMatchesFound')
                    : t('priceSetPage.emptyState.title'),
                description:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('priceSetPage.emptyState.description'),
                link:
                  searchText || selectedQuickFilterData.length > 0
                    ? ''
                    : t('priceSetPage.emptyState.link'),
                onLinkAction: () => navigate(ROUTES.PRICES.PRICES_PRICE_SETS_ADD),
              }}
              paginationProps={{
                ...paginationData,
                onPaginationChange(page, pageLimit) {
                  setFilterParams((prev) => ({
                    ...prev,
                    pageNumber: page,
                    pageSize: pageLimit,
                  }));
                },
              }}
              onSortChanged={(params: IExtendedSortChangedEvent) =>
                setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
              }
            />
          </div>
        </main>
      </div>
    </div>
  );
};

export default PriceSetComponent;
