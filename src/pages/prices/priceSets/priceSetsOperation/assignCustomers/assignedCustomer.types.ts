import { IGetAssignedCustomers } from '@/api/priceSet/assignedCustomers/assignedCustomers.types';
import { IServiceSearchHighlight } from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { IColDef } from '@/types/AgGridTypes';

export interface ICustomerServiceGridProps {
  setIsEdit: React.Dispatch<React.SetStateAction<boolean>>;
  allCustomers: IGetAssignedCustomers[] | undefined;
  setSearchText: React.Dispatch<React.SetStateAction<IServiceSearchHighlight>>;
  colDefs: IColDef[];
  searchText: IServiceSearchHighlight;
  noSCustomersAvailableInSystem: boolean;
}
