import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { ROUTES } from '@/constant/RoutesConstant';
import { useLanguage } from '@/hooks/useLanguage';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Button, Divider, Typography } from 'antd';
import { emptyStateIcon2, infoCircleOutlined, PlusButtonIcon } from '@/assets';
import { searchData } from '@/lib/helper';
import CustomModal from '@/components/common/modal/CustomModal';
import {
  ICustomerServiceGridProps,
  ITransferPriceModifiersListing,
} from './assignPriceModifier.types';
import { GridIdConstant } from '@/constant/GridIdConstant';

const PriceModifiersGrid: React.FC<ICustomerServiceGridProps> = (props) => {
  const [modifiers, setModifiers] = useState<ITransferPriceModifiersListing[]>();
  const [modifiersFilter, setModifierFilter] = useState<ITransferPriceModifiersListing[]>([]);
  const [isHelpModalVisible, setIsHelpModalVisible] = useState(false);
  const { t } = useLanguage();

  const {
    setIsEdit,
    allModifiers,
    setSearchText,
    colDefs,
    searchText,
    noCustomersAvailableInSystem,
  } = props;

  useEffect(() => {
    if (allModifiers) {
      setModifiers(allModifiers || []);
      setModifierFilter(allModifiers || []);
    }
  }, [allModifiers]);

  const { Text } = Typography;

  const renderHelpModal = () => (
    <CustomModal
      modalTitle={t('priceSetPage.helpModalStrings.modalTitle')}
      modalDescription={t('priceSetPage.helpModalStrings.modalDescription')}
      open={isHelpModalVisible}
      onCancel={() => setIsHelpModalVisible(false)}
      footer={
        <div className="flex justify-end">
          <Button
            type="primary"
            className="border-[1px] h-[36px] rounded-[8px] bg-[#0876A4] text-white font-[500] hover:!bg-[#0876A4] hover:!text-white"
            onClick={() => setIsHelpModalVisible(false)}
            aria-label="Close Help Modal"
          >
            {t('priceSetPage.helpModalStrings.buttonText')}
          </Button>
        </div>
      }
      width={800}
    >
      <div className="space-y-5">
        {/* Section: Add Price Modifiers */}
        <div className="p-4 rounded-lg border border-primary-600 bg-[#F0FAFF] shadow-md">
          <div className="flex items-center mb-3">
            <Text strong className="text-lg text-gray-800">
              {t('priceSetPage.helpModalStrings.addPriceModifiersTitle')}
            </Text>
          </div>
          <ul className="list-disc pl-6 space-y-2 text-gray-700">
            <li>
              <Text>
                {t('priceSetPage.helpModalStrings.addPriceModifiersDescription')}{' '}
                <strong>"{t('priceSetPage.helpModalStrings.addPriceModifiersStrong')}"</strong>{' '}
                {t('priceSetPage.helpModalStrings.addPriceModifiersEnd')}
              </Text>
            </li>
          </ul>
        </div>

        {/* Section: Configure Default Behavior */}
        <div className="p-4 rounded-lg border border-primary-600 bg-[#F0FAFF] shadow-md">
          <div className="flex items-start mb-3 flex-col gap-[5px]">
            <Text strong className="text-lg text-gray-800">
              {t('priceSetPage.helpModalStrings.configureDefaultBehaviorTitle')}
            </Text>
            <Text>
              {t('priceSetPage.helpModalStrings.configureDefaultBehaviorDescription')}{' '}
              <strong>"{t('priceSetPage.helpModalStrings.configureDefaultBehaviorStrong')}"</strong>{' '}
              {t('priceSetPage.helpModalStrings.configureDefaultBehaviorEnd')}
            </Text>
          </div>
          <ul className="list-disc pl-6 space-y-2 text-gray-700">
            <li>
              <Text>
                <strong>{t('priceSetPage.helpModalStrings.noneLabel')}</strong>{' '}
                {t('priceSetPage.helpModalStrings.noneDescription')}
              </Text>
            </li>
            <li>
              <Text>
                <strong>{t('priceSetPage.helpModalStrings.selectedLabel')}</strong>{' '}
                {t('priceSetPage.helpModalStrings.selectedDescription')}
              </Text>
            </li>
            <li>
              <Text>
                <strong>{t('priceSetPage.helpModalStrings.requiredLabel')}</strong>{' '}
                {t('priceSetPage.helpModalStrings.requiredDescription')}
              </Text>
            </li>
          </ul>
        </div>

        {/* Section: Example */}
        <div className="p-4 rounded-lg border border-primary-600 bg-[#F0FAFF] shadow-md">
          <div className="flex items-start mb-3 flex-col gap-[5px]">
            <Text strong className="text-lg text-gray-800">
              {t('priceSetPage.helpModalStrings.exampleTitle')}
            </Text>
            <Text>{t('priceSetPage.helpModalStrings.exampleDescription')}</Text>
          </div>
          <ul className="list-disc pl-6 space-y-2 text-gray-700">
            <li>
              <Text>{t('priceSetPage.helpModalStrings.exampleStep1')}</Text>
            </li>
            <li>{t('priceSetPage.helpModalStrings.exampleStep2')}</li>
          </ul>
        </div>
      </div>
    </CustomModal>
  );

  const navigate = useNavigate();

  const searchHandler = useCallback(
    (value: string) => {
      const results = searchData(
        modifiersFilter,
        {
          query: value,
        },
        colDefs,
        ['configuration']
      );
      setSearchText((prev) => ({
        ...prev,
        searchTextForAssigned: value,
      }));
      setModifiers(results);
    },
    [colDefs, modifiersFilter, setSearchText]
  );

  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);

  const displayNav =
    (modifiers && modifiers?.length > 0) ||
    (searchText.searchTextForAssigned && modifiers?.length === 0);

  return (
    <div className="flex h-full">
      {renderHelpModal()}
      <div className="flex-1 gap-3 flex flex-col overflow-hidden bg-white">
        {displayNav && (
          <div className="flex items-end 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[24px] 3xsm:w-full 3xsm:gap-3">
            <div className="flex gap-3 justify-end">
              <Button
                onClick={() => setIsHelpModalVisible(true)}
                className="h-[40px] p-2 mt-5 hover:!text-black hover:!border-gray-300"
              >
                <img className="w-[22px] h-[22px]" src={infoCircleOutlined} />
              </Button>
              <SearchFilterComponent
                onSearch={triggerSearch}
                colDefs={colDefs}
                onFilterApply={() => {}}
                advanceFilter={false}
                searchInputPlaceholder={t('priceSetPage.priceMod.searchPlaceholder')}
              />
            </div>
            <Divider type="vertical" className="h-[40px] m-0" />
            <Button
              className="border-[1px] h-[40px] rounded-[8px] bg-[#0876A4] text-white font-[500] hover:!bg-[#0876A4] hover:!text-white"
              icon={<PlusButtonIcon />}
              type="primary"
              disabled={noCustomersAvailableInSystem}
              onClick={() => setIsEdit(true)}
            >
              {t('priceSetPage.priceMod.assignPriceModBtn')}
            </Button>
          </div>
        )}
        <main className="h-full overflow-x-hidden overflow-y-auto bg-white pr-6">
          <CustomAgGrid
            rowData={modifiers}
            columnDefs={colDefs}
            gridId={GridIdConstant.GRID_WRAPPER_FOR_CHILDREN}
            rowHeight={50}
            pagination
            className="max-h-[75vh] !h-[75vh] lg:!h-[76vh]"
            emptyState={{
              title: searchText.searchTextForAssigned
                ? t('common.noMatchesFound')
                : noCustomersAvailableInSystem
                  ? t('priceSetPage.priceMod.notFound')
                  : t('priceSetPage.priceMod.assignToSet'),

              description: searchText.searchTextForAssigned
                ? ''
                : noCustomersAvailableInSystem
                  ? t('priceSetPage.priceMod.getStarted')
                  : t('priceSetPage.priceMod.toAssignMod'),
              link: searchText.searchTextForAssigned
                ? ''
                : noCustomersAvailableInSystem
                  ? t('priceSetPage.priceMod.addSet')
                  : t('priceSetPage.priceMod.clickHear'),
              onLinkAction: () =>
                !noCustomersAvailableInSystem
                  ? setIsEdit(true)
                  : navigate(ROUTES.PRICES.PRICES_PRICE_MODIFIER_ADD),
              image:
                !searchText.searchTextForAssigned || !noCustomersAvailableInSystem
                  ? emptyStateIcon2
                  : undefined,
            }}
          />
        </main>
      </div>
    </div>
  );
};
export default PriceModifiersGrid;
