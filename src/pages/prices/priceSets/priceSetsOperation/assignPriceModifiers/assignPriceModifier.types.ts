import { ModifiersConfig } from '@/api/priceSet/assignedModifiers/assignedModifiers.types';
import { IServiceSearchHighlight } from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { IColDef } from '@/types/AgGridTypes';

export interface ITransferPriceModifiersListing {
  id?: string;
  transferId: string;
  name: string;
  memberId: string;
  isGroup: boolean;
  configuration: ModifiersConfig;
}

export interface ICustomerServiceGridProps {
  setIsEdit: React.Dispatch<React.SetStateAction<boolean>>;
  allModifiers: ITransferPriceModifiersListing[] | undefined;
  setSearchText: React.Dispatch<React.SetStateAction<IServiceSearchHighlight>>;
  colDefs: IColDef[];
  searchText: IServiceSearchHighlight;
  noCustomersAvailableInSystem: boolean;
}
