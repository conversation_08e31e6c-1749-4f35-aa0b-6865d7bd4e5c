import { useCallback, useEffect, useMemo, useState } from 'react';
import PriceModifiersGrid from './PriceModifiersGrid';
import { IServiceSearchHighlight } from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { useParams } from 'react-router-dom';
import TransferGrid from '@/components/common/transferGrid/TransferGrid';
import { priceModifierHook } from '@/api/priceModifier/usePriceModifier';
import {
  assignedModifierHook,
  assignedModifierService,
} from '@/api/priceSet/assignedModifiers/useAssignedModifier';
import { IPriceModifier, IPriceModifiersListing } from '@/api/priceModifier/priceModifier.types';
import notificationManagerInstance, { useNotificationManager } from '@/hooks/useNotificationManger';
import { useLanguage } from '@/hooks/useLanguage';
import { IColDef } from '@/types/AgGridTypes';
import { ModifierGroupGridIcon, ModifierGridIcon } from '@/assets';
import { highlightText } from '@/lib/SearchFilterTypeManage';
import { Radio, RadioChangeEvent } from 'antd';
import { ICellRendererParams } from 'ag-grid-community';
import { IChangeConfigDTO } from '@/api/priceSet/assignedModifiers/assignedModifiers.types';
import { ITransferPriceModifiersListing } from './assignPriceModifier.types';

const PriceModifierComponent = () => {
  const [isEdit, setIsEdit] = useState(false);

  const { id: priceSetId } = useParams();
  const { t } = useLanguage();

  const { data: priceModifiersList } = priceModifierHook.useEntities('combined');
  const notificationManager = useNotificationManager();

  const { data: assignedModifiers, refetch: refetchAssignedModifiers } =
    assignedModifierHook.useEntities(`${priceSetId}/modifiers`);

  const [searchText, setSearchText] = useState<IServiceSearchHighlight>({
    searchTextForAvailable: '',
    searchTextForSelected: '',
    searchTextForAssigned: '',
  });

  const changeModifierConfiguration = useCallback(
    async (modifierId: string, event: RadioChangeEvent) => {
      try {
        await assignedModifierService.update<IChangeConfigDTO>(`modifiers/${modifierId}`, {
          configuration: event.target.value,
        });
        refetchAssignedModifiers();
      } catch (error) {
        notificationManager.error({
          message: t('common.error'),
          description: t('priceSetPage.errorWhileChangingModifierConfig'),
        });
      }
    },
    [notificationManager, refetchAssignedModifiers, t]
  );

  const configOptions = useMemo(() => {
    return [
      {
        label: t('priceSetPage.options.none'),
        value: 'none',
      },
      {
        label: t('priceSetPage.options.selected'),
        value: 'selected',
      },
      {
        label: t('priceSetPage.options.required'),
        value: 'required',
      },
    ];
  }, [t]);

  const unAssignedCustomers = useMemo(() => {
    return priceModifiersList?.data
      .filter(
        (service) =>
          !assignedModifiers?.data.some((assignedService) => {
            return assignedService.memberId === service.id;
          })
      )
      .map((m) => ({ ...m, transferId: m.id }));
  }, [assignedModifiers, priceModifiersList]);

  const assignedModifiersMasked = useMemo(() => {
    return assignedModifiers?.data.map((service) => ({
      ...service,
      transferId: service.memberId,
    }));
  }, [assignedModifiers]);

  const onSave = async (selectedData: ITransferPriceModifiersListing[]) => {
    const selectedModifiers = selectedData.map((data) => ({
      id: data.transferId,
      isGroup: data.isGroup as boolean,
    }));
    try {
      await assignedModifierService.update(`${priceSetId}/modifiers`, {
        members: selectedModifiers,
      });
      await refetchAssignedModifiers();
    } catch (error) {
      notificationManagerInstance.error({
        message: t('common.error'),
        description: t('systemErrors.whileAssigningServices'),
      });
    }
  };

  const customerServiceColDefs: IColDef[] = useMemo(() => {
    return [
      {
        field: 'name',
        headerName: t('priceSetPage.colDefs.name'),
        unSortIcon: true,
        tooltipField: 'name',
        type: 'string',
        visible: true,
        flex: 1,
        maxWidth: 900,
        cellRenderer: (params: ICellRendererParams<IPriceModifier>) => {
          if (params.context) {
            const isAvailableGrid = params.context.name === 'available';
            const relevantSearchText = isAvailableGrid
              ? searchText.searchTextForAvailable
              : searchText.searchTextForSelected;

            const icon = params.data?.isGroup ? <ModifierGroupGridIcon /> : <ModifierGridIcon />;
            return (
              <div className="flex gap-2 items-center">
                <div>{icon}</div>
                {(relevantSearchText && highlightText(params.value, relevantSearchText)) || (
                  <div>{params.data?.name}</div>
                )}
              </div>
            );
          }
          return searchText.searchTextForAssigned
            ? highlightText(params.value, searchText.searchTextForAssigned)
            : params.value;
        },
      },
    ];
  }, [
    searchText.searchTextForAssigned,
    searchText.searchTextForAvailable,
    searchText.searchTextForSelected,
    t,
  ]);

  const customerServiceColDefsWithConfig: IColDef[] = useMemo(() => {
    return [
      {
        field: 'name',
        headerName: t('priceSetPage.colDefs.name'),
        key: 'type',
        unSortIcon: true,
        type: 'string',
        flex: 1,
        visible: true,
        cellRenderer: (params: ICellRendererParams<IPriceModifier>) => {
          const icon = params?.data?.isGroup ? <ModifierGroupGridIcon /> : <ModifierGridIcon />;
          return (
            <div className="h-full flex gap-1 items-center">
              {icon}
              {searchText.searchTextForAssigned
                ? highlightText(params?.data?.name as string, searchText.searchTextForAssigned)
                : params.data?.name}
            </div>
          );
        },
      },
      {
        field: 'configuration',
        headerName: t('priceSetPage.colDefs.configByDefault'),
        visible: true,
        flex: 1,
        maxWidth: 300,
        minWidth: 300,
        sortable: false,
        type: 'string',
        cellRenderer: (params: ICellRendererParams) => {
          return (
            <div className="p-2">
              <Radio.Group
                options={configOptions}
                optionType="button"
                buttonStyle="solid"
                defaultValue={params.value}
                onChange={(event) => changeModifierConfiguration(params.data.id, event)}
              />
            </div>
          );
        },
      },
    ];
  }, [changeModifierConfiguration, configOptions, searchText.searchTextForAssigned, t]);

  useEffect(() => {
    setSearchText({
      searchTextForAssigned: '',
      searchTextForAvailable: '',
      searchTextForSelected: '',
    });
  }, [isEdit]);

  return (
    <>
      {isEdit ? (
        <TransferGrid<IPriceModifiersListing>
          setIsEdit={setIsEdit}
          colDefs={customerServiceColDefs}
          initialRowData={unAssignedCustomers || []}
          setSearchText={setSearchText}
          assignedServices={assignedModifiersMasked || []}
          searchText={searchText}
          onSave={onSave}
          hideBackNavigation
          mainHeaderTitle={t('priceSetPage.priceMod.assignModifierWithBack')}
          availableGridHeader={t('priceSetPage.priceMod.unassignMods')}
          availableGridSearchPlaceholder={t('priceSetPage.priceMod.searchPlaceholder')}
          selectedGridHeader={t('priceSetPage.priceMod.assignMods')}
          selectedGridSearchPlaceholder={t('priceSetPage.priceMod.searchPlaceholder')}
          availableGridEmptyStateTitle={t('priceSetPage.priceMod.notAvailable')}
          availableGridEmptyStateDescription={t('priceSetPage.priceMod.noModsFoundToAssigned')}
          selectedGridEmptyState={t('priceSetPage.priceMod.noModsAssigned')}
        />
      ) : (
        <PriceModifiersGrid
          setIsEdit={setIsEdit}
          colDefs={customerServiceColDefsWithConfig}
          allModifiers={assignedModifiersMasked}
          noCustomersAvailableInSystem={priceModifiersList?.data.length === 0}
          searchText={searchText}
          setSearchText={setSearchText}
        />
      )}
    </>
  );
};
export default PriceModifierComponent;
