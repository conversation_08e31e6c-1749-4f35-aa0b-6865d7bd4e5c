import SelectDownArrow from '@/assets/icons/selectDownArrow';
import { useLanguage } from '@/hooks/useLanguage';
import { IZoneLookupRow } from '@/pages/location/zone/list/zone.type';
import { Button, Select } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { logger } from '@/lib/logger/logger';
import { EmptyStatePage } from '@/components/common/emptyState/EmptyStatePage';
import Spinner from '@/components/common/spinner/Spinner';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { priceSetHook, priceSetService } from '@/api/priceSet/usePriceSet';
import { BasePriceByZoneDTO, ZoneTableValues } from '@/api/priceSet/priceSet.types';
import { zoneTableHook, zoneTableService } from '@/api/zoneTable/useZoneTable';
import { zoneHooks } from '@/api/zones/useZones';

const BasePriceByZone: React.FC = () => {
  const {
    data: zoneTables,
    isFetching,
    isLoading,
  } = zoneTableHook.useList({ pageNumber: 1, pageSize: 100 });

  const { data: zones } = zoneHooks.useList({ pageNumber: 1, pageSize: 100 });
  const { t } = useLanguage();
  const gridRef = useRef<HTMLDivElement>(null);
  const notificationManager = useNotificationManager();
  const { id: priceSetId } = useParams();

  // Active Cell and Interaction State
  const [activeCell, setActiveCell] = useState<{ row: number; col: number } | null>(null);
  const [highlight, setHighlight] = useState(t('zonePage.zoneLookUp.hoverOverACell'));
  const [cellValues, setCellValues] = useState<IZoneLookupRow[]>([]);
  const [undoStack, setUndoStack] = useState<IZoneLookupRow[][]>([]);
  const [redoStack, setRedoStack] = useState<IZoneLookupRow[][]>([]);

  const [selectedValue, setSelectedValue] = useState<string | undefined>(undefined);

  const { data: currentAssignedZoneTable, refetch: refetchCurrentZoneTable } =
    priceSetHook.useEntity<BasePriceByZoneDTO>(`${priceSetId}/zone`);

  const zoneTableValuesReversMapper = useCallback(
    (zoneTableValues: IZoneLookupRow[]): ZoneTableValues[] => {
      return (
        zoneTableValues?.map((table) => ({
          originZoneId: table.fromId,
          destinationZoneId: table.toId,
          value: table.value,
        })) || []
      );
    },
    []
  );

  const zoneTableValueMapper = useCallback(
    (zoneTableValues: BasePriceByZoneDTO): IZoneLookupRow[] => {
      return (
        zoneTableValues?.zoneTableValues.map((table) => ({
          fromId: table.originZoneId,
          toId: table.destinationZoneId,
          value: table.value,
        })) || []
      );
    },
    []
  );

  useEffect(() => {
    if (currentAssignedZoneTable && currentAssignedZoneTable.zoneTableValues.length > 0) {
      const mappedZoneTableValues: IZoneLookupRow[] =
        zoneTableValueMapper(currentAssignedZoneTable);
      setCellValues(mappedZoneTableValues);
    }
  }, [currentAssignedZoneTable, zoneTableValueMapper]);

  const navigate = useNavigate();

  const isChanged = useMemo(
    () =>
      JSON.stringify(cellValues) !==
      (currentAssignedZoneTable && JSON.stringify(zoneTableValueMapper(currentAssignedZoneTable))),
    [cellValues, currentAssignedZoneTable, zoneTableValueMapper]
  );

  const onSubmit = async () => {
    try {
      if (currentAssignedZoneTable?.zoneTableValues && isChanged) {
        customAlert.warning({
          title: t('priceSetPage.alert.overWriteZoneTableOnSave'),
          message: t('priceSetPage.alert.updateZoneTableConfirmationMsg'),
          firstButtonTitle: t('common.doItAnyWay'),
          secondButtonTitle: t('common.cancel'),
          firstButtonFunction: async () => {
            await priceSetService.update(`${priceSetId}/zone`, {
              name: 'untitled', // TODO: remove this name property after discussion
              zoneTableValues: zoneTableValuesReversMapper(cellValues),
            });
            await refetchCurrentZoneTable();
            notificationManager.success({
              message: `${t('common.success')}`,
              description: t('priceSetPage.notification.successAssignedZoneTable'),
            });
            customAlert.destroy();
          },
          secondButtonFunction: () => {
            customAlert.destroy();
          },
        });
        return;
      }
      await priceSetService.update(`${priceSetId}/zone`, {
        name: 'untitled', // TODO: remove this name properly after discussion
        zoneTableValues: zoneTableValuesReversMapper(cellValues),
      });
      await refetchCurrentZoneTable();

      notificationManager.success({
        message: `${t('common.success')}`,
        description: t('priceSetPage.notification.successAssignedZoneTable'),
      });
      customAlert.destroy();
    } catch (error) {
      notificationManager.error({
        message: `${t('common.error')}`,
        description: t('priceSetPage.notification.errorAssignZoneTable'),
      });
      logger.error('ERROR', error as Error);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!activeCell) return;

    const { row, col } = activeCell;
    let newRow = row;
    let newCol = col;

    switch (event.key) {
      case 'ArrowUp':
        newRow = Math.max(row - 1, 0);
        break;
      case 'ArrowDown':
        newRow = Math.min(row + 1, zones ? zones.data.length - 1 : 0);
        break;
      case 'ArrowLeft':
        newCol = Math.max(col - 1, 0);
        break;
      case 'ArrowRight':
        newCol = Math.min(col + 1, zones ? zones?.data.length - 1 : 0);
        break;
      case 'Escape':
        setActiveCell(null);
        setHighlight(t('zonePage.zoneLookUp.hoverOverACell'));
        return;
      default:
        return;
    }

    setActiveCell({ row: newRow, col: newCol });
    setHighlight(
      `From: ${zones && zones?.data[newRow]?.name} → To: ${zones && zones?.data[newCol]?.name}`
    );
  };

  const handleCellClick = (rowIndex: number, colIndex: number): void => {
    setActiveCell({ row: rowIndex, col: colIndex });
  };

  const captureState = useCallback(() => {
    setUndoStack((prev) => [...prev, cellValues]);
    setRedoStack([]); // Clear redo stack when a new action is performed
  }, [cellValues]);

  const handleUndo = useCallback(() => {
    if (undoStack.length === 0) return;
    const previousState = undoStack[undoStack.length - 1];
    setUndoStack((prev) => prev.slice(0, -1));
    setRedoStack((prev) => [...prev, cellValues]);
    setCellValues(previousState);
  }, [cellValues, undoStack]);

  const handleRedo = useCallback(() => {
    if (redoStack.length === 0) return;
    const nextState = redoStack[redoStack.length - 1];
    setRedoStack((prev) => prev.slice(0, -1));
    setUndoStack((prev) => [...prev, cellValues]);
    setCellValues(nextState);
  }, [cellValues, redoStack]);

  useEffect(() => {
    const handleShortcuts = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'z') {
        event.preventDefault();
        handleUndo();
      }
      if (event.ctrlKey && event.key === 'y') {
        event.preventDefault();
        handleRedo();
      }
    };

    document.addEventListener('keydown', handleShortcuts);
    return () => document.removeEventListener('keydown', handleShortcuts);
  }, [handleUndo, handleRedo]);

  const handleInputChange = (rowIndex: string, colIndex: string, value: string): void => {
    captureState();

    setCellValues((prev) => {
      const existingIndex = prev.findIndex(
        (cell) => cell.fromId === rowIndex && cell.toId === colIndex
      );
      if (existingIndex !== -1) {
        // Update the existing cell value
        const updatedCells = [...prev];
        updatedCells[existingIndex] = {
          ...updatedCells[existingIndex],
          value: value === '' ? NaN : parseFloat(value),
        };
        return updatedCells;
      } else {
        return [
          ...prev,
          { fromId: rowIndex, toId: colIndex, value: value === '' ? NaN : parseFloat(value) },
        ];
      }
    });
  };

  const renderGrid = () => (
    <>
      <div
        className="overflow-auto max-h-[625px] min-h-[400px] border"
        style={{
          borderRadius: '8px 8px 0 0',
          scrollSnapType: 'both mandatory', // Add scroll snapping
        }}
        ref={gridRef}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="grid"
        aria-label="Zone Lookup Table Grid"
      >
        <div
          className="grid w-max"
          style={{
            gridTemplateColumns: `repeat(${zones ? zones?.data.length + 1 : 0}, 140px)`,
            scrollSnapAlign: 'start', // Ensure cells snap
          }}
        >
          {/* Top-left corner */}
          <div className="sticky top-0 left-0 bg-[#E1F4FD] font-bold text-center border z-10 p-2">
            {t('zonePage.zoneLookUp.zones')}
          </div>

          {/* Column Headers */}
          {zones?.data?.map((zone, colIndex) => (
            <div
              key={`col-header-${colIndex}`}
              className={`sticky top-0 bg-[#E1F4FD] font-bold text-center border p-2 ${
                activeCell?.col === colIndex ? 'bg-blue-100 font-extrabold' : ''
              }`}
              role="columnheader"
            >
              {zone.name}
            </div>
          ))}

          {/* Rows */}
          {zones?.data?.map((rowZone, rowIndex) => (
            <React.Fragment key={`row-${rowIndex}`}>
              {/* Row Header */}
              <div
                className={`sticky left-0 font-bold text-center border z-10 p-2 ${
                  rowIndex % 2 === 0 ? 'bg-[#F0FAFF]' : 'bg-white'
                }  ${activeCell?.row === rowIndex ? 'bg-blue-100 font-extrabold' : ''}`}
                role="rowheader"
              >
                {rowZone.name}
              </div>

              {zones?.data.map((colZone, colIndex) => {
                // Find the cell value for this specific fromId and toId
                const cellValue = cellValues.find(
                  (cell) => cell.fromId === `${rowZone.id}` && cell.toId === `${colZone.id}`
                );

                const isActive = activeCell?.row === rowIndex && activeCell?.col === colIndex;

                return (
                  <div
                    key={`${rowIndex}-${colIndex}`}
                    className={`border p-2 text-center scroll-snap-align-start hover:bg-blue-100 cursor-pointer  
                  ${
                    isActive
                      ? 'bg-blue-200'
                      : activeCell?.col === colIndex || activeCell?.row === rowIndex
                        ? 'bg-blue-50'
                        : 'bg-white'
                  }
                  `}
                    onClick={() => handleCellClick(rowIndex, colIndex)}
                    onMouseEnter={() => setHighlight(`From: ${rowZone.name} → To: ${colZone.name}`)}
                    onMouseLeave={() => setHighlight(t('zonePage.zoneLookUp.hoverOverACell'))}
                    role="gridcell"
                    aria-selected={isActive}
                  >
                    {isActive ? (
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={isNaN(cellValue?.value as number) ? '' : cellValue?.value}
                        onChange={(e) =>
                          handleInputChange(`${rowZone.id}`, `${colZone.id}`, e.target.value)
                        }
                        className="w-full text-center border border-gray-300 rounded focus:outline-none focus:ring focus:ring-blue-300"
                        autoFocus
                        placeholder={t('zonePage.zoneLookUp.enterValue')}
                      />
                    ) : (
                      <span
                        className={`text-sm ${cellValue?.value ? 'text-black' : 'text-gray-400 italic'}`}
                      >
                        {cellValue?.value || 'N/A'}
                      </span>
                    )}
                  </div>
                );
              })}
            </React.Fragment>
          ))}
        </div>
      </div>
      <div
        className="sticky top-0 border bg-primary-50 p-2"
        style={{ borderRadius: '0px 0px 8px 8px' }}
      >
        {highlight}
      </div>
    </>
  );

  const zoneTablesOption = useMemo(() => {
    return zoneTables?.data?.map((table) => {
      return { value: table.id as string, label: table.name };
    });
  }, [zoneTables]);

  const getZoneTableById = async (_: string, option: { label: string; value: string }) => {
    try {
      setSelectedValue(option.value);
      const zoneTable = await zoneTableService.getById<BasePriceByZoneDTO>(`${option.value}`);

      if (currentAssignedZoneTable && currentAssignedZoneTable?.zoneTableValues.length > 0) {
        customAlert.warning({
          title: t('priceSetPage.alert.overWriteZoneTableConfirmationTitle'),
          message: t('priceSetPage.alert.overWriteZoneTableConfirmationMsg'),
          firstButtonTitle: t('common.doItAnyWay'),
          secondButtonTitle: t('common.cancel'),
          firstButtonFunction: async () => {
            setCellValues(zoneTableValueMapper(zoneTable));
            customAlert.destroy();
          },
          secondButtonFunction: () => {
            setSelectedValue(undefined);
            customAlert.destroy();
          },
        });
        return;
      }

      setCellValues(zoneTableValueMapper(zoneTable));
    } catch (error) {
      logger.error('ERROR', error as Error);
    }
  };

  const renderButton = () => (
    <Button
      type="primary"
      onClick={onSubmit}
      className="h-[40px] border px-6 rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
      disabled={cellValues.length <= 0 || !isChanged}
    >
      {t('common.save')}
    </Button>
  );

  return (
    <div className="h-full pr-6 py-5">
      <header className="flex justify-end items-center">
        <Select
          options={zoneTablesOption}
          prefixCls="custom-select"
          loading={isFetching || isLoading}
          className="w-full max-w-[400px] md:w-full"
          suffixIcon={<SelectDownArrow />}
          placeholder={t('priceSetPage.form.selectZoneTableGridPlaceholder')}
          onSelect={getZoneTableById}
          value={selectedValue}
        />
      </header>
      <main
        className={`py-4 min-h-[700px] ${(!zones || zones?.data?.length <= 0) && 'flex justify-center items-center'}`}
      >
        {zones ? (
          zones?.data.length > 0 ? (
            renderGrid()
          ) : (
            <EmptyStatePage
              title={t('zonePage.noZonesFound')}
              description={t('zonePage.createNewZone')}
              link={t('zonePage.addZone')}
              onLinkAction={() => navigate(ROUTES.LOCATION.LOCATION_ZONE)}
            />
          )
        ) : (
          <Spinner />
        )}
        <div className="py-4">
          {cellValues.length <= 0 || !isChanged ? (
            <CustomTooltip title={t('priceSetPage.tooltip.disableZoneTableSaveBtn')}>
              {zones && zones?.data.length > 0 && renderButton()}
            </CustomTooltip>
          ) : (
            renderButton()
          )}
        </div>
      </main>
    </div>
  );
};

export default BasePriceByZone;
