.auth-form .ant-input-affix-wrapper,
.auth-form .ant-input-outlined {
  height: 44px !important;
  border: 1px solid #cdd7db;
}
.auth-form,
.ant-form-item .ant-form-item-label > label {
  color: #20363f;
  font-weight: 500;
  font-size: 14px;
}
.auth-form .ant-btn.ant-btn-block:hover,
.otp-form .ant-btn.ant-btn-block:hover {
  background-color: var(--primary-500);
}
.auth-form .create-password {
  margin-bottom: 10px;
}

.auth-form .ant-input-prefix {
  margin-inline-end: 8px !important;
}
.auth-form .ant-input-affix-wrapper > input.ant-input::placeholder,
.auth-form .ant-input-outlined::placeholder {
  color: #96a9b1 !important;
  font-weight: 400;
}
.ant-btn-color-primary {
  box-shadow: none;
}
.login-form-wrapper {
  position: relative;
  background-image: url('../../assets/png/dot.png');
  background-repeat: no-repeat;
  background-position-y: bottom;
  background-size: 100%;
}

.links-wrapper span {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #96a9b1 !important;
  font-weight: 400;
}

.links-wrapper span::after {
  content: '';
  display: block;
  height: 3px;
  width: 3px;
  background-color: #96a9b1;
  border-radius: 100%;
}
.links-wrapper span:last-child::after {
  display: none;
}
