import { AuthLayout } from '@/components/layout/AuthLayout';
import { useLanguage } from '@/hooks/useLanguage';
import { UserOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';
import '../loginPage.css';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { ROUTES } from '@/constant/RoutesConstant';

const ForgetPasswordPage = () => {
  const { t } = useLanguage();

  const { navigate } = useNavigationContext();

  const onFinishHandler = () => {
    navigate(ROUTES.COMMON.OTP_VERIFICATION);
  };

  return (
    <AuthLayout>
      <div className="flex justify-center items-center flex-col gap-4">
        <h1 className="text-[36px] font-semibold text-[#20363F]">
          {t('auth.forgetPasswordPage.header')}
        </h1>
        <p className="text-[#647A83] text-center font-medium">
          {t('auth.forgetPasswordPage.headerDescription')}
        </p>
      </div>
      <div className="mt-8">
        <Form
          name="forgetPassword"
          className="auth-form"
          layout="vertical"
          onFinish={onFinishHandler}
        >
          <Form.Item
            label={t('auth.form.email.label')}
            name="sendOTPEmail"
            required
            rules={[
              { required: true, message: t('auth.emailRequired') },
              { type: 'email', message: t('auth.invalidEmail') },
            ]}
          >
            <Input
              placeholder={t('auth.form.email.placeholder')}
              prefix={<UserOutlined className="text-gray-400" />}
            />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              block
              className={`h-[48px] bg-primary-600 text-white !hover:bg-red-600 text-sm `}
            >
              {t('auth.forgetPasswordPage.sendOtp')}
            </Button>
          </Form.Item>
        </Form>
      </div>
    </AuthLayout>
  );
};

export default ForgetPasswordPage;
