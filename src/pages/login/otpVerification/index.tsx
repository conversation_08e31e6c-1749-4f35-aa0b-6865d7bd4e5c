import { AuthLayout } from '@/components/layout/AuthLayout';
import { useLanguage } from '@/hooks/useLanguage';
import { Form, Button, Typography, Input } from 'antd';
import './otpVerification.css';
import '../loginPage.css';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { ROUTES } from '@/constant/RoutesConstant';
import { useConfig } from '@/contexts/ConfigContext';
import { logger } from '@/lib/logger/logger';

const { Text } = Typography;

const OTPVerificationPage = () => {
  const { t } = useLanguage();
  const [form] = Form.useForm();
  const { navigate } = useNavigationContext();
  const { config } = useConfig();

  const onFinish = (values: { otp: string }) => {
    logger.info('OTP submitted:', values.otp);
    navigate(ROUTES.COMMON.RESET_PASSWORD);
  };

  const validateOTP = (_: any, value: string) => {
    if (value.length !== config.otpLength) {
      return Promise.reject(
        new Error(
          t('auth.otpVerificationPage.mustBeInLength', { length: String(config.otpLength) })
        )
      );
    }
    return Promise.resolve();
  };

  return (
    <AuthLayout>
      <div className="flex justify-center items-center flex-col gap-4">
        <h1 className="text-[36px] font-semibold text-[#20363F]">
          {t('auth.otpVerificationPage.header')}
        </h1>
        <p className="text-[#647A83] text-center font-medium">
          {t('auth.otpVerificationPage.headerDescription', { length: String(config.otpLength) })}{' '}
          <span className="text-primary-600"><EMAIL></span>
        </p>
      </div>
      <Form
        form={form}
        name="otpForm"
        onFinish={onFinish}
        className="otp-form mt-8 flex justify-center items-center flex-col"
      >
        <Form.Item
          name="otp"
          validateFirst
          rules={[
            { required: true, message: t('auth.otpVerificationPage.requireOtp') },
            { validator: validateOTP },
          ]}
        >
          <Input.OTP
            length={config.otpLength}
            onInput={(value: string[]) => {
              const otpValue = value.join('');
              form.setFieldsValue({ otp: otpValue });
            }}
          />
        </Form.Item>
        <div className="text-center font-semibold text-[#647A83] mb-6 ">
          <Text type="secondary">
            {t('auth.otpVerificationPage.doNotGetOtp')}{' '}
            <Typography.Link href="/register" className="!text-primary-600">
              {t('auth.otpVerificationPage.resendOtp')}
            </Typography.Link>
          </Text>
        </div>
        <Button
          type="primary"
          block
          htmlType="submit"
          className="h-[48px] w-full bg-primary-600 text-white !hover:bg-red-600 text-sm"
        >
          {t('auth.otpVerificationPage.verifyButton')}
        </Button>
      </Form>
    </AuthLayout>
  );
};

export default OTPVerificationPage;
