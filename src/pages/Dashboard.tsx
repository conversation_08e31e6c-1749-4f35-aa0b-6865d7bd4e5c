import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { useLanguage } from '@/hooks/useLanguage';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { SupportedLanguage } from '@/i18n/languageLoader';

export const DashboardPage: React.FC = () => {
  const { t, setLanguage, currentLanguage } = useLanguage();
  const notify = useNotificationManager();
  const [userName, setUserName] = useState('Alice');
  const [deliveryCount, setDeliveryCount] = useState(24);

  const handleLanguageChange = (lang: SupportedLanguage) => {
    setLanguage(lang);
    notify.success({
      message: t('notifications.languageChanged', { language: t(`languages.${lang}`) }),
      description: t('notifications.languageChangeDescription'),
    });
  };

  const handleIncreaseDeliveries = () => {
    setDeliveryCount((prev) => prev + 1);
    notify.info({
      message: t('notifications.deliveryAdded'),
      description: t('notifications.currentDeliveries', { count: deliveryCount + 1 }),
    });
  };

  const handleChangeUser = () => {
    const newUser = userName === 'Alice' ? 'Bob' : 'Alice';
    setUserName(newUser);
    notify.custom('success', {
      message: t('notifications.userChanged', { name: newUser }),
      description: t('notifications.welcomeUser', { name: newUser }),
      icon: '👤',
      duration: 5,
      placement: 'bottomRight',
    });
  };

  return (
    <DashboardLayout>
      <h1 className="text-3xl font-bold mb-6">{t('dashboard.title')}</h1>

      <div className="mb-6">
        <p className="text-lg mb-2">{t('dashboard.welcome', { name: userName })}</p>
        <p className="text-md">
          {t('dashboard.languageStatus', { language: t(`languages.${currentLanguage}`) })}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">{t('dashboard.pendingDeliveries')}</h2>
          <p className="text-4xl font-bold text-blue-600">{deliveryCount}</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">{t('dashboard.inTransit')}</h2>
          <p className="text-4xl font-bold text-green-600">18</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">{t('dashboard.deliveredToday')}</h2>
          <p className="text-4xl font-bold text-purple-600">42</p>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
        <button
          onClick={() => handleLanguageChange('EN')}
          className="bg-blue-500 text-white p-2 rounded"
        >
          {t('languages.EN')}
        </button>
        <button
          onClick={() => handleLanguageChange('FR')}
          className="bg-blue-500 text-white p-2 rounded"
        >
          {t('languages.FR')}
        </button>
        <button onClick={handleIncreaseDeliveries} className="bg-green-500 text-white p-2 rounded">
          {t('dashboard.addDelivery')}
        </button>
        <button onClick={handleChangeUser} className="bg-purple-500 text-white p-2 rounded">
          {t('dashboard.changeUser')}
        </button>
        <button
          onClick={() =>
            notify.warning({
              message: t('notifications.warning'),
              description: t('notifications.warningDescription'),
            })
          }
          className="bg-yellow-500 text-white p-2 rounded"
        >
          {t('dashboard.showWarning')}
        </button>
      </div>

      <div className="bg-gray-100 p-4 rounded">
        <h3 className="text-lg font-semibold mb-2">{t('dashboard.translationDemo')}</h3>
        <p>{t('dashboard.demoText', { name: userName, count: deliveryCount, isVIP: true })}</p>
      </div>
    </DashboardLayout>
  );
};
