import React from 'react';
import { TrackingLayout } from '@/components/layout/TrackingLayout';
import { useLanguage } from '@/hooks/useLanguage';

export const TrackingPage: React.FC = () => {
  const { t } = useLanguage();

  return (
    <TrackingLayout>
      <h1 className="text-3xl font-bold mb-6">{t('common.tracking')}</h1>
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Tracking Information</h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Enter a tracking number to see delivery status.
          </p>
        </div>
        <div className="border-t border-gray-200">
          <div className="px-4 py-5 sm:p-6">
            <input
              type="text"
              placeholder="Enter tracking number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button className="mt-4 bg-blue-500 text-white font-bold py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
              Track Package
            </button>
          </div>
        </div>
      </div>
    </TrackingLayout>
  );
};
