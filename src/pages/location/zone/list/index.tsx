import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  deleteSvg,
  PlusButtonIcon,
  infoCircleOutlined,
  InfoForPopupIcon,
  AssignToIcon,
} from '@/assets';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import { IContextMenuItems } from '@customTypes/ContextMenuTypes';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import SearchFilterComponent from '@/components/specific/searchFilter/SearchFilterComponent';
import { Button, Divider, Form } from 'antd';
import CustomModal from '@/components/common/modal/CustomModal';
import { IIsOpenModal, IZone } from './zone.type';
import { on } from '@/contexts/PulseContext';
import { AgGridReact } from 'ag-grid-react';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import { GridNames } from '@/types/AppEvents';
import { IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes.ts';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { useZoneColDefs } from './useZoneColDefs';
import ZoneOperationForm from '@pages/location/zone/list/zoneOperationForm.tsx';
import { useLanguage } from '@/hooks/useLanguage';
import { useLogger } from '@lib/logger/useLogger.ts';
import { zoneHooks } from '@api/zones/useZones.ts';
import { CreateZoneDto } from '@/api/zones/zone.types';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import usePreventExits from '@/hooks/usePreventExits';
import { defaultPagination } from '@/constant/generalConstant';
import { getPaginationData } from '@/lib/helper';
import { filterableModules } from '@/constant/AdvanceFilterConstant';
import { advanceFilterObjectMapper, maskQuickFilterData } from '@/lib/SearchFilterTypeManage';
import { IAssignedFilters } from '@/pages/logistics/orders/orders.types';
import ActiveFilters from '@/components/specific/activeFilters/ActiveFilters';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';

const ZonePage = () => {
  const log = useLogger('ZonePage');
  const [isModalOpen, setIsModalOpen] = useState<IIsOpenModal>({ isOpen: false, isEdit: false });
  const [isHowToOpen, setIsHowToOpen] = useState(false);
  const [form] = Form.useForm();
  const [zones, setZones] = useState<IZone[]>();
  const [searchText, setSearchText] = useState('');
  const gridRef = useRef<AgGridReact<IZone>>(null);
  const [cellData, setCellData] = useState<IZone>({} as IZone);
  const [usedPostalCodes, setUsedPostalCodes] = useState<string[]>([]);
  const [isUsedPostalCode, setIsUsedPostalCode] = useState<string[]>([]);
  const [filterParams, setFilterParams] = useState(defaultPagination);
  const [selectedQuickFilterData, setSelectedQuickFilterData] = useState<IAssignedFilters[]>([]);

  const { isBlocked, setIsBlocked } = useNavigationContext();
  const { setPreventExit } = usePreventExits();

  const {
    data: zoneList,
    isLoading: isLoadingList,
    isFetching,
    refetch: refetchZones,
  } = zoneHooks.useList(filterParams);

  const { t } = useLanguage();

  useEffect(() => {
    if (zoneList && zoneList?.data) {
      setZones(zoneList?.data);
    }
  }, [zoneList]);

  const notificationManager = useNotificationManager();

  useEffect(() => {
    if (zones) {
      setUsedPostalCodes(zones.flatMap((zone) => zone.postalCodes));
    }
  }, [zones]);

  const isColumnSortable = useCallback((field: string) => {
    return filterableModules.zone.sortable.includes(field);
  }, []);

  const deleteMutation = zoneHooks.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('zonePage.notification.successDeleted'),
      });
      await refetchZones();
    },
  });

  const createMutation = zoneHooks.useCreate({
    onSuccess: async () => {
      await refetchZones();
      notificationManager.success({
        message: t('common.success'),
        description: t('zonePage.notification.successAdded'),
      });
    },
  });

  const updateMutation = zoneHooks.useUpdate({
    onSuccess: async () => {
      await refetchZones();
      notificationManager.success({
        message: t('common.success'),
        description: t('zonePage.notification.successUpdated'),
      });
    },
  });

  const onFinish = useCallback(
    async (values: CreateZoneDto) => {
      const initialData = isModalOpen.isEdit ? cellData : undefined;
      const zoneData: CreateZoneDto = {
        ...values,
        postalCodes: values.postalCodes.map((c) => c.trim()).filter((c) => c !== ''),
      };

      if (isModalOpen.isEdit && initialData?.id) {
        await updateMutation.mutateAsync({ id: initialData.id, data: zoneData });
      } else {
        await createMutation.mutateAsync(zoneData);
      }
      setIsModalOpen({ isOpen: false });
    },
    [cellData, createMutation, isModalOpen.isEdit, updateMutation]
  );

  const deleteZoneConfirmation = useCallback(
    (cellData: IZone) => {
      if (cellData?.id) {
        const id = cellData.id;
        customAlert.error({
          title: `${t('zonePage.confirmDeleteZone')}`.replace('this', cellData.name),
          message: `${t('priceModifiers.deleteModifierWarning')}`.replace('price modifier', 'zone'),
          secondButtonFunction: () => customAlert.destroy(),
          firstButtonFunction: async () => {
            await deleteMutation.mutateAsync(id);
            customAlert.destroy();
          },
          firstButtonTitle: `${t('common.delete')}`,
          secondButtonTitle: `${t('common.cancel')}`,
        });
      }
    },
    [t, deleteMutation]
  );

  const clearAllFunctionRef = useRef<{ handleClearAll: () => void }>({
    handleClearAll: () => {},
  });

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);
    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const applyFilters = useCallback(
    async (data: { filters: IAssignedFilters[] }) => {
      const filterObject = await advanceFilterObjectMapper(data.filters);

      data.filters.length > 0 &&
        setFilterParams({
          pageNumber: filterParams.pageNumber,
          pageSize: filterParams.pageSize,
          searchTerm: filterParams.searchTerm,
          sortDirection: filterParams.sortDirection,
          ...filterObject,
        });

      setSelectedQuickFilterData(
        data.filters.length > 0 ? (maskQuickFilterData(data.filters) as IAssignedFilters[]) : []
      );
    },
    [filterParams]
  );

  const clearAllToDefault = () => {
    setSearchText('');
    setFilterParams({
      pageNumber: filterParams.pageNumber,
      pageSize: filterParams.pageSize,
      searchTerm: filterParams.searchTerm,
      sortDirection: filterParams.sortDirection,
    });
    setSelectedQuickFilterData([]);
    clearAllFunctionRef.current.handleClearAll();
  };

  on('columnManager:changed', (data) => {
    if (data.gridName === 'zoneGrid' && gridRef.current?.api) {
      log.debug('Column order changed', {
        gridName: data.gridName,
        newOrder: data.gridState.map((column: { id: string }) => column.id),
      });
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const closeModalHandler = useCallback(() => {
    if (isBlocked) {
      customAlert.warning({
        title: t('common.alert.areYouSure'),
        message: t('common.alert.preventExist'),
        firstButtonTitle: t('common.leave'),
        secondButtonTitle: t('common.stay'),
        firstButtonFunction: () => {
          setIsModalOpen({ isOpen: false, isEdit: false });
          setPreventExit(false);
          setIsBlocked(false);
          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      });
      return;
    }
    setIsModalOpen({ isOpen: false });
    setIsUsedPostalCode([]);
  }, [isBlocked, setIsBlocked, setPreventExit, t]);

  useEffect(() => {
    if (!isModalOpen.isOpen) {
      form.resetFields();
      setIsUsedPostalCode([]);
    }
  }, [form, isModalOpen]);

  const zoneColDefs: IColDef[] = useZoneColDefs({
    searchText,
    onEditZone: (zone: IZone) => {
      setCellData(zone);
      form.setFieldsValue({
        ...zone,
        postalCodes: (zone.postalCodes || []).map((c: string) => c.toUpperCase()),
      });
      setIsModalOpen({ isOpen: true, isEdit: true });
    },
    onDeleteZone: (data: IZone) => {
      deleteZoneConfirmation(data);
    },
    alreadyInUsedPostalCodes: usedPostalCodes,
    isColumnSortable,
  });

  const isLoadingButton = useMemo(
    () =>
      isLoadingList ||
      isFetching ||
      deleteMutation.isPending ||
      updateMutation.isPending ||
      createMutation.isPending,
    [
      isLoadingList,
      isFetching,
      deleteMutation.isPending,
      updateMutation.isPending,
      createMutation.isPending,
    ]
  );
  const paginationData = useMemo(() => getPaginationData(zoneList), [zoneList]);

  const zoneContextMenuItems: IContextMenuItems[] = useMemo(
    () => [
      {
        label: t('zonePage.emptyState.link'),
        key: 'Open',
        icon: AssignToIcon as React.ElementType,
        onClick: () => {
          log.debug('Opening new zone modal from context menu');
          setIsModalOpen({ isOpen: true });
        },
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} />) as unknown as React.ElementType,
        onClick: () => deleteZoneConfirmation(cellData),
        key: 'delete',
      },
    ],
    [t, cellData, deleteZoneConfirmation, log]
  );

  const Footer = useMemo(
    () => (
      <footer className="custom-modals-footer flex gap-2 justify-end">
        <Button className="rounded-lg border-[#96A9B1]" onClick={closeModalHandler}>
          {t('common.cancel')}
        </Button>
        <Button
          form="zone-form"
          htmlType="submit"
          type="primary"
          loading={isLoadingButton}
          className="bg-primary-600 text-white border-0 rounded-lg hover:!bg-primary-600"
        >
          {isModalOpen.isEdit ? `${t('common.update')}` : `${t('common.save')}`}
        </Button>
      </footer>
    ),
    [closeModalHandler, isLoadingButton, isModalOpen.isEdit, t]
  );

  const triggerSearch = useCallback((value: string) => searchHandler(value), [searchHandler]);

  return (
    <>
      <CustomModal
        open={isHowToOpen}
        onCancel={() => setIsHowToOpen(false)}
        modalTitle={t('zonePage.howZoneWorks')}
        modalDescription={t('zonePage.guidelinesForZones')}
        destroyOnClose
        footer={
          <div>
            <Button
              onClick={() => setIsHowToOpen(false)}
              className="w-[100px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
            >
              {t('zonePage.ok')}, {t('zonePage.gotIt')}
            </Button>
          </div>
        }
      >
        <div className="p-3 h-full border border-primary-600 rounded-md bg-[#F0FAFF]">
          <div className="flex flex-col gap-2">
            <div className="w-full flex gap-2">
              <img src={InfoForPopupIcon} alt="" />{' '}
              <span className="font-semibold text-[16px]">
                {t('zonePage.createZoneGuidelines')}
              </span>
            </div>
            <div className="p-2 ">
              <ol className="list-decimal pl-5 flex flex-col gap-5">
                <li>
                  <div className="flex flex-col">
                    <span className="font-semibold">
                      {t('zonePage.click')} "{t('zonePage.addZone')}"
                    </span>
                    <span>
                      {t('zonePage.pressThe')}{' '}
                      <span className="font-semibold">{t('zonePage.addZone')}</span>{' '}
                      {t('zonePage.buttonToBegin')}
                    </span>
                  </div>
                </li>
                <li>
                  <div className="flex flex-col gap-2">
                    <span className="font-semibold">{t('zonePage.fillInTheDetails')}</span>
                    <div className="flex flex-col">
                      <div>
                        <span className="font-semibold">{t('zonePage.zoneName')}</span>
                        {t('zonePage.enterUniqueZoneName')}
                      </div>
                      <div>
                        <span className="font-semibold">{t('zonePage.postalCodes')}</span>{' '}
                        {t('zonePage.addMultiplePostalCodes')}
                      </div>
                      <div>
                        <span className="font-semibold">{t('zonePage.notes')}</span>{' '}
                        {t('zonePage.addAdditionalInfo')}
                      </div>
                    </div>
                  </div>
                </li>
                <li>
                  <div className="flex flex-col gap-1">
                    <span className="font-semibold"> {t('zonePage.saveYourZone')}</span>
                    <span>{t('zonePage.clickSaveToFinalize')}</span>
                  </div>
                </li>
              </ol>
            </div>
          </div>
        </div>
      </CustomModal>
      <CustomModal
        modalTitle={isModalOpen.isEdit ? `${t('zonePage.editZone')}` : `${t('zonePage.addZone')}`}
        modalDescription={
          isModalOpen.isEdit
            ? t('zonePage.enterDetails').replace('Enter', 'Edit').replace('new', '')
            : t('zonePage.enterDetails')
        }
        open={isModalOpen.isOpen}
        onCancel={closeModalHandler}
        footer={Footer}
        maskClosable={false}
        keyboard={false}
      >
        <ZoneOperationForm
          form={form}
          onFinish={onFinish}
          open={isModalOpen}
          refreshGrid={refetchZones}
          initialData={isModalOpen.isEdit ? cellData : undefined}
          setIsModalOpen={setIsModalOpen}
          alreadyInUsedPostalCodes={usedPostalCodes}
          setIsUsedPostalCode={setIsUsedPostalCode}
          isUsedPostalCode={isUsedPostalCode}
        />
      </CustomModal>

      <div className="flex h-screen">
        <div className="flex-1 flex flex-col overflow-hidden bg-white">
          <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
            <div className="md:w-1/3 flex flex-col 3xsm:w-full">
              <PageHeadingComponent title="Zone" />
            </div>
            <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
              <div className="flex gap-3">
                <SearchFilterComponent
                  onSearch={triggerSearch}
                  colDefs={zoneColDefs.filter((colDef) => colDef?.field !== 'postalCodes')}
                  isSetQuickFilter={false}
                  searchInputPlaceholder="Search zone"
                  onFilterApply={applyFilters}
                  setSelectedQuickFilterData={setSelectedQuickFilterData}
                  supportedFields={filterableModules.zone.advanceFilter}
                  clearAllFunctionRef={clearAllFunctionRef}
                  setFilterParams={setFilterParams}
                />
              </div>

              <div className="pt-5">
                <Divider type="vertical" className="3xsm:hidden md:block h-[40px] !m-0" />
              </div>
              <div className="3xsm:pt-0 md:pt-5 flex items-center gap-3">
                <Button
                  onClick={() => setIsHowToOpen(true)}
                  className="w-[180px] h-[40px] border-[1px] rounded-[8px] font-[500] hover:!bg-white hover:!text-black hover:!border-grey-300"
                >
                  <img className="w-[16px] h-[16px]" src={infoCircleOutlined} />
                  {t('zonePage.howZoneWorks')}
                </Button>
                <Button
                  className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                  icon={<PlusButtonIcon />}
                  onClick={() => {
                    log.debug('Opening new zone modal from button');
                    setIsModalOpen({ isOpen: true });
                  }}
                >
                  {t('zonePage.addZone')}
                </Button>
              </div>
            </div>
          </div>
          <main className="h-screen overflow-x-hidden overflow-y-auto bg-white pt-[20px]">
            <ActiveFilters
              selectedQuickFilterData={selectedQuickFilterData}
              clearAllToDefault={clearAllToDefault}
              colDefs={zoneColDefs}
            />
            <div className="mx-auto pr-6 py-5 h-full flex justify-center items-center ">
              <CustomAgGrid
                className={selectedQuickFilterData.length > 0 ? 'md:!h-[79vh]' : ''}
                gridRef={gridRef}
                loading={isLoadingList || isFetching}
                rowData={zones}
                columnDefs={zoneColDefs}
                isContextMenu
                contextMenuItem={zoneContextMenuItems}
                onContextMenu={(params) => {
                  setCellData(params.data);
                }}
                gridName={GridNames.zoneGrid}
                paginationProps={{
                  ...paginationData,
                  onPaginationChange(page, pageLimit) {
                    setFilterParams((prev) => ({
                      ...prev,
                      pageNumber: page,
                      pageSize: pageLimit,
                    }));
                  },
                }}
                onSortChanged={(params: IExtendedSortChangedEvent) =>
                  setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
                }
                emptyState={{
                  title:
                    searchText || selectedQuickFilterData.length > 0
                      ? t('common.noMatchesFound')
                      : t('zonePage.noZonesFound'),
                  description:
                    searchText || selectedQuickFilterData.length > 0
                      ? ''
                      : t('zonePage.createNewZone'),
                  link: t('zonePage.addZone'),
                  onLinkAction: () => {
                    setIsModalOpen({ isOpen: true });
                  },
                }}
              />
            </div>
          </main>
        </div>
      </div>
    </>
  );
};

export default ZonePage;
