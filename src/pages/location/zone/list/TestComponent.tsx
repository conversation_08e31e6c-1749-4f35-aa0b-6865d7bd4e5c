import React, { useState } from 'react';
import { zoneHooks } from '@api/zones/useZones.ts';
import { CreateZoneDto, Zone } from '@api/zones/zone.types.ts';

const TestComponent: React.FC = () => {
  // State for storing the last created zone ID
  const [lastCreatedId, setLastCreatedId] = useState<string>('');
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);

  // Fetch zones list
  const {
    data: zonesList,
    isLoading: isLoadingList,
    refetch: refetchZones,
  } = zoneHooks.useList({
    pageNumber: 1,
    pageSize: 10,
  });

  // Get single zone details
  const { data: zoneDetails, isLoading: isLoadingDetails } = zoneHooks.useEntity(
    selectedZone?.id || '',
    {
      enabled: !!selectedZone,
    }
  );

  // Mutations
  const createMutation = zoneHooks.useCreate({
    onSuccess: (data) => {
      setLastCreatedId(data.id);
      refetchZones();
    },
  });

  const updateMutation = zoneHooks.useUpdate({
    onSuccess: () => {
      refetchZones();
    },
  });

  const deleteMutation = zoneHooks.useDelete({
    onSuccess: () => {
      setSelectedZone(null);
      refetchZones();
    },
  });

  // Handler functions
  const handleCreate = () => {
    const newZone: CreateZoneDto = {
      name: `Test Zone ${Date.now()}`,
      postalCodes: ['12345', '67890'],
      notes: 'Created from test component',
    };

    createMutation.mutate(newZone);
  };

  const handleUpdate = (zoneId: string) => {
    const updatedZone: CreateZoneDto = {
      name: `Updated Zone ${Date.now()}`,
      postalCodes: ['11111', '22222'],
      notes: 'Updated from test component',
    };

    updateMutation.mutate({ id: zoneId, data: updatedZone });
  };

  const handleDelete = (zoneId: string) => {
    if (window.confirm('Are you sure you want to delete this zone?')) {
      deleteMutation.mutate(zoneId);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Zone Operations Test</h1>

      {/* Create Operation */}
      <section className="mb-8 p-4 border rounded">
        <h2 className="text-xl font-semibold mb-4">Create Zone</h2>
        <button
          onClick={handleCreate}
          disabled={createMutation.isPending}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-400"
        >
          {createMutation.isPending ? 'Creating...' : 'Create New Zone'}
        </button>
        {createMutation.isError && (
          <p className="mt-2 text-red-500">Error: {createMutation.error?.message}</p>
        )}
        {lastCreatedId && (
          <p className="mt-2 text-green-600">Last created zone ID: {lastCreatedId}</p>
        )}
      </section>

      {/* List Operation */}
      <section className="mb-8 p-4 border rounded">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Zone List</h2>
          <button
            onClick={() => refetchZones()}
            className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh
          </button>
        </div>

        {isLoadingList ? (
          <p>Loading zones...</p>
        ) : zonesList?.data.length ? (
          <div className="space-y-4">
            {zonesList.data.map((zone) => (
              <div
                key={zone.id}
                className="p-4 border rounded hover:bg-gray-50 flex justify-between items-center"
              >
                <div>
                  <h3 className="font-medium">{zone.name}</h3>
                  <p className="text-sm text-gray-600">
                    Postal Codes: {zone.postalCodes.join(', ')}
                  </p>
                  <p className="text-sm text-gray-500">{zone.notes}</p>
                </div>
                <div className="space-x-2">
                  <button
                    onClick={() => handleUpdate(zone.id)}
                    disabled={updateMutation.isPending}
                    className="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:bg-gray-400"
                  >
                    {updateMutation.isPending ? 'Updating...' : 'Update'}
                  </button>
                  <button
                    onClick={() => handleDelete(zone.id)}
                    disabled={deleteMutation.isPending}
                    className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 disabled:bg-gray-400"
                  >
                    {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
                  </button>
                  <button
                    onClick={() => setSelectedZone(zone)}
                    className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p>No zones found</p>
        )}
      </section>

      {/* Zone Details */}
      {selectedZone && (
        <section className="mb-8 p-4 border rounded">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Zone Details</h2>
            <button
              onClick={() => setSelectedZone(null)}
              className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Close
            </button>
          </div>

          {isLoadingDetails ? (
            <p>Loading details...</p>
          ) : zoneDetails ? (
            <div className="space-y-2">
              <p>
                <strong>ID:</strong> {zoneDetails.id}
              </p>
              <p>
                <strong>Name:</strong> {zoneDetails.name}
              </p>
              <p>
                <strong>Postal Codes:</strong> {zoneDetails.postalCodes.join(', ')}
              </p>
              <p>
                <strong>Notes:</strong> {zoneDetails.notes}
              </p>
              <p>
                <strong>Created At:</strong> {new Date(zoneDetails.createdAt).toLocaleString()}
              </p>
              <p>
                <strong>Updated At:</strong> {new Date(zoneDetails.updatedAt).toLocaleString()}
              </p>
              <p>
                <strong>Created By:</strong> {zoneDetails.createdBy}
              </p>
              <p>
                <strong>Updated By:</strong> {zoneDetails.updatedBy}
              </p>
            </div>
          ) : (
            <p>No details available</p>
          )}
        </section>
      )}

      {/* Operation Status */}
      <section className="fixed bottom-4 right-4 space-y-2">
        {(createMutation.isPending || updateMutation.isPending || deleteMutation.isPending) && (
          <div className="p-4 bg-blue-500 text-white rounded shadow-lg">
            Operation in progress...
          </div>
        )}
        {(createMutation.isSuccess || updateMutation.isSuccess || deleteMutation.isSuccess) && (
          <div className="p-4 bg-green-500 text-white rounded shadow-lg">
            Operation completed successfully!
          </div>
        )}
        {(createMutation.isError || updateMutation.isError || deleteMutation.isError) && (
          <div className="p-4 bg-red-500 text-white rounded shadow-lg">
            Error occurred during operation
          </div>
        )}
      </section>
    </div>
  );
};

export default TestComponent;
