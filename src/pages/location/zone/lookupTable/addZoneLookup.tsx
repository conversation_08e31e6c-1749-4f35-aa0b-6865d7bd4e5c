import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Button, Checkbox, Form, Input, InputNumber, Modal, Tooltip, Typography } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';

import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { EditOutlined } from '@ant-design/icons';
import { IAdjustCellsType, IZone } from '@pages/location/zone/list/zone.type.ts';
import { useNotificationManager } from '@hooks/useNotificationManger.ts';
import { KeyboardIcon } from 'lucide-react';
import { EmptyStatePage } from '@/components/common/emptyState/EmptyStatePage';
import { useLanguage } from '@/hooks/useLanguage';
import { formErrorRegex } from '@/constant/Regex';
import { ArrowSvgIcon, FlashFeaturesIcon, infoCircleOutlined, InfoForPopupIcon } from '@/assets';
import CustomModal from '@/components/common/modal/CustomModal';
import { zoneTableHook } from '@/api/zoneTable/useZoneTable';
import { CreateZoneTableDto, CreateZoneTableValueDto } from '@/api/zoneTable/zoneTable.types';
import { zoneHooks } from '@/api/zones/useZones';
import { ROUTES } from '@/constant/RoutesConstant';

const { Text } = Typography;

const AddZoneLookupTablePage = () => {
  const { t } = useLanguage();
  // State Management Hooks.(like you dont know?lol)
  const [loading, setLoading] = useState(false);
  const [zones, setZones] = useState<IZone[]>([]);
  const [cellValues, setCellValues] = useState<CreateZoneTableValueDto[]>([]);
  // Active Cell and Interaction State
  const [activeCell, setActiveCell] = useState<{ row: number; col: number } | null>(null);
  const [highlight, setHighlight] = useState(t('zonePage.zoneLookUp.hoverOverACell'));

  // Table Metadata State
  const [tableName, setTableName] = useState('');
  const [defaultTableName, setDefaultTableName] = useState('');
  const [defaultCellValues, setDefaultCellValues] = useState<CreateZoneTableValueDto[]>([]);
  const [isReversePricing, setIsReversePricing] = useState(false);
  // Undo/Redo State Management
  const [undoStack, setUndoStack] = useState<CreateZoneTableValueDto[][]>([]);
  const [redoStack, setRedoStack] = useState<CreateZoneTableValueDto[][]>([]);

  // Modal Visibility State
  const [isHelpModalVisible, setIsHelpModalVisible] = useState(false);
  const [isFillEmptyCellsModalVisible, setIsFillEmptyCellsModalVisible] = useState(false);
  const [isAdjustAllCellsModalVisible, setIsAdjustAllCellsModalVisible] = useState(false);

  // Modal Input State
  const [fillEmptyCellsValue, setFillEmptyCellsValue] = useState(0);
  const [adjustAllCellsValue, setAdjustAllCellsValue] = useState(0);
  const [adjustAllCellsType, setAdjustAllCellsType] = useState<IAdjustCellsType['type']>('fixed');
  // Hooks and Utilities
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditMode = Boolean(id);
  const notificationManager = useNotificationManager();
  const gridRef = useRef<HTMLDivElement>(null);
  const [form] = Form.useForm();
  const { refetch: refetchZoneTableList } = zoneTableHook.useList({ pageNumber: 1, pageSize: 100 });

  const { data: lookupTable, isLoading } = zoneTableHook.useEntity(id as string, {
    enabled: isEditMode,
  });
  const [isChanged, setIsChanged] = useState(false);
  const zonesData = zoneHooks.useList({ pageNumber: 1, pageSize: 100 }).data?.data;
  const [reciprocalValueAndIds, setReciprocalValueAndIds] = useState<
    { value: number; originZoneId: string; destinationZoneId: string }[]
  >([]);

  /**
   * State Capture and History Management
   */
  const captureState = useCallback(() => {
    setUndoStack((prev) => [...prev, cellValues]);
    setRedoStack([]); // Clear redo stack when a new action is performed
  }, [cellValues]);
  const handleUndo = useCallback(() => {
    if (undoStack.length === 0) return;
    const previousState = undoStack[undoStack.length - 1];
    setUndoStack((prev) => prev.slice(0, -1));
    setRedoStack((prev) => [...prev, cellValues]);
    setCellValues(previousState);
  }, [cellValues, undoStack]);

  const handleRedo = useCallback(() => {
    if (redoStack.length === 0) return;
    const nextState = redoStack[redoStack.length - 1];
    setRedoStack((prev) => prev.slice(0, -1));
    setUndoStack((prev) => [...prev, cellValues]);
    setCellValues(nextState);
  }, [cellValues, redoStack]);
  useEffect(() => {
    if (isEditMode && lookupTable && cellValues) {
      const lookupMap = new Map();

      lookupTable.zoneTableValues.forEach((cell) => {
        const key = `${cell.originZoneId}-${cell.destinationZoneId}`;
        lookupMap.set(key, cell.value);
      });

      const hasChanges = cellValues.some((cell) => {
        const key = `${cell.originZoneId}-${cell.destinationZoneId}`;
        return lookupMap.get(key) !== cell.value;
      });

      setIsChanged(!hasChanges);
    }
  }, [cellValues, isEditMode, lookupTable]);

  /**
   * Data Loading and Initialization
   */

  const loadZonesAndTable = useCallback(async () => {
    setLoading(true);
    try {
      if (!zonesData) return;
      setZones(zonesData);
      if (isLoading) return;

      if (isEditMode) {
        if (lookupTable) {
          setTableName(lookupTable.name);
          setCellValues(lookupTable?.zoneTableValues);
          setDefaultTableName(lookupTable.name);
          setDefaultCellValues(lookupTable.zoneTableValues);
          form.setFieldsValue(lookupTable);
        } else {
          notificationManager.error({
            message: `${t('common.error')}`,
            description: `${t('zonePage.zoneLookUp.tableNotFound')}`,
          });
          navigate(ROUTES.LOCATION.LOCATION_ZONE_LOOKUP_TABLE);
        }
      }
    } catch (error) {
      notificationManager.error({
        message: `${t('common.error')}`,
        description: `${t('zonePage.zoneLookUp.failedToLoad')}`,
      });
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [form, isEditMode, isLoading, lookupTable, navigate, notificationManager, t, zonesData]);

  /**
   * Event Handlers
   */
  const handleDiscardChanges = () => {
    if (isEditMode) {
      setTableName(defaultTableName);
      setCellValues(defaultCellValues);
      notificationManager.info({
        message: `${t('zonePage.zoneLookUp.changesDiscarded')}`,
        description: `${t('zonePage.zoneLookUp.formRevertedToOriginal')}`,
      });
      form.setFieldsValue({ name: defaultTableName });
    } else {
      navigate(ROUTES.LOCATION.LOCATION_ZONE_LOOKUP_TABLE);
    }
  };

  const handleCellClick = (rowIndex: number, colIndex: number): void => {
    setActiveCell({ row: rowIndex, col: colIndex });
    if (isReversePricing) {
      handleReciprocal();
    }
  };

  const handleInputChange = (rowIndex: string, colIndex: string, value: string): void => {
    captureState();
    if (value.length === 0) {
      const cellFilter = (cell: CreateZoneTableValueDto) => {
        const primaryMatch = cell.originZoneId === rowIndex && cell.destinationZoneId === colIndex;

        if (!isReversePricing) {
          return !primaryMatch;
        }

        const reciprocalMatch =
          cell.originZoneId === colIndex && cell.destinationZoneId === rowIndex;
        return !(primaryMatch || reciprocalMatch);
      };

      setCellValues((prev) => prev.filter(cellFilter));

      if (isReversePricing) {
        setReciprocalValueAndIds((prev) => prev.filter(cellFilter));
      }

      return;
    }
    if (isReversePricing) {
      setReciprocalValueAndIds([
        ...reciprocalValueAndIds,
        { value: parseFloat(value), originZoneId: rowIndex, destinationZoneId: colIndex },
      ]);
    }
    setCellValues((prev) => {
      const existingIndex = prev.findIndex(
        (cell) => cell.originZoneId === rowIndex && cell.destinationZoneId === colIndex
      );
      if (existingIndex !== -1) {
        // Update the existing cell value
        const updatedCells = [...prev];
        updatedCells[existingIndex] = {
          ...updatedCells[existingIndex],
          value: parseFloat(value),
        };
        return updatedCells;
      } else {
        const newCell: CreateZoneTableValueDto = {
          originZoneId: rowIndex,
          destinationZoneId: colIndex,
          value: parseFloat(value),
        };
        return [...prev, newCell];
      }
    });
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (!activeCell) return;

    const { row, col } = activeCell;
    let newRow = row;
    let newCol = col;

    switch (event.key) {
      case 'ArrowUp':
        newRow = Math.max(row - 1, 0);
        break;
      case 'ArrowDown':
        newRow = Math.min(row + 1, zones.length - 1);
        break;
      case 'ArrowLeft':
        newCol = Math.max(col - 1, 0);
        break;
      case 'ArrowRight':
        newCol = Math.min(col + 1, zones.length - 1);
        break;
      case 'Escape':
        setActiveCell(null);
        setHighlight(t('zonePage.zoneLookUp.hoverOverACell'));
        return;
      default:
        return;
    }
    if (isReversePricing) {
      handleReciprocal();
    }
    setActiveCell({ row: newRow, col: newCol });
    setHighlight(`From: ${zones[newRow].name} → To: ${zones[newCol].name}`);
  };

  /**
   * Bulk Operation Handlers
   */
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const handleReciprocal = () => {
    captureState();
    const newCellValues = [...cellValues];

    if (reciprocalValueAndIds.length > 0) {
      reciprocalValueAndIds.forEach((cellForParent) => {
        const existingIndex = newCellValues.findIndex(
          (cell) =>
            cell.originZoneId === cellForParent.destinationZoneId &&
            cell.destinationZoneId === cellForParent.originZoneId
        );
        if (existingIndex !== -1) {
          newCellValues[existingIndex].value = cellForParent.value;
        } else {
          newCellValues.push({
            originZoneId: cellForParent.destinationZoneId,
            destinationZoneId: cellForParent.originZoneId,
            value: cellForParent.value,
          });
        }
      });
    }

    setCellValues(newCellValues);
    setReciprocalValueAndIds([]);
  };
  const handleFillEmptyCells = () => {
    captureState();
    const totalCells = zones.length * zones.length;
    const filledCells = cellValues.length;
    if (filledCells === totalCells) {
      notificationManager.error({
        message: t('zonePage.zoneLookUp.valueCannotBeFilled'),
        description: t('zonePage.zoneLookUp.allCellsAlreadyFilled'),
      });
      return;
    }
    const newCellValues = [...cellValues];

    zones.forEach((row) => {
      zones.forEach((col) => {
        const existingCellIndex = newCellValues.findIndex(
          (cell) => cell.originZoneId === `${row.id}` && cell.destinationZoneId === `${col.id}`
        );

        if (existingCellIndex === -1) {
          newCellValues.push({
            originZoneId: `${row.id}`,
            destinationZoneId: `${col.id}`,
            value: fillEmptyCellsValue,
          });
        }
      });
    });

    setCellValues(newCellValues);
    setIsFillEmptyCellsModalVisible(false);
    setFillEmptyCellsValue(0);
    fillEmptyCellsForm.resetFields();
    notificationManager.success({
      message: `${t('common.success')}`,
      description: `${t('zonePage.zoneLookUp.emptyCellsFilled')}`,
    });
  };

  const handleAdjustAllCells = () => {
    captureState();
    if (cellValues.length === 0) {
      notificationManager.error({
        message: t('zonePage.zoneLookUp.valueCannotBeAdjusted'),
        description: t('zonePage.zoneLookUp.allCellsEmpty'),
      });
      return;
    }
    const adjustValue = adjustAllCellsValue;

    const newCellValues = cellValues.map((cell) => {
      const currentValue = cell.value;

      if (adjustAllCellsType === 'fixed') {
        return {
          ...cell,
          value: Number((currentValue + adjustValue).toFixed(2)),
        };
      } else {
        return {
          ...cell,
          value: Number((currentValue * (1 + adjustValue / 100)).toFixed(2)),
        };
      }
    });

    setCellValues(newCellValues);
    setIsAdjustAllCellsModalVisible(false);
    setAdjustAllCellsValue(0);
    adjustAllValuesForm.resetFields();
    notificationManager.success({
      message: `${t('common.success')}`,
      description: `${t('zonePage.zoneLookUp.allCellsAdjusted')}`,
    });
  };
  const createMutation = zoneTableHook.useCreate({
    onSuccess: async () => {
      await refetchZoneTableList();
      notificationManager.success({
        message: t('common.success'),
        description: t('zonePage.notification.successAdded'),
      });
    },
  });

  const updateMutation = zoneTableHook.useUpdate({
    onSuccess: async () => {
      await refetchZoneTableList();
      notificationManager.success({
        message: t('common.success'),
        description: t('zonePage.notification.successUpdated'),
      });
    },
  });

  /**
   * Form Submission Handler
   */
  const onSubmit = async () => {
    if (!tableName.trim()) {
      notificationManager.error({
        message: `${t('common.error')}`,
        description: `${t('zonePage.zoneLookUp.tableShouldNotBeEmpty')}`,
      });
      return;
    }

    setLoading(true);
    const lookupTableDataForTable: CreateZoneTableDto = {
      name: tableName,
      zoneTableValues: cellValues,
    };

    if (isEditMode && id) {
      await updateMutation.mutateAsync({ id: id, data: lookupTableDataForTable });
    } else {
      const response = await createMutation.mutateAsync(lookupTableDataForTable);
      if (response) {
        navigate(ROUTES.LOCATION.LOCATION_ZONE_LOOKUP_TABLE_EDIT.replace(':id', `${response.id}`), {
          replace: true,
        });
      }
    }
  };

  /**
   * Side Effects
   */
  // Keyboard Shortcut Listener
  useEffect(() => {
    const handleShortcuts = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.key === 'z') {
        event.preventDefault();
        handleUndo();
      }
      if (event.ctrlKey && event.key === 'y') {
        event.preventDefault();
        handleRedo();
      }
    };

    document.addEventListener('keydown', handleShortcuts);
    return () => document.removeEventListener('keydown', handleShortcuts);
  }, [handleUndo, handleRedo]);

  // Initial Data Loading
  useEffect(() => {
    loadZonesAndTable();
  }, [loadZonesAndTable]);

  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (gridRef.current && !gridRef.current.contains(event.target as Node)) {
        setActiveCell(null);
        if (isReversePricing) {
          handleReciprocal();
        }
        setHighlight(t('zonePage.zoneLookUp.hoverOverACell'));
      }
    };

    document.addEventListener('mousedown', handleOutsideClick);
    return () => document.removeEventListener('mousedown', handleOutsideClick);
  }, [handleReciprocal, isReversePricing, t]);
  const renderGrid = () => (
    <>
      <div
        className="overflow-auto max-h-[600px] border"
        style={{
          borderRadius: '8px 8px 0 0',
          scrollSnapType: 'both mandatory',
        }}
        ref={gridRef}
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="grid"
        aria-label={t('zonePage.zoneLookUp.zoneLookupTableGrid')}
      >
        <div
          className="grid w-max"
          style={{
            gridTemplateColumns: `repeat(${zones.length + 1}, 140px)`,
            scrollSnapAlign: 'start', // Ensure cells snap
          }}
        >
          {/* Top-left corner */}
          <div className="sticky top-0 left-0 bg-[#E1F4FD] font-bold text-center border z-10 p-2">
            {t('zonePage.zoneLookUp.zones')}
          </div>

          {/* Column Headers */}
          {zones
            .sort((a, b) => a.name.localeCompare(b.name))
            .map((zone, colIndex) => (
              <div
                key={`col-header-${colIndex}`}
                className={`sticky top-0 bg-[#E1F4FD] font-bold text-center border p-2 break-words ${
                  activeCell?.col === colIndex ? 'bg-blue-100 font-extrabold' : ''
                }`}
                role="columnheader"
              >
                {zone.name}
              </div>
            ))}

          {/* Rows */}
          {zones
            .sort((a, b) => a.name.localeCompare(b.name))
            .map((rowZone, rowIndex) => (
              <React.Fragment key={`row-${rowIndex}`}>
                {/* Row Header */}
                <div
                  className={`sticky left-0 font-bold text-center border z-10 p-2 break-words ${
                    rowIndex % 2 === 0 ? 'bg-[#F0FAFF]' : 'bg-white'
                  }  ${activeCell?.row === rowIndex ? 'bg-blue-100 font-extrabold' : ''}`}
                  role="rowheader"
                >
                  {rowZone.name}
                </div>

                {/* Cells */}
                {zones.map((colZone, colIndex) => {
                  // Find the cell value for this specific fromId and toId
                  const cellValue = cellValues.find(
                    (cell) =>
                      cell.originZoneId === `${rowZone.id}` &&
                      cell.destinationZoneId === `${colZone.id}`
                  );

                  const isActive = activeCell?.row === rowIndex && activeCell?.col === colIndex;

                  return (
                    <div
                      key={`${rowIndex}-${colIndex}`}
                      className={`border p-2 text-center scroll-snap-align-start hover:bg-blue-100 cursor-pointer  
                ${
                  isActive
                    ? 'bg-blue-200'
                    : activeCell?.col === colIndex || activeCell?.row === rowIndex
                      ? 'bg-blue-50'
                      : 'bg-white'
                }
                `}
                      onClick={() => handleCellClick(rowIndex, colIndex)}
                      onMouseEnter={() =>
                        setHighlight(`From: ${rowZone.name} → To: ${colZone.name}`)
                      }
                      onMouseLeave={() => setHighlight(t('zonePage.zoneLookUp.hoverOverACell'))}
                      role="gridcell"
                      aria-selected={isActive}
                    >
                      {isActive ? (
                        <input
                          defaultValue={cellValue?.value}
                          inputMode="decimal"
                          pattern="[0-9]*[.,]?[0-9]*"
                          onInput={(e) => {
                            e.currentTarget.value = e.currentTarget.value.replace(/[^0-9.]/g, '');
                          }}
                          onChange={(e) =>
                            handleInputChange(`${rowZone.id}`, `${colZone.id}`, e.target.value)
                          }
                          className="w-full text-center border border-gray-300 rounded focus:outline-none focus:ring focus:ring-blue-300"
                          autoFocus
                          maxLength={10}
                          placeholder={t('zonePage.zoneLookUp.enterValue')}
                        />
                      ) : (
                        <span
                          className={`text-sm ${cellValue?.value ? 'text-black' : 'text-gray-400 italic'}`}
                        >
                          {cellValue?.value || 'N/A'}
                        </span>
                      )}
                    </div>
                  );
                })}
              </React.Fragment>
            ))}
        </div>
      </div>
      <div
        className="sticky top-0 border bg-[#E1F4FD] bg-white p-2"
        style={{ borderRadius: '0px 0px 8px 8px' }}
      >
        {highlight}
      </div>
    </>
  );

  const renderHelpModal = () => (
    <Modal
      maskProps={{ style: { backdropFilter: 'blur(2px)' } }}
      title={
        <Text className="text-xl font-semibold text-gray-800">
          {t('zonePage.zoneLookUp.howToUse')}
        </Text>
      }
      open={isHelpModalVisible}
      onCancel={() => setIsHelpModalVisible(false)}
      footer={
        <div className="flex justify-end">
          <Button
            type="primary"
            className="px-6 py-2 rounded-lg bg-primary-600 text-white font-medium hover:bg-primary-600"
            onClick={() => setIsHelpModalVisible(false)}
            aria-label="Close Help Modal"
          >
            {t('zonePage.ok')}, {t('zonePage.zoneLookUp.gotIt')}
          </Button>
        </div>
      }
      width={800}
    >
      <div className="space-y-8">
        {/* Section: Keyboard Navigation */}
        <div className="p-4 rounded-lg border border-primary-600 bg-[#F0FAFF] shadow-md">
          <div className="flex items-center mb-3">
            <KeyboardIcon className="text-2xl" aria-hidden="true" />
            <Text strong className="ml-3 text-lg text-gray-800">
              {t('zonePage.zoneLookUp.keyboardNavigation')}
            </Text>
          </div>
          <ul className="list-disc pl-6 space-y-2 text-gray-700">
            <li>
              <Text>
                <strong>{t('zonePage.zoneLookUp.click')}</strong>{' '}
                {t('zonePage.zoneLookUp.activateCell')}
              </Text>
            </li>
            <li>
              <Text>
                {t('zonePage.zoneLookUp.useThe')}
                <strong>{t('zonePage.zoneLookUp.arrowKeys')}</strong>
                {t('zonePage.zoneLookUp.whenCellIsActive')}
              </Text>
            </li>
            <li>
              <Text>
                {t('zonePage.zoneLookUp.press')} {t('zonePage.zoneLookUp.escape')}{' '}
                {t('zonePage.zoneLookUp.deactivateCell')}
              </Text>
            </li>
          </ul>
        </div>

        {/* Section: Tips */}
        <div className="p-4 rounded-lg border border-primary-600 bg-[#F0FAFF] shadow-md">
          <div className="flex items-center mb-3">
            <img src={InfoForPopupIcon} className="text-2xl" aria-hidden="true" />
            <Text strong className="ml-3 text-lg text-gray-800">
              {t('zonePage.zoneLookUp.tipsForUsage')}
            </Text>
          </div>
          <ul className="list-disc pl-6 space-y-2 text-gray-700">
            <li>
              <Text>
                {t('zonePage.zoneLookUp.hoverBefore')}{' '}
                <strong>{t('zonePage.zoneLookUp.hoverStrong')}</strong>{' '}
                {t('zonePage.zoneLookUp.hoverAfter')}
              </Text>
            </li>
            <li>
              <Text>
                {t('zonePage.zoneLookUp.applyReciprocalPricingBefore')}{' '}
                <strong>{t('zonePage.zoneLookUp.applyReciprocalPricingStrong')}</strong>{' '}
                {t('zonePage.zoneLookUp.applyReciprocalPricingAfter')}
              </Text>
            </li>
            <li>
              <Text>
                {t('zonePage.zoneLookUp.autoFillEmptyCellsBefore')}{' '}
                <strong>{t('zonePage.zoneLookUp.autoFillEmptyCellsStrong')}</strong>{' '}
                {t('zonePage.zoneLookUp.autoFillEmptyCellsAfter')}
              </Text>
            </li>
            <li>
              <Text>
                {t('zonePage.zoneLookUp.bulkAdjustGridValuesBefore')}{' '}
                <strong>{t('zonePage.zoneLookUp.bulkAdjustGridValuesStrong')}</strong>{' '}
                {t('zonePage.zoneLookUp.bulkAdjustGridValuesAfter')}
              </Text>
            </li>
          </ul>
        </div>

        {/* Section: Advanced Features */}
        <div className="p-4 rounded-lg border border-primary-600 bg-[#F0FAFF] shadow-md">
          <div className="flex items-center mb-3">
            <img src={FlashFeaturesIcon} />
            <Text strong className="ml-3 text-lg text-gray-800">
              {t('zonePage.zoneLookUp.advancedFeatures')}
            </Text>
          </div>
          <ul className="list-disc pl-6 space-y-2 text-gray-700">
            <li>
              <Text>
                <strong>{t('zonePage.zoneLookUp.keyboardShortcutsBefore')}</strong>{' '}
                {t('zonePage.zoneLookUp.useShortcuts')}
                <ul className="list-disc pl-6 text-sm">
                  <li>
                    <strong>{t('zonePage.zoneLookUp.ctrlZBefore')}</strong>
                    {t('zonePage.zoneLookUp.ctrlZAfter')}
                  </li>
                  <li>
                    <strong>{t('zonePage.zoneLookUp.ctrlYBefore')}</strong>
                    {t('zonePage.zoneLookUp.ctrlYAfter')}
                  </li>
                </ul>
              </Text>
            </li>
          </ul>
        </div>
      </div>
    </Modal>
  );

  const handleOnCloseForEmptyCells = () => {
    setFillEmptyCellsValue(0);
    fillEmptyCellsForm.resetFields();
    setIsFillEmptyCellsModalVisible(false);
  };
  const [fillEmptyCellsForm] = Form.useForm();
  const renderFillEmptyCellsModal = () => (
    <CustomModal
      className="!w-[450px] !h-[80%] flex items-center"
      modalTitle={t('zonePage.zoneLookUp.autoFillEmptyCells')}
      modalDescription={t('zonePage.zoneLookUp.fillEmptyCellsWithAmount')}
      maskClosable={false}
      footer={
        <div className="flex gap-2 w-full justify-end">
          <Button
            onClick={handleOnCloseForEmptyCells}
            className="hover:!text-black hover:!border-gray-300"
          >
            {t('common.cancel')}
          </Button>
          <Button
            className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
            onClick={handleFillEmptyCells}
            disabled={!fillEmptyCellsValue}
          >
            {t('zonePage.zoneLookUp.submit')}
          </Button>
        </div>
      }
      open={isFillEmptyCellsModalVisible}
      onCancel={handleOnCloseForEmptyCells}
    >
      <div className="space-y-2 p-y-3">
        <div className="flex gap-1">
          <Text className="text-black font-[600]">{t('zonePage.zoneLookUp.amount')}</Text>
          <Text className="text-gray-500">(Example 8.75)</Text>
        </div>
        <div>
          <Form form={fillEmptyCellsForm}>
            <Form.Item
              name="emptyCellsFillUp"
              rules={[
                {
                  validator: (_, value) => {
                    if (value > 100000) {
                      return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <InputNumber
                value={fillEmptyCellsValue}
                type="number"
                onChange={(value) => setFillEmptyCellsValue(value as number)}
                min={0}
                step={0.01}
                className="w-full border b-primary-600"
                prefix="$"
                placeholder={t('zonePage.zoneLookUp.enterValue')}
                inputMode="decimal"
                pattern="[0-9]*[.,]?[0-9]*"
                defaultValue={0.0}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
    </CustomModal>
  );

  const handleOnCloseForAdjustAllCells = () => {
    setIsAdjustAllCellsModalVisible(false);
    setAdjustAllCellsValue(0);
    adjustAllValuesForm.resetFields();
  };
  const [adjustAllValuesForm] = Form.useForm();
  const renderAdjustAllCellsModal = () => (
    <CustomModal
      modalTitle={t('zonePage.zoneLookUp.bulkAdjustModalTitle')}
      modalDescription={t('zonePage.zoneLookUp.adjustPopulatedCellsByAmount')}
      open={isAdjustAllCellsModalVisible}
      onCancel={handleOnCloseForAdjustAllCells}
      maskClosable={false}
      className="!w-[450px] !h-[80%] flex items-center"
      footer={
        <div className="flex gap-2 w-full justify-end">
          <Button
            onClick={handleOnCloseForAdjustAllCells}
            className="hover:!text-black hover:!border-gray-300"
          >
            {t('common.cancel')}
          </Button>
          <Button
            className="bg-primary-600 hover:!bg-primary-600 text-white hover:!text-white"
            onClick={handleAdjustAllCells}
            disabled={!adjustAllCellsValue}
          >
            {t('zonePage.zoneLookUp.submit')}
          </Button>
        </div>
      }
      width={450} // Set modal width
    >
      <div className="space-y-6">
        <div>
          <Text className="block mb-2 text-sm font-medium text-gray-700">
            {t('zonePage.zoneLookUp.adjustmentAmountLabel')}
          </Text>
          <div className="flex justify-between items-center py-2 w-full">
            <div className="fixed-radio flex gap-2 items-center bg-[#F0FAFF] w-[46%] rounded-lg p-2">
              <input
                className=""
                type="radio"
                checked={adjustAllCellsType === 'fixed'}
                onChange={() => {
                  setAdjustAllCellsType('fixed');
                  adjustAllValuesForm.resetFields();
                }}
              />
              <label className="mr-2 font-semibold text-gray-700">
                {t('zonePage.zoneLookUp.treatAsFixedLabel')}
              </label>
            </div>
            <div className="percentage-radio flex gap-2 items-center bg-[#F0FAFF] w-[50%] rounded-lg p-2">
              <input
                type="radio"
                checked={adjustAllCellsType === 'percentage'}
                onChange={() => {
                  setAdjustAllCellsType('percentage');
                  adjustAllValuesForm.resetFields();
                }}
              />
              <label className="font-semibold text-gray-700">
                {t('zonePage.zoneLookUp.treatAsPercentageLabel')}
              </label>
            </div>
          </div>

          <div className="flex gap-1 px-1 py-1">
            <Text className="text-black font-[600]">{t('zonePage.zoneLookUp.amount')}</Text>
            <Text className="text-gray-500">(Example 8.75)</Text>
          </div>

          <Form form={adjustAllValuesForm}>
            <Form.Item
              rules={[
                {
                  validator: (_, value) => {
                    if (adjustAllCellsType === 'fixed') {
                      if (value > 100000) {
                        return Promise.reject(new Error(t('priceModifiers.maximumValueExceeded')));
                      }
                    } else {
                      if (value > 100) {
                        return Promise.reject(
                          new Error(t('priceModifiers.maximumPercentExceeded'))
                        );
                      }
                    }

                    return Promise.resolve();
                  },
                },
              ]}
              name="adjustAllValues"
            >
              <InputNumber
                inputMode="decimal"
                pattern="[0-9]*[.,]?[0-9]*"
                type="number"
                addonBefore={adjustAllCellsType === 'fixed' ? '$' : '%'}
                name="Amount"
                value={adjustAllCellsValue}
                onChange={(value) => setAdjustAllCellsValue(value ?? 0)}
                className="bulk-adjust-input w-full p-1"
                placeholder="0.00"
                parser={(value) => {
                  if (!value) {
                    return 0;
                  }
                  const parsedValue = parseFloat(value.replace(/\$\s?|(,*)/g, ''));
                  return isNaN(parsedValue) ? 0 : parsedValue;
                }}
                min={0}
                step={0.01}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
    </CustomModal>
  );

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Page Header */}
        <div className="flex w-full flex-col md:flex-row items-center justify-between h-fit p-4 ">
          <div className="flex items-center">
            <PageHeadingComponent
              title={
                isEditMode
                  ? `${t('zonePage.zoneLookUp.editZoneLookupTable')}`
                  : `${t('zonePage.zoneLookUp.createZoneLookupTable')}`
              }
              isChildComponent
            />
          </div>
        </div>

        {/* Content */}
        {zones?.length > 0 ? (
          <div className="p-4 flex-1 overflow-auto ">
            <div className="flex items-center space-x-4 justify-between w-full">
              <Form
                name="zoneLookupForm"
                form={form}
                layout="vertical"
                className="flex w-full justify-between flex-wrap"
              >
                <Form.Item
                  required
                  name="name"
                  label={t('zonePage.colDefs.name')}
                  className="lookup-table-name"
                  rules={[
                    {
                      pattern: formErrorRegex.NoMultipleWhiteSpaces,
                      message: t('common.errors.noMultipleWhiteSpace'),
                    },
                  ]}
                >
                  <Input
                    placeholder={t('zonePage.zoneLookUp.enterUniqueName')}
                    className="w-80 rounded p-2"
                    value={tableName}
                    onChange={(e) => setTableName(e.target.value)}
                    required
                    maxLength={255}
                  />
                </Form.Item>
                <div className="flex space-x-4 align-bottom items-end">
                  <Form.Item>
                    <Button
                      onClick={() => setIsHelpModalVisible(true)}
                      className="p-5 hover:!text-black hover:!border-gray-300"
                    >
                      <img className="w-[16px] h-[16px]" src={infoCircleOutlined} />
                      {t('zonePage.zoneLookUp.howToUseZoneTable')}
                    </Button>
                  </Form.Item>

                  <Form.Item name="isReversePricing">
                    <Button
                      onClick={() => setIsReversePricing(!isReversePricing)}
                      className="p-5 hover:!text-black hover:!border-gray-300"
                    >
                      <Checkbox checked={isReversePricing} />
                      {t('zonePage.zoneLookUp.reversePricing')}
                    </Button>
                  </Form.Item>

                  <Form.Item>
                    <Button
                      className="p-5 hover:!text-black hover:!border-gray-300"
                      icon={<EditOutlined />}
                      onClick={() => setIsFillEmptyCellsModalVisible(true)}
                    >
                      {t('zonePage.zoneLookUp.autoFillEmptyCellsButton')}
                    </Button>
                  </Form.Item>

                  <Form.Item>
                    <Button
                      className="p-5 hover:!text-black hover:!border-gray-300"
                      onClick={() => setIsAdjustAllCellsModalVisible(true)}
                    >
                      <img src={ArrowSvgIcon} />
                      {t('zonePage.zoneLookUp.bulkAdjustGridValue')}
                    </Button>
                  </Form.Item>
                </div>
              </Form>
            </div>

            {renderGrid()}
            <div className="mt-4 flex space-x-2">
              <Tooltip
                title={
                  loading || !tableName.trim() || cellValues.length === 0
                    ? t('zonePage.zoneLookUp.enterZoneTableNameAndFillCell')
                    : ''
                }
              >
                <Button
                  type="primary"
                  onClick={onSubmit}
                  disabled={!tableName.trim() || cellValues.length === 0}
                  className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                >
                  {isEditMode ? `${t('common.update')}` : `${t('common.save')}`}
                </Button>
              </Tooltip>
              <Button
                disabled={isChanged}
                className="min-w-[155px] h-[40px] border-[1px] rounded-[8px] max-w-fit"
                onClick={handleDiscardChanges}
              >
                {t('zonePage.zoneLookUp.discardChanges')}
              </Button>
            </div>
          </div>
        ) : (
          <EmptyStatePage
            title={t('zonePage.zoneLookUp.currentlyNoZonesAdded')}
            description={t('zonePage.zoneLookUp.addZonesToCreateLookupTable')}
            link={t('zonePage.zoneLookUp.addZones')}
            onLinkAction={() => navigate('/location/zone', { replace: true })}
          />
        )}
      </div>

      {/* Modals */}
      {renderHelpModal()}
      {renderFillEmptyCellsModal()}
      {renderAdjustAllCellsModal()}
    </div>
  );
};

export default AddZoneLookupTablePage;
