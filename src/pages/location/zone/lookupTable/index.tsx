import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import CustomAgGrid from '@/components/common/agGrid/AgGrid';
import PageHeadingComponent from '@/components/specific/pageHeading/PageHeadingComponent';
import { But<PERSON>, Divider } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { IZone, IZoneLookupTable } from '../list/zone.type';
import { GridNames } from '@/types/AppEvents.ts';
import { IColDef, IExtendedSortChangedEvent } from '@/types/AgGridTypes.ts';
import { useZoneLookupTableColDefs } from '@pages/location/zone/lookupTable/useZoneLookupTableColDefs.tsx';
import { customAlert } from '@components/common/customAlert/CustomAlert.tsx';
import { on } from '@contexts/PulseContext.tsx';
import { AgGridReact } from 'ag-grid-react';
import SearchFilterComponent from '@components/specific/searchFilter/SearchFilterComponent.tsx';
import { AssignToIcon, deleteSvg, DuplicateCustomerIcon, PlusButtonIcon } from '@/assets';
import { filterData } from '@pages/location/zone/zoneApi.ts';
import { EmptyStatePage } from '@/components/common/emptyState/EmptyStatePage';
import { IContextMenuItems } from '@/types/ContextMenuTypes';
import { useLanguage } from '@/hooks/useLanguage';
import { zoneTableHook } from '@/api/zoneTable/useZoneTable';
import { ZoneTable } from '@/api/zoneTable/zoneTable.types';
import { defaultPagination } from '@/constant/generalConstant';
import { getPaginationData } from '@/lib/helper';
import { onSortChangeHandler } from '@/lib/helper/agGridHelper';

const Index = () => {
  const gridRef = useRef<AgGridReact<IZone>>(null);
  const navigate = useNavigate();
  const [zoneTables, setZonesTables] = useState<ZoneTable[]>([]);
  const [zoneTablesFilter, setTablesZoneFilter] = useState<ZoneTable[]>([]);
  const [searchText, setSearchText] = useState('');
  const [cellValues, setCellValues] = useState<IZoneLookupTable>();
  const { t } = useLanguage();
  const notificationManager = useNotificationManager();
  const [filterParams, setFilterParams] = useState(defaultPagination);

  const {
    data: zoneTableList,
    refetch: refetchZoneTableList,
    isLoading,
    isFetching,
  } = zoneTableHook.useList(filterParams);

  const duplicateMutation = zoneTableHook.useDuplicate({
    onSuccess: async () => {
      refetchZoneTableList();
    },
  });
  useEffect(() => {
    if (zoneTableList) {
      setZonesTables(zoneTableList?.data);
      setTablesZoneFilter(zoneTableList?.data);
    }
  }, [zoneTableList]);

  const deleteMutation = zoneTableHook.useDelete({
    onSuccess: async () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('zonePage.zoneLookUp.zoneTableDeletedSuccessfully'),
      });
      await refetchZoneTableList();
    },
  });

  const zoneColDefs: IColDef[] = useZoneLookupTableColDefs({
    searchText,
    onEditZoneTable: (zone: IZoneLookupTable) => {
      navigate(`/location/lookup-table/edit/${zone.id}`);
    },
    onDeleteZoneTable: (data: IZoneLookupTable) => {
      deleteZoneTableConfirmation(data);
    },
  });

  const deleteZoneTableConfirmation = useCallback(
    (data: IZoneLookupTable) => {
      customAlert.error({
        title: t('zonePage.zoneLookUp.confirmDeleteZoneTable', { this: data?.name }),
        message: `${t('priceModifiers.deleteModifierWarning')}`.replace(
          'price modifier',
          'zone table'
        ),
        firstButtonFunction: async () => {
          customAlert.destroy();
          await deleteMutation.mutateAsync(data.id as string);
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
        firstButtonTitle: t('common.delete'),
        secondButtonTitle: t('common.cancel'),
      });
    },
    [deleteMutation, t]
  );

  on('columnManager:changed', (data) => {
    if (data.gridName === GridNames.zoneLookupGrid && gridRef.current?.api) {
      const columnOrder = data.gridState.map((column: { id: string }) => column.id);
      gridRef.current.api.moveColumns(columnOrder, 0);
    }
  });

  const searchHandler = useCallback((value: string) => {
    setSearchText(value);
    setFilterParams((prev) => ({
      ...prev,
      pageNumber: value ? 1 : prev.pageNumber,
      searchTerm: value || undefined,
    }));
  }, []);

  const applyFilters = useCallback(
    (data: any) => {
      const filteredResults = filterData(zoneTablesFilter, data.filters);
      setZonesTables(filteredResults);
    },
    [zoneTablesFilter]
  );

  const duplicateZoneTableConfirmation = useCallback(
    (data: IZoneLookupTable) =>
      customAlert.warning({
        title: t('zonePage.zoneLookUp.confirmDuplicateZoneTable'),
        message: t('customerAddressPage.notifications.confirmDuplicateMessage'),
        firstButtonTitle: t('common.duplicate'),
        secondButtonTitle: t('common.cancel'),
        firstButtonFunction: async () => {
          duplicateMutation.mutate(data.id as string);
          customAlert.destroy();
        },
        secondButtonFunction: () => {
          customAlert.destroy();
        },
      }),
    [duplicateMutation, t]
  );
  const paginationData = useMemo(() => getPaginationData(zoneTableList), [zoneTableList]);

  const zoneTableContextMenuItems: IContextMenuItems[] = useMemo(
    () => [
      {
        label: t('zonePage.zoneLookUp.newZoneTable'),
        key: 'Open',
        icon: AssignToIcon as React.ElementType,
        onClick: () => {
          navigate('/location/lookup-table/add');
        },
      },
      {
        label: t('zonePage.zoneLookUp.duplicateZoneTable'),
        icon: DuplicateCustomerIcon as React.ElementType,
        key: 'duplicateZoneTable',
        onClick: () => duplicateZoneTableConfirmation(cellValues as IZoneLookupTable),
      },
      {
        label: <span className="text-red-600">{t('common.delete')}</span>,
        icon: (<img src={deleteSvg} />) as unknown as React.ElementType,
        onClick: () => deleteZoneTableConfirmation(cellValues as IZoneLookupTable),
        key: 'delete',
      },
    ],
    [cellValues, deleteZoneTableConfirmation, duplicateZoneTableConfirmation, navigate, t]
  );

  return (
    <div className="flex h-screen">
      <div className="flex-1 flex flex-col overflow-hidden bg-white">
        <div className="flex w-full 3xsm:flex-col md:flex-row h-fit">
          <div className="md:w-1/3 flex flex-col 3xsm:w-full">
            <PageHeadingComponent title="Zone Tables" />
          </div>
          <div className="flex 3xsm:text-center md:flex-row justify-end w-2/3 md:gap-4 pe-[25px] lg:pe-[30px] 3xsm:w-full 3xsm:flex-col 3xsm:gap-0">
            <div className="flex gap-3">
              <SearchFilterComponent
                onSearch={searchHandler}
                colDefs={zoneColDefs}
                isSetQuickFilter={false}
                advanceFilter={false}
                searchInputPlaceholder="Search zone table"
                onFilterApply={applyFilters}
                setFilterParams={setFilterParams}
              />
            </div>
            <div className="pt-5">
              <Divider type="vertical" className="3xsm:hidden md:block h-[40px] !m-0" />
            </div>
            <div className="3xsm:pt-0 md:pt-5 flex items-center gap-3">
              <Button
                className="w-[155px] h-[40px] border-[1px] rounded-[8px] bg-primary-600 text-white font-[500] hover:!bg-primary-600 hover:!text-white"
                icon={<PlusButtonIcon />}
                onClick={() => navigate('/location/lookup-table/add')}
              >
                {t('zonePage.zoneLookUp.addZoneTable')}
              </Button>
            </div>
          </div>
        </div>

        {zoneTables?.length > 0 ? (
          <main className="h-screen overflow-x-hidden overflow-y-auto bg-white">
            <div className="mx-auto pr-6 py-5 h-full flex justify-center items-center">
              <CustomAgGrid
                gridRef={gridRef}
                loading={isLoading || isFetching}
                rowData={zoneTables}
                columnDefs={zoneColDefs}
                gridName={GridNames.zoneLookupGrid}
                isContextMenu
                onContextMenu={(params) => {
                  setCellValues(params.data);
                }}
                paginationProps={{
                  ...paginationData,
                  onPaginationChange(page, pageLimit) {
                    setFilterParams((prev) => ({
                      ...prev,
                      pageNumber: page,
                      pageSize: pageLimit,
                    }));
                  },
                }}
                onSortChanged={(params: IExtendedSortChangedEvent) =>
                  setFilterParams(onSortChangeHandler(params, filterParams) as typeof filterParams)
                }
                contextMenuItem={zoneTableContextMenuItems}
              />
            </div>
          </main>
        ) : (
          <EmptyStatePage
            title={searchText ? t('common.noMatchesFound') : t('zonePage.zoneLookUp.noZoneTables')}
            description={searchText ? '' : t('zonePage.zoneLookUp.noZoneTablesFound')}
            link={searchText ? '' : t('zonePage.zoneLookUp.addTable')}
            onLinkAction={() => navigate('/location/lookup-table/add')}
          />
        )}
      </div>
    </div>
  );
};

export default Index;
