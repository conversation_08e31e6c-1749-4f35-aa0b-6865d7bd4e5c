import { useCallback, useMemo } from 'react';
import { IColDef } from '@/types/AgGridTypes.ts';
import { ICellRendererParams } from 'ag-grid-community';
import { IZoneLookupTable, ZoneTableProps } from '@pages/location/zone/list/zone.type.ts';
import { DeleteIcon, EyeIcon } from '@/assets';
import Icon from '@ant-design/icons';
import { useLanguage } from '@/hooks/useLanguage';

function escapeRegex(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function useHighlightText() {
  return useCallback(
    (value: string | string[] | null | undefined, search: string): string | JSX.Element => {
      const text = Array.isArray(value) ? value.join(', ') : (value ?? '');

      if (!search || !text) return text;

      const escapedSearch = escapeRegex(search);
      const regex = new RegExp(`(${escapedSearch})`, 'gi');
      const parts = text.split(regex);

      return (
        <span
          dangerouslySetInnerHTML={{
            __html: parts
              .map((part) =>
                regex.test(part) ? `<span style="background-color: yellow;">${part}</span>` : part
              )
              .join(''),
          }}
        />
      );
    },
    []
  );
}

export function useZoneLookupTableColDefs({
  searchText,
  onEditZoneTable,
  onDeleteZoneTable,
}: ZoneTableProps) {
  const { t } = useLanguage();

  const highlightText = useHighlightText();
  const zoneLookUpTableColDefs: IColDef[] = useMemo(
    () => [
      {
        field: 'name',
        headerName: t('addressPage.colDefs.name'),
        unSortIcon: true,
        type: 'string',
        flex: 1,
        visible: true,
        cellRenderer: (params: { value: string }) => {
          return searchText ? highlightText(params.value, searchText) : params.value;
        },
      },
      {
        field: 'action',
        headerName: t('dashboard.customer.columns.action'),
        pinned: 'right',
        width: 80,
        visible: true,
        sortable: false,
        resizable: false,
        cellRenderer: (params: ICellRendererParams<IZoneLookupTable>) => (
          <div className="flex gap-2 h-full items-center">
            <Icon
              component={EyeIcon}
              className="cursor-pointer"
              alt="view/edit"
              onClick={() => {
                if (onEditZoneTable && params.data) {
                  onEditZoneTable(params.data);
                }
              }}
            />
            <Icon
              component={DeleteIcon}
              className="cursor-pointer"
              alt="delete"
              onClick={() => {
                if (onDeleteZoneTable && params.data?.id) {
                  onDeleteZoneTable(params.data);
                }
              }}
            />
          </div>
        ),
      },
    ],
    [searchText, highlightText, onEditZoneTable, onDeleteZoneTable]
  );

  return zoneLookUpTableColDefs;
}
