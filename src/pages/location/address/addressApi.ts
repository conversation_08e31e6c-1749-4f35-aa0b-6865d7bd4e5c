type FilterOperator =
  | 'contains'
  | 'notContains'
  | 'startsWith'
  | 'endsWith'
  | 'equals'
  | 'notEquals'
  | 'greaterThan'
  | 'lessThan';

interface SearchCondition {
  query: string;
}

export interface FilterCondition {
  field: string;
  operator: FilterOperator;
  value: string;
}

/**
 * Filter the provided data based on the given filter conditions
 * @param data The dataset to filter
 * @param filters List of filter conditions to apply
 * @returns Filtered array of objects based on provided filters
 */
export function filterData(data: any[], filters: FilterCondition[] = []): any[] {
  return data.filter((item) => {
    return filters.every((filter) => {
      const fieldValue = item[filter.field];
      if (fieldValue === undefined || fieldValue === null) {
        return false;
      }

      const valueToCompare = filter.value.toLowerCase();
      const itemValue = String(fieldValue).toLowerCase();

      switch (filter.operator || 'equals') {
        case 'contains':
          return itemValue.includes(valueToCompare);
        case 'notContains':
          return !itemValue.includes(valueToCompare);
        case 'startsWith':
          return itemValue.startsWith(valueToCompare);
        case 'endsWith':
          return itemValue.endsWith(valueToCompare);
        case 'equals':
          return itemValue === valueToCompare;
        case 'notEquals':
          return itemValue !== valueToCompare;
        case 'greaterThan':
          return Number(itemValue) > Number(valueToCompare);
        case 'lessThan':
          return Number(itemValue) < Number(valueToCompare);
        default:
          return false;
      }
    });
  });
}

/**
 * Function to search across provided fields using given search logic
 * @param data Array of objects to search
 * @param condition Search condition with query string and operator
 * @returns Filtered data matching the search criteria
 */
export function searchData(data: any[], condition: SearchCondition): any[] {
  const { query } = condition;

  return data.filter((item) => {
    // Normalize search string to lowercase for case-insensitive matching
    const normalizedQuery = query.toLowerCase();

    // Check all relevant fields for match
    return (
      item.addressLine1?.toLowerCase().includes(normalizedQuery) ||
      item.addressLine2?.toLowerCase().includes(normalizedQuery) ||
      item.city?.toLowerCase().includes(normalizedQuery) ||
      item.comment?.toLowerCase().includes(normalizedQuery) ||
      item.companyName?.toLowerCase().includes(normalizedQuery) ||
      item.contact?.toLowerCase().includes(normalizedQuery) ||
      item.email?.toLowerCase().includes(normalizedQuery) ||
      item.name?.toLowerCase().includes(normalizedQuery)
    );
  });
}
