import { CreateAddressDto } from '@/api/address/address.types';
import { IIsOpenModal } from '@/types/CommonTypes';
import { FormInstance } from 'antd';

export interface IAddress {
  id?: string;
  customer: string;
  name: string;
  contact: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  province: string;
  postalCode: string;
  zone: string;
  email: string;
  phone: string;
  phoneExtension: string;
  companyName: string;
  country: string;
  comment: string;
  dateUpdated: string;
  lastUpdateBy: string;
}

export interface IAddressOperationForm extends CreateAddressDto {
  zone: string;
  notFoundZoneFor?: string;
}

export interface IAddressOperationFormProps {
  form: FormInstance<IAddressOperationForm>;
  onFinish: (formValues: IAddressOperationForm) => Promise<void>;
  open: IIsOpenModal;
}
