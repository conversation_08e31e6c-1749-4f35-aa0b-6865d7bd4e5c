import { NotificationPlacement } from 'antd/es/notification/interface';

export interface INotificationConfig {
  duration: number;
  placement: NotificationPlacement;
  pauseOnHover: boolean;
  className: string;
}

export interface INotificationOptions {
  message: string;
  description: string;
  duration?: number;
  placement?: NotificationPlacement;
}

export type TNotificationType = 'success' | 'error' | 'info' | 'warning';
