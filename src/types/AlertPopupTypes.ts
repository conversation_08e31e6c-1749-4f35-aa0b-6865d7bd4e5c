export interface IAlertPopupConfig {
  icon?: React.ReactNode;
  title?: string;
  message?: string;
  firstButtonFunction: () => void;
  secondButtonFunction: () => void;
  firstButtonTitle?: string;
  secondButtonTitle?: string;
  firstButtonClass?: string;
  secondButtonClass?: string;
  firstButtonLoading?: boolean;
  secondButtonLoading?: boolean;
}

export type IAlertType = 'success' | 'error' | 'warning';
