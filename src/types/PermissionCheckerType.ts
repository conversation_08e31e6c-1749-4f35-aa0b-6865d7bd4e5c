import { Action } from './enums/Plans';

/**
 * Base props shared across all permission scenarios.
 */
type TWrapperType = 'popover' | 'display';

type PermissionCheckerBaseProps = {
  children: React.ReactElement;
  type?: TWrapperType;
};

/**
 * Props for permission checks using `resource` and `action`.
 * Ensures `feature` is not allowed in this case.
 */
type PermissionCheckerPropsWithResourceAndAction = PermissionCheckerBaseProps & {
  resource: string;
  action: Action;
  feature?: never;
};

/**
 * Props for permission checks using `feature` only.
 * Ensures `resource` and `action` are not allowed in this case.
 */
type PermissionCheckerPropsWithFeature = PermissionCheckerBaseProps & {
  feature: string;
  resource?: never;
  action?: never;
};

/**
 * Union type for `PermissionChecker` props.
 * Ensures mutually exclusive permission scenarios.
 */
export type IPermissionCheckerProps =
  | PermissionCheckerPropsWithResourceAndAction
  | PermissionCheckerPropsWithFeature;
