import { errorCodes } from '@/lib/errorHandling/ErrorCodes';
import { AxiosRequestConfig } from 'axios';
export interface IAxiosDefaultConfig {
  timeout: number;
  retries: number;
  retryDelay: number;
  maxRefreshAttempt: number;
}

/**
 * Extended configuration interface for AxiosClient.
 * Includes additional properties for retry behavior and refresh token.
 */
export interface ExtendedAxiosConfig extends AxiosRequestConfig {
  timeout?: number;
  /** Number of retry attempts */
  retries?: number;
  /** Delay between retries in milliseconds */
  retryDelay?: number;
  /** Function to get the access token */
  getAccessToken?: () => string;
  /** Function to refresh the token */
  refreshToken?: () => Promise<string>;
}

export interface TrackedError {
  statusCode?: number;
  timestamp: Date;
  code: keyof typeof errorCodes | string;
  message: string;
  details?: Record<string, any>;
}
