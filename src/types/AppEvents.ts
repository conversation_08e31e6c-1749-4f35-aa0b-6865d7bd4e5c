import { CreateSettingsDto } from '@/api/settings/settings.types';
import { IQuickFilterPayload } from '@/components/specific/quickFilter/quickFilterTypes';
import { IAssignedFilters } from '@/pages/logistics/orders/orders.types';
import { QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import { FormInstance } from 'antd';

export enum GridNames {
  'customerGrid' = 'customerGrid',
  'customerAddressGrid' = 'customerAddressGrid',
  'orderGrid' = 'orderGrid',
  'addressGrid' = 'addressGrid',
  'contactGrid' = 'contactGrid',
  'customerService' = 'customerService',
  'dispatcherGrid' = 'dispatcherGrid',
  'zoneGrid' = 'zoneGrid',
  'zoneLookupGrid' = 'zoneLookupGrid',
  'priceModifiersGrid' = 'priceModifiersGrid',
  'vehicleGrid' = 'vehicleGrid',
  'timeClockSessionsGrid' = 'timeClockSessionsGrid',
  'customerBillingGrid' = 'customerBillingGrid',
  'customerPartnerGrid' = 'customerPartnerGrid',
  'templatesGrid' = 'templatesGrid',
  'generalGrid' = 'generalGrid',
  'partnersGrid' = 'partnersGrid',
  'priceSetsGrid' = 'priceSetsGrid',
  'routesGrid' = 'routesGrid',
  'packageTypeGrid' = 'packageTypeGrid',
  'invoicesGrid' = 'invoicesGrid',
  'paymentGrid' = 'paymentGrid',
}

export interface AppEvents {
  'columnManager:changed': {
    gridName: GridNames;
    gridState: any;
  };
  'columnManager:ready': {
    gridName: GridNames;
  };
  'breadCrumb:changed': { breadCrumbs: string };
  'idExistsOrNot:boolean': { status: boolean };
  OrderQuickFilter: IQuickFilterEvent;
  OrderQuickFilterTitleEvent: IQuickFilterTitleEvent;
}

export interface IQuickFilterEvent {
  form: FormInstance;
  onFilterApply: (values: { filters: IAssignedFilters[] }) => void;
  quickFilter: CreateSettingsDto<IQuickFilterPayload>;
  refetchFilters: (options?: RefetchOptions) => Promise<QueryObserverResult<any, Error>>;
  handleClearAll: () => void;
}

export interface IQuickFilterTitleEvent {
  title: string | undefined;
}
