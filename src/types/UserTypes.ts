import { Action, Plans, UPlans } from './enums/Plans';
import { URoles } from './enums/Roles';

export type IRoleBasedActionsChecker = Record<URoles, Record<string, Action[]>>;

export type IPlanBasedFeatureChecker = {
  [key in UPlans]: Set<string>;
};

export interface IAccessRights {
  roleBasedActions: IRoleBasedActionsChecker;
  planBasedFeature: IPlanBasedFeatureChecker;
}

export interface IUser {
  id: string;
  companyName: string;
  email: string;
  password: string;
  role: URoles;
  isAuthenticated?: boolean;
  plan: Plans;
  accessRight: IAccessRights;
  accessToken: string;
}
