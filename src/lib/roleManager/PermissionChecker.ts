import { IPermission<PERSON>hecker, Action } from '@customTypes/enums/Plans';
import { Roles } from '@/types/enums/Roles';
import { IUser } from '@customTypes/UserTypes';

/**
 * Checks permissions based on user roles and resource.
 *
 * @implements {<PERSON>er<PERSON><PERSON>he<PERSON>}
 */
export class RoleBasedPer<PERSON><PERSON>he<PERSON> implements IPermissionChecker {
  private readonly rolePermissions: Record<Roles, Record<string, Action[]>>;

  /**
   * @param {Record<Roles, Record<string, Action[]>>} rolePermissions -
   * Maps roles to resources and their allowed actions.
   *
   * @example
   * const roleBasedActions = {
   *   Tenant: {
   *     customer: ['create', 'read', 'update', 'delete'],
   *     deliveries: ['create', 'read', 'update', 'delete'],
   *   },
   *   Dispatcher: {
   *     customer: ['read', 'update', 'delete'],
   *     deliveries: ['create', 'read', 'update'],
   *   },
   * };
   *
   * const checker = new RoleBasedPermissionChecker(roleBasedActions);
   */
  constructor(rolePermissions: Record<Roles, Record<string, Action[]>>) {
    this.rolePermissions = rolePermissions;
  }

  /**
   * Determines if a user has permission for a specific action on a resource.
   *
   * @param {IUser} user - The user to check.
   * @param {string} resource - The resource to verify.
   * @param {Action} action - The action to validate.
   * @returns {boolean} - `true` if the user has permission, otherwise `false`.
   */
  public hasPermission(user: IUser, resource: string, action: Action): boolean {
    return this.rolePermissions[user.role][resource].includes(action);
  }
}
