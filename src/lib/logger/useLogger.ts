import { useCallback } from 'react';
import { logger } from '@lib/logger/logger.ts';

/**
 * React hook for using the logger within components
 * @param module - Module name for the logger
 */
export const useLogger = (module: string) => {
  const debug = useCallback(
    (message: string, data?: Record<string, unknown>) => {
      logger.debug(message, module, data);
    },
    [module]
  );

  const info = useCallback(
    (message: string, data?: Record<string, unknown>) => {
      logger.info(message, module, data);
    },
    [module]
  );

  const warn = useCallback(
    (message: string, data?: Record<string, unknown>) => {
      logger.warn(message, module, data);
    },
    [module]
  );

  const error = useCallback(
    (message: string, error: Error, data?: Record<string, unknown>) => {
      logger.error(message, error, module, data);
    },
    [module]
  );

  return {
    debug,
    info,
    warn,
    error,
  };
};
