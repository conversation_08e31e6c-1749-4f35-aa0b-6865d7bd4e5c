import { LogEntry, LoggerConfig, LogLevel } from '@lib/logger/types.ts';

/**
 * Production-grade logger for React applications
 * Provides type-safe logging with configurable levels, modules, and data structures
 */
class Logger {
  private static instance: Logger;
  private config: LoggerConfig = {
    minLevel: LogLevel.INFO,
    enabled: true,
    preserveConsole: false,
  };

  private constructor() {
    if (this.config.preserveConsole) {
      this.preserveConsoleLog();
    }
  }

  /**
   * Get the singleton instance of the logger
   */
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Configure the logger
   * @param config - Logger configuration options
   */
  public configure(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Create a log entry
   * @param level - Log level
   * @param message - Log message
   * @param module - Optional module name
   * @param data - Optional additional data
   * @param error - Optional error object
   */
  private createLogEntry(
    level: LogLevel,
    message: string,
    module?: string,
    data?: Record<string, unknown>,
    error?: Error
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      module,
      data,
      error,
    };
  }

  /**
   * Check if the log level is enabled
   * @param level - Log level to check
   */
  private isLevelEnabled(level: LogLevel): boolean {
    if (!this.config.enabled) return false;
    const levels = Object.values(LogLevel);
    return levels.indexOf(level) >= levels.indexOf(this.config.minLevel);
  }

  /**
   * Preserve original console methods
   */
  private preserveConsoleLog(): void {
    const originalConsole = { ...console };
    Object.keys(originalConsole).forEach((key) => {
      // @ts-expect-error Right now i dont want to fix this
      if (typeof originalConsole[key] === 'function') {
        // @ts-expect-error Right now i dont want to fix this
        console[key] = (...args: unknown[]) => {
          // @ts-expect-error Right now i dont want to fix this
          originalConsole[key](...args);
        };
      }
    });
  }

  /**
   * Format and output the log entry
   * @param entry - Log entry to format and output
   */
  private output(entry: LogEntry): void {
    const { level, message, module, data, error } = entry;
    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const modulePrefix = module ? `[${module}] ` : '';

    const style = {
      DEBUG: 'color: #6c757d',
      INFO: 'color: #0d6efd',
      WARN: 'color: #ffc107',
      ERROR: 'color: #dc3545',
    };

    console.group(`%c${timestamp} ${level} ${modulePrefix}${message}`, style[level]);

    if (data) {
      console.log('Data:', data);
    }

    if (error) {
      console.error('Error:', error);
    }

    console.groupEnd();
  }

  /**
   * Log a debug message
   * @param message - Debug message
   * @param module - Optional module name
   * @param data - Optional additional data
   */
  public debug(message: string, module?: string, data?: Record<string, unknown>): void {
    if (!this.isLevelEnabled(LogLevel.DEBUG)) return;
    this.output(this.createLogEntry(LogLevel.DEBUG, message, module, data));
  }

  /**
   * Log an info message
   * @param message - Info message
   * @param module - Optional module name
   * @param data - Optional additional data
   */
  public info(message: string, module?: string, data?: Record<string, unknown>): void {
    if (!this.isLevelEnabled(LogLevel.INFO)) return;
    this.output(this.createLogEntry(LogLevel.INFO, message, module, data));
  }

  /**
   * Log a warning message
   * @param message - Warning message
   * @param module - Optional module name
   * @param data - Optional additional data
   */
  public warn(message: string, module?: string, data?: Record<string, unknown>): void {
    if (!this.isLevelEnabled(LogLevel.WARN)) return;
    this.output(this.createLogEntry(LogLevel.WARN, message, module, data));
  }

  /**
   * Log an error message
   * @param message - Error message
   * @param error - Error object
   * @param module - Optional module name
   * @param data - Optional additional data
   */
  public error(
    message: string,
    error: Error,
    module?: string,
    data?: Record<string, unknown>
  ): void {
    if (!this.isLevelEnabled(LogLevel.ERROR)) return;
    this.output(this.createLogEntry(LogLevel.ERROR, message, module, data, error));
  }
}

export const logger = Logger.getInstance();
