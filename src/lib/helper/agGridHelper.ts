import { FilterParams, PaginatedResponse, PaginationParams } from '@/api/core/types';

import { IExtendedSortChangedEvent, IPagination } from '@/types/AgGridTypes';

export const getPaginationData = <T>(object?: PaginatedResponse<T>): IPagination => ({
  page: object?.page ?? 1,
  limit: object?.limit ?? 100,
  totalPages: object?.totalPages ?? 1,
  total: object?.total ?? 0,
  hasNextPage: object?.hasNextPage ?? false,
  hasPreviousPage: object?.hasPreviousPage ?? false,
});

export const onSortChangeHandler = (
  params: IExtendedSortChangedEvent,
  filterParams: PaginationParams & FilterParams
): PaginationParams & FilterParams => {
  const lastColumn = params.columns?.at(-1);
  if (!lastColumn) return filterParams;

  const { colId: sortField, sort } = lastColumn;

  const updatedParams: PaginationParams & FilterParams = {
    ...filterParams,
    sortField,
  };

  if (sort) {
    updatedParams.sortDirection = sort === 'asc' ? 'ASC' : 'DESC';
  } else {
    delete updatedParams.sortDirection;
    delete updatedParams.sortField;
  }

  return updatedParams;
};
