export const searchData = (
  data: any[],
  condition: any,
  colDefs: any[],
  avoidSearch: string[] = []
): any[] => {
  const { query } = condition;
  const normalizedQuery = query.toLowerCase();

  return data.filter((item) => {
    return colDefs.some((col) => {
      const field = col.field;
      if (field && !avoidSearch.includes(field) && item[field]) {
        return item[field].toString().toLowerCase().includes(normalizedQuery);
      }
      return false;
    });
  });
};
