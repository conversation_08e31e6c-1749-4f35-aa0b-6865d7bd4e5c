import { StorageKeys } from '@/types/enums/StorageEnums';
import { getParsedStorageItem, removeStorageItem } from '../Storage';
import { IUser } from '@/types/UserTypes';
import { ROUTES } from '@/constant/RoutesConstant';

export const getUserInfoFromStorage = (): IUser | null => {
  return getParsedStorageItem(StorageKeys.USER_INFO) || null;
};

export const sessionExpiredHandler = () => {
  removeStorageItem(StorageKeys.USER_INFO);
  removeStorageItem(StorageKeys.IS_AUTHENTICATED);
  setTimeout(() => {
    window.location.replace(ROUTES.COMMON.LOGIN);
  }, 2000);
};
