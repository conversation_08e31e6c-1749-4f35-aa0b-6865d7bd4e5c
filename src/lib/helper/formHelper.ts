export const isFormChangedHandler = (
  initialValue: Record<string, any>,
  currentValue: Record<string, any>,
  fieldsWithInitialValues: string[] = []
): boolean => {
  const isInitialValueEmpty = Object.keys(initialValue).length < 1;
  const isAddFormChanged = Object.keys(currentValue).some((bool) => {
    return !fieldsWithInitialValues.includes(bool) && currentValue[bool];
  });

  return isInitialValueEmpty
    ? isAddFormChanged
    : JSON.stringify(initialValue) !== JSON.stringify(currentValue);
};

export const numberParser = (value: string | undefined) => {
  if (!value) {
    return 0;
  }
  const parsedValue = parseFloat(value.replace(/\$\s?|(,*)/g, ''));
  return isNaN(parsedValue) ? 0 : parsedValue;
};

export const isJSON = (str: string) => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
};
