const checkAddressString = (name: string, str: string | null) => {
  if (!str) {
    return name;
  }
  const result = str?.substring(0, name.length);
  return name === result ? str : `${name}, ${str}`;
};

export const googlePlaceDataMasking = (data: google.maps.places.PlaceResult) => {
  const { address_components, geometry, name } = data;

  const maskedObj: { [key: string]: any } = {};

  maskedObj.latitude = geometry?.location?.lat() as number;
  maskedObj.longitude = geometry?.location?.lng() as number;

  address_components?.forEach((element) => {
    const elementType = element?.types?.[0];

    switch (elementType) {
      case 'administrative_area_level_2':
      case 'locality':
        maskedObj.city = element?.long_name || '';
        break;
      case 'country':
        maskedObj.country = element?.long_name || '';
        break;
      case 'postal_code':
      case 'postal_code_prefix':
        maskedObj.postalCode = element?.long_name || '';
        break;
      case 'administrative_area_level_1':
        maskedObj.state = element?.long_name || '';
        break;
      default:
        break;
    }
  });
  if (name) {
    maskedObj.addressLine1 = name;
    maskedObj.formattedAddress = checkAddressString(name, data?.formatted_address || null);
  }
  return maskedObj;
};
