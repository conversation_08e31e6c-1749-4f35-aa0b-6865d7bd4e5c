import { ThemeConfig } from 'antd';

const primary500 = '#088dc1';
const primary600 = '#0876a4';

export const ANTD_THEME: ThemeConfig = {
  token: {
    fontFamily: 'Inter',
  },
  components: {
    Input: {
      activeBorderColor: primary600,
      hoverBorderColor: primary600,
    },
    Button: {
      colorPrimary: primary600,
      colorPrimaryHover: primary600,
      colorPrimaryActive: primary600,
      colorPrimaryBg: primary600,
      colorPrimaryBgHover: primary600,
      borderRadius: 8,
    },
    Select: {
      activeBorderColor: primary600,
      hoverBorderColor: primary600,
      optionSelectedColor: primary600,
      optionSelectedFontWeight: 400,
      borderRadius: 8,
    },
    Tabs: {
      itemHoverColor: primary600,
    },
    DatePicker: {
      colorPrimary: primary600,
      hoverBorderColor: primary600,
    },
    Checkbox: {
      colorPrimary: primary600,
    },
    Radio: {
      colorPrimary: primary600,
      buttonSolidCheckedBg: primary600,
      buttonSolidCheckedHoverBg: primary500,
    },
  },
};
