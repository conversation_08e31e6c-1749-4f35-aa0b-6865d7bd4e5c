import { IPermissionChecker, Action, UPlans } from '@customTypes/enums/Plans';
import { IFeatureChecker, Roles, URoles } from '@/types/enums/Roles';
import { IUser } from '@customTypes/UserTypes';
import { PlanBasedFeatureChecker } from '../planManager/PermissionChecker';
import { RoleBasedPermissionChecker } from '../roleManager/PermissionChecker';
import { IRouteGuard, RouteGuard } from '../routeGuard/RouteGuard';

/**
 * Centralized access control manager for handling role-based permissions,
 * plan-based feature checks, and route activation.
 *
 */
export class AccessController {
  private readonly permissionChecker: IPermissionChecker;
  private readonly featureChecker: IFeatureChecker;
  private readonly routeGuard: IRouteGuard;

  /**
   * @param {Record<Roles, Record<string, Action[]>>} rolePermissions - Role-based permissions for resources and actions.
   * @param {Record<UPlans, Set<string>>} planFeatures - Plan-based access to features.
   */
  public constructor(
    rolePermissions: Record<Roles, Record<string, Action[]>>,
    planFeatures: Record<UPlans, Set<string>>
  ) {
    this.permissionChecker = new RoleBasedPermissionChecker(rolePermissions);
    this.featureChecker = new PlanBasedFeatureChecker(planFeatures);
    this.routeGuard = new RouteGuard();
  }

  /**
   * Checks if a user has permission for a specific action on a resource.
   *
   * @param {IUser} user - The user to check.
   * @param {string} resource - The resource in question.
   * @param {Action} action - The action to validate.
   * @returns {boolean} - `true` if the user has permission, otherwise `false`.
   */
  public roleBasedPermissionChecker(user: IUser, resource: string, action: Action): boolean {
    return this.permissionChecker.hasPermission(user, resource, action);
  }

  /**
   * Checks if a user has access to a specific feature based on their plan.
   *
   * @param {IUser} user - The user to check.
   * @param {string} feature - The feature to validate.
   * @returns {boolean} - `true` if the feature is available, otherwise `false`.
   */
  public planBasedFeatureChecker(user: IUser, feature: string): boolean {
    return this.featureChecker.hasFeature(user, feature);
  }

  /**
   * Validates if a user can activate a route based on their role.
   *
   * @param {IUser} user - The user to check.
   * @param {URoles[]} roles - The roles allowed to access the route.
   * @returns {boolean} - `true` if the user has access, otherwise `false`.
   */
  public checkRoute(user: IUser, roles: URoles[]): boolean {
    return this.routeGuard.canActivate(user, roles);
  }
}
