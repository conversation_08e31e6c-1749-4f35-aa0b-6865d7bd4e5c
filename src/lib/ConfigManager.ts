import { ConfigSchema } from '@customTypes/ConfigSchema';

type EnvType = 'development' | 'staging' | 'production';

class ConfigManager {
  private static instance: ConfigManager;
  private config: Partial<ConfigSchema> = {};

  private constructor() {}

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  public async initialize(): Promise<void> {
    try {
      await this.loadEnvConfig();
      console.log('Configuration loaded:', this.config);
    } catch (error) {
      console.error('Failed to initialize config:', error);
      throw new Error('Configuration initialization failed');
    }
  }

  public getAll(): Readonly<Partial<ConfigSchema>> {
    return Object.freeze({ ...this.config });
  }

  public updateConfig(newConfig: Partial<ConfigSchema>): void {
    this.config = { ...this.config, ...newConfig };
    this.persistConfig();
  }

  private async loadEnvConfig(): Promise<void> {
    const env = this.getEnvironment();
    const envConfig = await this.loadEnvironmentConfig(env);
    const runtimeConfig = this.loadRuntimeConfig();
    this.config = { ...envConfig, ...runtimeConfig };
  }

  public get<K extends keyof ConfigSchema>(key: K): ConfigSchema[K] | undefined {
    return this.config[key] as ConfigSchema[K] | undefined;
  }

  private getEnvironment(): EnvType {
    const env = import.meta.env.VITE_APP_ENV;
    if (env !== 'development' && env !== 'staging' && env !== 'production') {
      console.warn(`Invalid environment: ${env}. Falling back to development.`);
      return 'development';
    }
    return env;
  }

  private async loadEnvironmentConfig(env: EnvType): Promise<Partial<ConfigSchema>> {
    try {
      const envConfig = await import(`../config/${env}.config.ts`);
      return envConfig.default;
    } catch (error) {
      console.error(`Failed to load config for environment: ${env}`, error);
      return {};
    }
  }

  private loadRuntimeConfig(): Partial<ConfigSchema> {
    const storedConfig = localStorage.getItem('runtimeConfig');
    return storedConfig ? JSON.parse(storedConfig) : {};
  }

  private persistConfig(): void {
    localStorage.setItem('runtimeConfig', JSON.stringify(this.config));
  }
}

export default ConfigManager.getInstance();
