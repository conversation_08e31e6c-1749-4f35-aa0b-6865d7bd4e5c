import { AxiosRequestConfig } from 'axios';

/**
 * Custom error class for API-related errors.
 * Extends the native Error class with additional properties specific to API responses.
 */
export class ApiError extends Error {
  /**
   * Constructs an ApiError instance.
   * @param statusCode - The HTTP status code of the error response
   * @param responseData - The data returned in the error response
   * @param requestConfig - The configuration of the request that caused the error
   */
  constructor(
    public statusCode: number,
    public responseData: any,
    public requestConfig: AxiosRequestConfig
  ) {
    super(`API Error: ${statusCode}`);
    this.name = 'ApiError';
  }
}
