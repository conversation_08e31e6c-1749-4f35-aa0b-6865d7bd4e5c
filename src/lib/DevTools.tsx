import React, { useEffect, useMemo, useState } from 'react';
import { useConfig } from '@/contexts/ConfigContext';
import { ConfigSchema } from '@customTypes/ConfigSchema';
import {
  seedAddresses,
  seedCustomerAddresses,
  seedCustomers,
  seedOrderAttachments,
  seedOrderPackages,
  seedPriceBreakdownList,
  seedPriceModifiers,
  seedServices,
  seedVehicles,
  seedZones,
} from './SeedHelper';

type ConfigValue = string | number | boolean | object;

const DevTools: React.FC = () => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const { config, updateConfig } = useConfig();
  const [localConfig, setLocalConfig] = useState<Partial<ConfigSchema>>(config);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());
  const [activeTab, setActiveTab] = useState<string>('');

  useEffect(() => {
    setLocalConfig(config);
  }, [config]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.shiftKey && event.key === 'D') {
        event.preventDefault();
        setIsOpen((prev) => !prev);
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const tabs = useMemo(() => {
    return [
      'General',
      ...Object.keys(config).filter((key) => typeof config[key as keyof ConfigSchema] === 'object'),
    ];
  }, [config]);

  useEffect(() => {
    if (tabs.length > 0 && !activeTab) {
      setActiveTab(tabs[0]);
    }
  }, [tabs, activeTab]);

  const handleChange = (path: string, value: ConfigValue) => {
    setLocalConfig((prev) => {
      const newConfig = { ...prev };
      const keys = path.split('.');
      let current: any = newConfig;
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) current[keys[i]] = {};
        current = current[keys[i]];
      }
      current[keys[keys.length - 1]] = value;
      return newConfig;
    });
  };

  const handleSave = () => {
    updateConfig(localConfig);
  };

  const toggleSection = (path: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  const renderConfigInput = (key: string, value: ConfigValue, path: string) => {
    if (typeof value === 'boolean') {
      return (
        <div className="flex items-center justify-between py-1">
          <span className="text-gray-700">{key}</span>
          <input
            type="checkbox"
            checked={value}
            onChange={(e) => handleChange(path, e.target.checked)}
            className="form-checkbox h-4 w-4 text-blue-600 rounded"
          />
        </div>
      );
    } else if (typeof value === 'number' || typeof value === 'string') {
      return (
        <div className="flex items-center justify-between py-1">
          <span className="text-gray-700">{key}</span>
          <input
            type={typeof value === 'number' ? 'number' : 'text'}
            value={value}
            onChange={(e) =>
              handleChange(
                path,
                e.target.type === 'number' ? parseFloat(e.target.value) : e.target.value
              )
            }
            className="w-1/2 px-2 py-1 text-sm border rounded bg-white focus:ring-1 focus:ring-blue-500"
          />
        </div>
      );
    } else if (typeof value === 'object' && value !== null) {
      const isExpanded = expandedSections.has(path);
      return (
        <div className="py-1">
          <div className="flex items-center cursor-pointer" onClick={() => toggleSection(path)}>
            <span className="mr-2">{isExpanded ? '▼' : '▶'}</span>
            <span className="text-gray-700">{key}</span>
          </div>
          {isExpanded && (
            <div className="pl-4 border-l border-gray-300 mt-1">
              {Object.entries(value).map(([subKey, subValue]) => (
                <div key={`${path}.${subKey}`}>
                  {renderConfigInput(subKey, subValue, `${path}.${subKey}`)}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  const renderTabContent = (tabKey: string) => {
    if (tabKey === 'General') {
      return Object.entries(localConfig).map(([key, value]) => {
        if (typeof value !== 'object') {
          return <div key={key}>{renderConfigInput(key, value, key)}</div>;
        }
        return null;
      });
    } else if (tabKey === 'Seeder') {
      return (
        <div className="grid grid-cols-2 gap-4 p-4">
          <button
            onClick={seedServices}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
          >
            Seed Service
          </button>

          <button
            onClick={seedZones}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
          >
            Seed Zones
          </button>

          <button
            onClick={seedCustomerAddresses}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
          >
            Seed Customer Addresses
          </button>
          <button
            onClick={seedAddresses}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
          >
            Seed Addresses
          </button>

          <button
            onClick={seedVehicles}
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
          >
            seed Vehicles
          </button>
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
            onClick={seedPriceModifiers}
          >
            Seed Modifiers
          </button>
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
            onClick={seedCustomers}
          >
            Seed customers
          </button>
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
            onClick={seedPriceBreakdownList}
          >
            Seed Price Breakdown List
          </button>
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
            onClick={seedOrderPackages}
          >
            Seed Order Packages
          </button>
          <button
            className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded"
            onClick={seedOrderAttachments}
          >
            Seed Order Attachments
          </button>
        </div>
      );
    } else {
      const tabContent = localConfig[tabKey as keyof ConfigSchema];
      if (typeof tabContent === 'object' && tabContent !== null) {
        return Object.entries(tabContent).map(([key, value]) => (
          <div key={key}>{renderConfigInput(key, value, `${tabKey}.${key}`)}</div>
        ));
      }
    }
    return null;
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed bottom-0 left-0 right-0 h-1/2 bg-white border-t border-gray-300 flex flex-col"
      style={{ zIndex: '9999' }}
    >
      <div className="flex border-b border-gray-300">
        {tabs.map((tab) => (
          <button
            key={tab}
            className={`px-4 py-2 ${activeTab === tab ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
            onClick={() => setActiveTab(tab)}
          >
            {tab}
          </button>
        ))}
        <button
          key="Seeder"
          className={`px-4 py-2 ${activeTab === 'Seeder' ? 'bg-gray-200' : 'hover:bg-gray-100'}`}
          onClick={() => setActiveTab('Seeder')}
        >
          Seeder
        </button>
      </div>
      <div className="flex-grow overflow-auto p-4">{renderTabContent(activeTab)}</div>
      <div className="border-t border-gray-300 p-2 flex justify-between items-center bg-gray-100">
        <button
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          onClick={handleSave}
        >
          Save Changes
        </button>
        <button
          className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors"
          onClick={() => setIsOpen(false)}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default DevTools;
