import { StorageKeys } from '@customTypes/enums/StorageEnums';
import { isJSON } from './helper';

export function getStorageItem(key: StorageKeys): string | null {
  return localStorage.getItem(key);
}

export function getParsedStorageItem<T>(key: StorageKeys): T | null {
  const item = localStorage.getItem(key);
  return item && isJSON(item) ? JSON.parse(item) : null;
}

export function setStorageItem(key: StorageKeys, data: string): void {
  localStorage.setItem(key, data);
}

export function setStringifyStorageItem(key: StorageKeys, data: Record<string, any>): void {
  return localStorage.setItem(key, JSON.stringify(data));
}

export function removeStorageItem(key: StorageKeys): void {
  localStorage.removeItem(key);
}
