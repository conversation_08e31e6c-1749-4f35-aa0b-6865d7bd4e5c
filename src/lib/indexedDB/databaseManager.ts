import { DATABAS<PERSON>_NAME, DATABASE_VERSION, STORES } from './databaseConfig';

export class DatabaseManager {
  private static db: IDBDatabase | null = null;
  private static initializing: Promise<IDBDatabase> | null = null;

  static async getDB(): Promise<IDBDatabase> {
    if (this.db) {
      return this.db;
    }
    if (this.initializing) {
      return this.initializing;
    }

    this.initializing = new Promise((resolve, reject) => {
      const request = indexedDB.open(DATABASE_NAME, DATABASE_VERSION);

      request.onupgradeneeded = () => {
        const db = request.result;
        // Create stores if they don't exist
        for (const storeConfig of STORES) {
          if (!db.objectStoreNames.contains(storeConfig.storeName)) {
            const objectStore = db.createObjectStore(storeConfig.storeName, storeConfig.options);

            // Create indexes if defined
            if (storeConfig.indexes) {
              for (const idx of storeConfig.indexes) {
                objectStore.createIndex(idx.name, idx.keyPath, idx.options);
              }
            }
          } else {
            // If we need to add indexes to existing stores in future,
            // we could handle that here by checking and creating missing indexes.
            // but i dont thing we are ever going to do this lol
          }
        }
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };

      request.onerror = () => reject(request.error);
    });

    return this.initializing;
  }
}
