import { SidebarLayoutProps } from '@/components/layout/SidebarLayout/SidebarLayout';
import { URoles } from '@/types/enums/Roles';
import { IUser } from '@customTypes/UserTypes';

export interface IRouteGuard {
  canActivate(user: IUser, roles: URoles[]): boolean;
  filterSidebarItems: (items: SidebarLayoutProps[], userRole: URoles) => SidebarLayoutProps[];
}

/**
 * Handles route-based access control.
 */

export class RouteGuard implements IRouteGuard {
  /**
   * Checks if a user can access a route based on their role.
   *
   * @param {IUser} user - The user to check.
   * @param {URoles[]} roles - Allowed roles for the route.
   * @returns {boolean} - `true` if access is allowed, otherwise `false`.
   */
  public canActivate(user: IUser, roles: URoles[]): boolean {
    return roles.includes(user.role);
  }

  /**
   * Filters sidebar items based on the user's role.
   *
   * @param {SidebarLayoutProps[]} items - Sidebar items to filter.
   * @param {URoles} userRole - User's role.
   * @returns {SidebarLayoutProps[]} - Filtered sidebar items.
   */
  public filterSidebarItems(items: SidebarLayoutProps[], userRole: URoles): SidebarLayoutProps[] {
    return items
      .map((item) => ({
        ...item,
        children: item.children?.filter((child) => child?.allowedroles?.includes(userRole)),
      }))
      .filter((item) => item.children?.length);
  }
}
