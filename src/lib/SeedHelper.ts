import { ICustomerService } from '@/pages/customer/customerOperations/customerServices/customerServiceTypes';
import { IndexedDBStore } from './indexedDB';
import { StoreName } from './indexedDB/databaseConfig';
import { IZone } from '@pages/location/zone/list/zone.type.ts';
import { ICustomerAddress } from '@/pages/customer/customerOperations/customerAddress/customerAddress.types';
import { IAddress } from '@/pages/location/address/address.type';
import { ITimeClockSessions, IVehicle } from '@/pages/logistics/vehicle/vehicleTypes';
import {
  CalculationField,
  CalculationType,
  IGroupPriceModifier,
  IIndividualPriceModifier,
  IPriceModifier,
  ModifierGroupBehavior,
  RangeFromOperator,
  RangeToOperator,
} from '@/pages/priceRules/priceModifier/priceModifiers.types';
import { ICustomer } from '@/pages/customer/customer.types';
import { IPriceBreakdown } from '@/pages/logistics/orders/ordersOperation/OrderPriceBreakdown/orderPriceBreakdown.types';
import { IPackage } from '@/pages/logistics/orders/ordersOperation/orderPackages/orderPackages.types';
import { IAttachments } from '@/pages/logistics/orders/ordersOperation/orderAttachments/orderAttachments.types';

const customerServicesStore = new IndexedDBStore<ICustomerService>(StoreName.customerService);
const zoneStore = new IndexedDBStore<IZone>(StoreName.Zone);
const customerAddressStore = new IndexedDBStore<ICustomerAddress>(StoreName.customerAddress);
const addressStore = new IndexedDBStore<IAddress>(StoreName.Addresses);
const vehicleStore = new IndexedDBStore<IVehicle>(StoreName.Vehicles);
const modifierStore = new IndexedDBStore<IPriceModifier>(StoreName.priceModifier);
const priceBreakdown = new IndexedDBStore<IPriceBreakdown>(StoreName.priceBreakdown);
const packagesStore = new IndexedDBStore<IPackage>(StoreName.packageType);
const attachmentsStore = new IndexedDBStore<IAttachments>(StoreName.orderAttachments);

export const seedServices = () => {
  const names = ['Esthern Howard', 'Jack moris', 'Jenny Wilson', 'Anneth Black'];
  const ServiceNames = [
    '7888015 canada inc 2 hr charge',
    '7888015 canada inc nextday 3.30PM charges',
    '7888015 canada inc sameday charges',
    '7888015 canada inc 4 hr charges',
    'ABC candy 2 days charges',
    'ABC candy 2 hr charges',
  ];

  const levels = ['Express 2hr service', '4hr service service'];

  function getRandomItem(array: string[]): string {
    return array[Math.floor(Math.random() * array.length)];
  }

  async function generateRandomCustomers(count: number) {
    for (let i = 0; i < count; i++) {
      const randomService: ICustomerService = {
        id: customerServicesStore.generateKey(),
        serviceName: getRandomItem(ServiceNames),
        serviceLevel: getRandomItem(levels),
        isSelected: false,
        notes: 'Random note for price set',
        paymentOption: getRandomItem(['Full', 'None', 'Authorized amount']),
        assignedCustomers: [],
        description: 'Random description for price set',
        createdAt: new Date(Date.now() - Math.floor(Math.random() * **********0)).toISOString(),
        updatedAt: new Date(Date.now() - Math.floor(Math.random() * **********0)).toISOString(),
        lastUpdateBy: getRandomItem(names),
      };
      await customerServicesStore.put(randomService).then(() => {
        console.log('Data Added');
      });
    }
  }

  generateRandomCustomers(20);
};

export const seedZones = async () => {
  // Generate 30 sample zones
  const sampleZones: IZone[] = [];
  const randomCodes: string[] = ['A1S', 'GH2', '2PO', 'H9K', 'IT6'];

  // function randomFsa() {
  //   // Example FSA format: Letter-Digit-Letter
  //   const randLetter = () => letters[Math.floor(Math.random() * letters.length)];
  //   const randDigit = () => digits[Math.floor(Math.random() * digits.length)];
  //   return `${randLetter()}${randDigit()}${randLetter()}`;
  // }

  for (let i = 0; i < 2; i++) {
    const id = zoneStore.generateKey();
    const name = `Zone ${i + 1}`;
    // Generate between 1 to 3 postal codes
    const postalCodes = Array.from(
      { length: 2 },
      () => randomCodes[Math.floor(Math.random() * randomCodes.length)]
    );
    const comment = `Auto-generated zone ${i + 1}`;
    sampleZones.push({ id, name, postalCodes, comment });
  }

  // Clear existing data and re-seed
  await zoneStore.clear();
  for (const z of sampleZones) {
    await zoneStore.put(z);
  }

  window.location.reload();
};
export const seedCustomerAddresses = async () => {
  const names = ['Esthern Howard', 'Jack moris', 'Jenny Wilson', 'Anneth Black'];

  const companies = [
    'Jaco - jaguar Corporation',
    'VG - Van Group',
    'Hero - Hero Corporation',
    'Ome - Omega Corporation',
    'Xi - Xian Group',
  ];

  function getRandomItem(array: string[]): string {
    return array[Math.floor(Math.random() * array.length)];
  }

  async function generateRandomCustomers(count: number) {
    for (let i = 0; i < count; i++) {
      const randomService: ICustomerAddress = {
        id: customerAddressStore.generateKey(),
        companyName: getRandomItem(companies),
        name: getRandomItem(names),
        addressLine1: `${Math.floor(Math.random() * 1000)} ${getRandomItem([
          'Main St',
          'Oak Avenue',
          'Pine Street',
          'Sunset Boulevard',
          'Maple Lane',
          'River Rd',
        ])}`,
        addressLine2: `${Math.floor(Math.random() * 1000)} ${getRandomItem([
          'Main St',
          'Oak Avenue',
          'Pine Street',
          'Sunset Boulevard',
          'Maple Lane',
          'River Rd',
        ])}`,
        city: getRandomItem([
          'New York',
          'Los Angeles',
          'Chicago',
          'Houston',
          'Phoenix',
          'San Francisco',
        ]),
        country: getRandomItem(['USA', 'Canada', 'UK', 'Germany', 'Australia']),
        province: getRandomItem(['California', 'Texas', 'New York', 'Florida', 'Nevada']),
        postalCode: `${Math.floor(Math.random() * 90000) + 10000}`,
        email: `${Math.random().toString(36).substring(2, 10)}@mail.com`,
        phone: `${Math.floor(Math.random() * ********** + **********)}`,

        createdAt: new Date(Date.now() - Math.floor(Math.random() * **********0)).toISOString(),
        updated: new Date(Date.now() - Math.floor(Math.random() * **********0)).toISOString(),
        lastUpdateBy: getRandomItem(names),
        notes: 'Entrance is on the left side of building',
        phoneExtension: getRandomItem(['543346', '532451', '294958']),
        phoneNumberCountryCode: getRandomItem(['USA +1', 'IND +1', 'CAN +1']),
        zone: 'Zone A',
      };
      await customerAddressStore.put(randomService).then(() => {
        console.log('Data Added');
      });
    }
  }

  generateRandomCustomers(100);
};

export const seedCustomers = () => {
  const names = [
    'John Smith',
    'Jane Doe',
    'Michael Brown',
    'Sarah Johnson',
    'James Wilson',
    'Linda Martinez',
    'Robert Garcia',
    'Emily Davis',
    'William Lopez',
    'Elizabeth Hernandez',
    'David Walker',
    'Sophia Allen',
    'Ethan Young',
    'Isabella King',
    'Daniel Wright',
    'Mia Scott',
    'Matthew Adams',
    'Avery Baker',
    'Benjamin Nelson',
    'Harper Carter',
  ];

  const companyNames = [
    'TechCorp',
    'Global Solutions',
    'Innovatech',
    'Alpha Systems',
    'Prime Logistics',
    'EcoEnergy',
    'VisionWorks',
    'Blue Ocean',
    'Summit Strategies',
    'NextGen Dynamics',
    'Green Fields',
    'Urban Mobility',
    'CyberStream',
    'Bright Future',
    'LogiTech Services',
    'Redwood Group',
    'Skyline Partners',
    'Infinity Ventures',
    'Swift Transport',
    'Pinnacle Advisors',
  ];
  const customerStore = new IndexedDBStore<ICustomer>('Customers');

  function getRandomItem(array: string[]): string {
    return array[Math.floor(Math.random() * array.length)];
  }

  async function generateRandomCustomers(count: number) {
    const countries = ['USA', 'Canada', 'UK', 'Germany', 'Australia'];

    for (let i = 0; i < count; i++) {
      const randomAddress: ICustomer = {
        id: customerStore.generateKey(),
        companyName: getRandomItem(companyNames),
        accountNumber: `ACC${Math.floor(100000 + Math.random() * 900000)}`,
        contactName: getRandomItem(names),
        addressLine1: `${Math.floor(Math.random() * 1000)} ${getRandomItem([
          'Main St',
          'Oak Avenue',
          'Pine Street',
          'Sunset Boulevard',
          'Maple Lane',
          'River Rd',
        ])}`,
        city: getRandomItem([
          'New York',
          'Los Angeles',
          'Chicago',
          'Houston',
          'Phoenix',
          'San Francisco',
        ]),
        fax: `${Math.floor(Math.random() * ********** + **********)}`,
        faxNumberPrefix: getRandomItem(['Usa', 'India', 'Canada']),
        status: getRandomItem(['0', '1']) === '1',
        province: getRandomItem(['California', 'Texas', 'New York', 'Florida', 'Nevada']),
        postalCode: `${Math.floor(Math.random() * 90000) + 10000}`,
        category: [getRandomItem(Object.values(['Envelop', 'Box', 'Product']))],
        email: `${Math.random().toString(36).substring(2, 10)}@mail.com`,
        phone: `${Math.floor(Math.random() * ********** + **********)}`,
        phoneNumberPrefix: getRandomItem(['Usa', 'India', 'Canada']),
        country: getRandomItem(countries),
        dateAdded: new Date(Date.now() - Math.floor(Math.random() * **********0)).toISOString(),
        dateUpdated: new Date(Date.now() - Math.floor(Math.random() * **********0)).toISOString(),
        lastUpdateBy: getRandomItem(names),
      };

      await customerStore.put(randomAddress).then(() => {
        console.log('Data Added');
      });
    }
  }

  generateRandomCustomers(100);
};
export const seedAddresses = () => {
  const names = [
    'John Smith',
    'Jane Doe',
    'Michael Brown',
    'Sarah Johnson',
    'James Wilson',
    'Linda Martinez',
    'Robert Garcia',
    'Emily Davis',
    'William Lopez',
    'Elizabeth Hernandez',
    'David Walker',
    'Sophia Allen',
    'Ethan Young',
    'Isabella King',
    'Daniel Wright',
    'Mia Scott',
    'Matthew Adams',
    'Avery Baker',
    'Benjamin Nelson',
    'Harper Carter',
  ];

  const companyNames = [
    'TechCorp',
    'Global Solutions',
    'Innovatech',
    'Alpha Systems',
    'Prime Logistics',
    'EcoEnergy',
    'VisionWorks',
    'Blue Ocean',
    'Summit Strategies',
    'NextGen Dynamics',
    'Green Fields',
    'Urban Mobility',
    'CyberStream',
    'Bright Future',
    'LogiTech Services',
    'Redwood Group',
    'Skyline Partners',
    'Infinity Ventures',
    'Swift Transport',
    'Pinnacle Advisors',
  ];

  function getRandomItem(array: string[]): string {
    return array[Math.floor(Math.random() * array.length)];
  }

  async function generateRandomAddresses(count: number) {
    const zones = ['Zone A', 'Zone B', 'Zone C', 'Zone D'];
    const countries = ['USA', 'Canada', 'UK', 'Germany', 'Australia'];

    for (let i = 0; i < count; i++) {
      const randomAddress: IAddress = {
        id: addressStore.generateKey(), // Unique ID
        customer: getRandomItem(companyNames),
        name: getRandomItem(names),
        contact: getRandomItem(names),
        addressLine1: `${Math.floor(Math.random() * 1000)} ${getRandomItem([
          'Main St',
          'Oak Avenue',
          'Pine Street',
          'Sunset Boulevard',
          'Maple Lane',
          'River Rd',
        ])}`,
        addressLine2: `${Math.floor(Math.random() * 100)} ${getRandomItem([
          'Apt',
          'Suite',
          'Floor',
          'Building',
        ])}`,
        city: getRandomItem([
          'New York',
          'Los Angeles',
          'Chicago',
          'Houston',
          'Phoenix',
          'San Francisco',
        ]),
        province: getRandomItem(['California', 'Texas', 'New York', 'Florida', 'Nevada']),
        postalCode: `${Math.floor(Math.random() * 90000) + 10000}`,
        zone: getRandomItem(zones),
        email: `${Math.random().toString(36).substring(2, 10)}@mail.com`,
        phone: `${Math.floor(Math.random() * ********** + **********)}`,
        phoneExtension: `${Math.floor(Math.random() * 1000)}`,
        companyName: getRandomItem(companyNames),
        country: getRandomItem(countries),
        comment: 'Random entry for testing purposes.',
        dateUpdated: new Date(Date.now() - Math.floor(Math.random() * **********0)).toISOString(),
        lastUpdateBy: getRandomItem(names),
      };
      await addressStore.put(randomAddress).then(() => {
        console.log('Data Added');
      });
    }
  }

  generateRandomAddresses(200);
};

function getRandomItem(array: string[]): string {
  return array[Math.floor(Math.random() * array.length)];
}

export const seedVehicles = () => {
  const type = ['Car', 'Truck', 'Van'];

  const capacity = ['20000', '12500', '15000', '17000', '44000'];

  async function generateRandomAddresses(count: number) {
    const names = [
      'John Smith',
      'Jane Doe',
      'Michael Brown',
      'Sarah Johnson',
      'James Wilson',
      'Linda Martinez',
      'Robert Garcia',
      'Emily Davis',
      'William Lopez',
      'Elizabeth Hernandez',
      'David Walker',
      'Sophia Allen',
      'Ethan Young',
      'Isabella King',
      'Daniel Wright',
      'Mia Scott',
      'Matthew Adams',
      'Avery Baker',
      'Benjamin Nelson',
      'Harper Carter',
    ];
    const sessions: ITimeClockSessions[] = [];

    const generateTimeClockSession = async () => {
      for (let index = 0; index < 10; index++) {
        sessions.push({
          id: vehicleStore.generateKey(),
          entryDate: getRandomItem([
            '2025-01-25T10:30:00+00:00',
            '2025-02-19T10:30:00+00:00',
            '2025-02-14T10:30:00+00:00',
          ]),
          driverName: getRandomItem(names),
          vehicle: getRandomItem(['Apex', 'Falco', 'Infinity', 'Vortex']),
          distance: Number(getRandomItem(['15', '14', '26', '40', '60'])),
          startTime: getRandomItem([
            '2025-01-25T10:30:00+00:00',
            '2025-02-19T10:30:00+00:00',
            '2025-02-14T10:30:00+00:00',
          ]),
          endTime: getRandomItem([
            '2025-03-25T10:30:00+00:00',
            '2025-01-11T10:30:00+00:00',
            '2025-01-17T10:30:00+00:00',
            '0',
            '1',
          ]),
          source: getRandomItem(['Manual', 'Automated']),
        });
      }
    };
    await generateTimeClockSession();

    for (let i = 0; i < count; i++) {
      const randomAddress: IVehicle = {
        id: vehicleStore.generateKey(), // Unique ID
        type: getRandomItem(type),
        capacity: Number(getRandomItem(capacity)),
        fleetId: getRandomItem(['B001', 'A008', 'K005']),
        odometer: `${Math.floor(Math.random() * 6000) * 100}`,
        packageType: [getRandomItem(['Box', 'Skid', 'Custom'])],
        license: `${Math.floor(Math.random() * 9000)}`,
        capabilities: getRandomItem(['Skids', 'Small Boxes', 'Large Boxes', 'Envelops']),
        branches: getRandomItem([
          'Montreal Branch',
          'Saint main Branch',
          'NovaDrive Branch',
          'Apex Branch',
        ]),
        make: getRandomItem([
          'Quantum Drive',
          'Eclipse Motors',
          'NovaDrive',
          'NextGen Vehicles',
          'Apex Auto',
          'Infinity Motors',
        ]),
        model: getRandomItem(['Apex', 'Falco', 'Infinity', 'Vortex']),
        year: getRandomItem(['2009', '2015', '2019', '2021', '2024']),
        defaultDriver: getRandomItem(names),
        comment: 'random comment',
        ownedBy: getRandomItem(['VNP', 'Xline', 'Abhi Logistics']),
        VIN: `${Math.floor(Math.random() * ********** + **********)}`,
        timeClockSessions: sessions,
      };
      await vehicleStore.put(randomAddress).then(() => {
        console.log('Data Added');
      });
    }
  }

  generateRandomAddresses(100);
};

export const seedPriceModifiers = () => {
  const calculationTypes: CalculationType[] = [];
  const calculationFields: CalculationField[] = [];
  const rangeOperators: RangeFromOperator[] = [];
  const rangeToOperators: RangeToOperator[] = [];
  const behaviorTypes: ModifierGroupBehavior[] = [];

  function getRandomItem<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  function generateRandomIndividualModifier(): IIndividualPriceModifier {
    return {
      id: modifierStore.generateKey(),
      Name: `Modifier_${Math.random().toString(36).substring(7)}`,
      PublicName: `Public_Modifier_${Math.random().toString(36).substring(7)}`,
      CalculationType: getRandomItem(calculationTypes),
      CalculationField: getRandomItem(calculationFields),
      calcutionObject: { key: Math.random() },
      isSelected: false,
      Increment: Math.random() * 10,
      Value: Math.random() * 100,
      TieredRange:
        Math.random() > 0.5
          ? [
              {
                From: { Value: Math.random() * 10, Operator: getRandomItem(rangeOperators) },
                To: { Value: Math.random() * 20 + 10, Operator: getRandomItem(rangeToOperators) },
                Value: Math.random() * 50,
              },
            ]
          : undefined,
      ApplicableRange:
        Math.random() > 0.5
          ? {
              From: { Value: Math.random() * 10, Operator: getRandomItem(rangeOperators) },
              To: { Value: Math.random() * 30 + 10, Operator: getRandomItem(rangeToOperators) },
            }
          : undefined,
      CalculationStartAfter: Math.random() > 0.5 ? Math.floor(Math.random() * 10) : undefined,
      Description: 'Random modifier for testing.',
      IsEnabled: Math.random() > 0.2,
      IsDriverCommission: Math.random() > 0.5,
      isGroupModifier: false,
    };
  }

  function generateRandomGroupModifier(): IGroupPriceModifier {
    return {
      id: modifierStore.generateKey(),
      Name: `Group_${Math.random().toString(36).substring(7)}`,
      Description: 'Random group modifier for testing.',
      isSelected: false,
      Behavior: getRandomItem(behaviorTypes), // ✅ Now correctly typed
      Modifiers: Array.from(
        { length: Math.floor(Math.random() * 5) + 1 },
        generateRandomIndividualModifier
      ),
      isGroupModifier: true,
    };
  }

  async function generateRandomPriceModifiers(count: number) {
    for (let i = 0; i < count; i++) {
      const randomModifier: IPriceModifier =
        Math.random() > 0.5 ? generateRandomIndividualModifier() : generateRandomGroupModifier();
      await modifierStore.put(randomModifier).then(() => {
        console.log('Price Modifier Added');
      });
    }
  }

  generateRandomPriceModifiers(100);
};

export const seedPriceBreakdownList = () => {
  const gen = async (count: number) => {
    for (let i = 0; i < count; i++) {
      const randomAddress: IPriceBreakdown = {
        id: priceBreakdown.generateKey(),
        appliedModifier: `Public_Modifier_${Math.random().toString(36).substring(7)}`,
        appliedCharges: Number((Math.random() * 100).toFixed(2)),
        description: 'Random modifier for testing.',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        updatedBy: 'John Doe',
      };
      await priceBreakdown.put(randomAddress).then(() => {
        console.log('Data Added Modd');
      });
    }
  };
  gen(100);
};

export const seedOrderPackages = () => {
  const gen = async (count: number) => {
    for (let i = 0; i < count; i++) {
      const random: IPackage = {
        id: packagesStore.generateKey(),
        packageType: getRandomItem(['Envelop', 'Box', 'Container', 'Large box', 'Small box']),
        quantity: Math.floor(Math.random() * 101),

        combinedWeight: Number((Math.random() * 100).toFixed(2)),
        length: Math.floor(Math.random() * 101),
        width: Math.floor(Math.random() * 101),
        height: Math.floor(Math.random() * 101),
        image: getRandomItem([
          'Envelop.png',
          'Box.png',
          'Container.png',
          'Large box.png',
          'Small box.png',
        ]),
      };
      await packagesStore.put(random).then(() => {
        console.log('Data Added');
      });
    }
  };
  gen(10);
};

export const seedOrderAttachments = () => {
  const gen = async (count: number) => {
    for (let i = 0; i < count; i++) {
      const random: IAttachments = {
        id: attachmentsStore.generateKey(),
        file: getRandomItem(['Image.png', 'Document.pdf']),
        type: getRandomItem(['Collection signature', 'Delivery signature', 'FIle attachment']),
        dateAdded: new Date(),
      };
      await attachmentsStore.put(random).then(() => {
        console.log('Data Added');
      });
    }
  };
  gen(10);
};
