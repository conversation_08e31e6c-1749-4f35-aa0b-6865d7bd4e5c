import React from 'react';
import { notification } from 'antd';
import { closeNotifyIcon } from '@/assets';
import { notificationIcons } from '@/constant/NotificationConstant';
import {
  INotificationConfig,
  TNotificationType,
  INotificationOptions,
} from '@customTypes/NotificationTypes';

/**
 * NotificationManager class for managing and displaying notifications
 */

class NotificationManager {
  private readonly config: INotificationConfig;

  /**
   * Creates an instance of NotificationManager.
   * @param {INotificationConfig} config - The notification configuration
   */
  constructor(config: INotificationConfig) {
    this.config = config;

    notification.config({
      closeIcon: <img src={closeNotifyIcon} alt="Close" />,
      ...this.config,
    });
  }

  /**
   * Creates a notification function for a specific type
   * @param {TNotificationType} type - The type of notification
   * @returns {Function} A function to display the notification
   */
  private createNotification(type: TNotificationType) {
    return ({ message, description, duration, placement }: INotificationOptions) => {
      notification[type]({
        message,
        description,
        className: this.config.className,
        icon: notificationIcons[type],
        duration: duration ?? this.config.duration,
        placement: placement ?? this.config.placement,
      });
    };
  }

  /** Display a success notification */
  public success = this.createNotification('success');

  /** Display an error notification */
  public error = this.createNotification('error');

  /** Display an info notification */
  public info = this.createNotification('info');

  /** Display a warning notification */
  public warning = this.createNotification('warning');

  /**
   * Displays a custom notification
   * @param {TNotificationType} type - The type of notification
   * @param {NotificationOptions & { icon?: React.ReactNode }} options - Notification options
   */

  public custom(
    type: TNotificationType,
    options: INotificationOptions & { icon?: React.ReactNode }
  ) {
    const { icon, ...rest } = options;
    notification[type]({
      ...rest,
      className: this.config.className,
      icon: icon ?? notificationIcons[type],
    });
  }
}

export default NotificationManager;
