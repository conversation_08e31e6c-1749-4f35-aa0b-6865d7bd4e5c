import { UPlans } from '@customTypes/enums/Plans';
import { <PERSON>eatureChecker } from '@/types/enums/Roles';
import { IUser } from '@customTypes/UserTypes';

/**
 * Checks feature availability based on user subscription plans.
 *
 * @implements {IFeatureChecker}
 */
export class PlanBasedFeatureChecker implements IFeatureChecker {
  private readonly planFeatures: Record<UPlans, Set<string>>;

  /**
   * @param {Record<UPlans, Set<string>>} planFeatures - Maps plans to their available features.
   */
  constructor(planFeatures: Record<UPlans, Set<string>>) {
    this.planFeatures = planFeatures;
  }

  /**
   * Determines if a feature is available for a given user.
   *
   * @param {IUser} user - The user to check.
   * @param {string} feature - The feature to verify.
   * @returns {boolean} - `true` if available, otherwise `false`.
   */
  public hasFeature(user: IUser, feature: string): boolean {
    return this?.planFeatures[user?.plan]?.has(feature);
  }
}
