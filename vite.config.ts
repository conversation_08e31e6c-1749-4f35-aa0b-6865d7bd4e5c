import { defineConfig, Plugin } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// Custom plugin to exclude config files
const excludeConfigFiles = (currentEnv: string): Plugin => ({
  name: 'exclude-config-files',
  resolveId(source) {
    if (source.includes('/config/') && !source.includes(`${currentEnv}.config.ts`)) {
      return { id: source, external: true };
    }
  },
});

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = mode || 'development';

  return {
    plugins: [react(), excludeConfigFiles(env)],
    define: {
      'import.meta.env.VITE_APP_ENV': JSON.stringify(env),
    },
    build: {
      rollupOptions: {
        input: {
          main: resolve(__dirname, 'index.html'),
        },
        external: [
          // Explicitly exclude config files that don't match the current environment
          ...['development', 'staging', 'production']
            .filter((e) => e !== env)
            .map((e) => new RegExp(`/config/${e}\\.config\\.ts$`)),
        ],
      },
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
        '@api': resolve(__dirname, './src/api'),
        '@assets': resolve(__dirname, './src/assets'),
        '@components': resolve(__dirname, './src/components'),
        '@config': resolve(__dirname, './src/config'),
        '@constant': resolve(__dirname, './src/constant'),
        '@contexts': resolve(__dirname, './src/contexts'),
        '@hooks': resolve(__dirname, './src/hooks'),
        '@i18n': resolve(__dirname, './src/i18n'),
        '@lib': resolve(__dirname, './src/lib'),
        '@pages': resolve(__dirname, './src/pages'),
        '@routes': resolve(__dirname, './src/routes'),
        '@styles': resolve(__dirname, './src/styles'),
        '@customTypes': resolve(__dirname, './src/types'),
      },
    },
  };
});
