# Transport App

## Project Overview

This is a scalable Delivery Management System built with React, TypeScript, and Vite. It uses pnpm as the package manager and is designed to follow SOLID principles for maintainability and scalability.

## Tech Stack

- Node.js: v20.17.0
- pnpm: 9.11.0
- React: ^18.3.1
- TypeScript: ^5.5.3
- Vite: ^5.4.8

## Project Structure

```
src/
│
├── api/
├── components/
├── config/
├── contexts/
├── features/
├── hooks/
├── i18n/
├── lib/
├── pages/
├── routes/
├── services/
├── styles/
├── types/
├── App.tsx
└── main.tsx
```

### Folder Purposes and Guidelines

#### `api/`

**Purpose**: Handle all API-related logic and data fetching.

**Example**:

```typescript
// api/endpoints/deliveryEndpoints.ts
export const DELIVERY_ENDPOINTS = {
  getDeliveries: '/api/deliveries',
  updateDeliveryStatus: '/api/delivery/status',
};

// api/services/deliveryService.ts
import axios from 'axios';
import { DELIVERY_ENDPOINTS } from '../endpoints/deliveryEndpoints';

export const getDeliveries = async () => {
  const response = await axios.get(DELIVERY_ENDPOINTS.getDeliveries);
  return response.data;
};
```

**Guidelines**:

- Use axios or fetch for HTTP requests
- Implement error handling and request/response interceptors
- Use TypeScript interfaces for request/response types

#### `components/`

**Purpose**: Store reusable React components.

**Example**:

```tsx
// components/common/Button.tsx
import React from 'react';

interface ButtonProps {
  label: string;
  onClick: () => void;
}

export const Button: React.FC<ButtonProps> = ({ label, onClick }) => (
  <button onClick={onClick} className="bg-blue-500 text-white px-4 py-2 rounded">
    {label}
  </button>
);

// components/specific/DeliveryCard.tsx
import React from 'react';
import { Button } from '../common/Button';

interface DeliveryCardProps {
  id: string;
  address: string;
  status: string;
}

export const DeliveryCard: React.FC<DeliveryCardProps> = ({ id, address, status }) => (
  <div className="border p-4 rounded">
    <h3>Delivery #{id}</h3>
    <p>Address: {address}</p>
    <p>Status: {status}</p>
    <Button label="Update Status" onClick={() => console.log(`Update delivery ${id}`)} />
  </div>
);
```

**Guidelines**:

- Use functional components with hooks
- Implement TypeScript interfaces for props
- Keep components small and focused
- Use CSS modules or styled-components for styling

#### `config/`

**Purpose**: Store configuration files for different environments.

**Example**:

```typescript
// config/development.config.ts
export default {
  apiUrl: 'http://localhost:3000',
  featureFlags: {
    enableTracking: true,
    enableNotifications: false,
  },
};

// config/production.config.ts
export default {
  apiUrl: 'https://api.ourcompany.com',
  featureFlags: {
    enableTracking: true,
    enableNotifications: true,
  },
};
```

**Guidelines**:

- Use TypeScript for type-safe configurations
- Don't store sensitive information (use environment variables instead)
- Keep configurations flat and simple

#### `contexts/`

**Purpose**: Define React contexts for global state management.

**Example**:

```tsx
// contexts/DeliveryContext.tsx
import React, { createContext, useState, useContext } from 'react';

interface DeliveryContextType {
  deliveries: Delivery[];
  addDelivery: (delivery: Delivery) => void;
}

const DeliveryContext = createContext<DeliveryContextType | undefined>(undefined);

export const DeliveryProvider: React.FC = ({ children }) => {
  const [deliveries, setDeliveries] = useState<Delivery[]>([]);

  const addDelivery = (delivery: Delivery) => {
    setDeliveries([...deliveries, delivery]);
  };

  return (
    <DeliveryContext.Provider value={{ deliveries, addDelivery }}>
      {children}
    </DeliveryContext.Provider>
  );
};

export const useDelivery = () => {
  const context = useContext(DeliveryContext);
  if (context === undefined) {
    throw new Error('useDelivery must be used within a DeliveryProvider');
  }
  return context;
};
```

**Guidelines**:

- Use the Context API for app-wide state that doesn't change often
- Implement a custom hook for each context to simplify usage
- Keep context providers as high in the component tree as necessary

#### `features/`

**Purpose**: Implement feature-specific logic and components.

**Example**:

```tsx
// features/tracking/TrackingMap.tsx
import React from 'react';
import { useDelivery } from '../../contexts/DeliveryContext';

export const TrackingMap: React.FC = () => {
  const { deliveries } = useDelivery();

  return (
    <div className="tracking-map">
      {/* Implement map logic here */}
      {deliveries.map((delivery) => (
        <div key={delivery.id} style={{ position: 'absolute', left: delivery.x, top: delivery.y }}>
          📦
        </div>
      ))}
    </div>
  );
};

// features/tracking/index.ts
export { TrackingMap } from './TrackingMap';
```

**Guidelines**:

- Organize code by domain/feature (e.g., auth, delivery, tracking)
- Each feature folder can have its own components, hooks, and utils
- Use index files to expose public API of each feature

#### `hooks/`

**Purpose**: Store custom React hooks.

**Example**:

```typescript
// hooks/useDeliveryStatus.ts
import { useState, useEffect } from 'react';
import { getDeliveryStatus } from '../api/services/deliveryService';

export const useDeliveryStatus = (deliveryId: string) => {
  const [status, setStatus] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchStatus = async () => {
      try {
        const result = await getDeliveryStatus(deliveryId);
        setStatus(result);
      } catch (error) {
        console.error('Failed to fetch delivery status', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStatus();
  }, [deliveryId]);

  return { status, loading };
};
```

**Guidelines**:

- Prefix hook names with "use" (e.g., useDeliveryStatus)
- Keep hooks focused on a single responsibility
- Document hook parameters and return values

#### `i18n/`

**Purpose**: Handle internationalization.

**Example**:

```typescript
// i18n/languages/en.ts
export default {
  common: {
    submit: 'Submit',
    cancel: 'Cancel',
  },
  delivery: {
    status: {
      pending: 'Pending',
      inTransit: 'In Transit',
      delivered: 'Delivered',
    },
  },
};

// i18n/languageLoader.ts
import en from './languages/en';
import fr from './languages/fr';

export const languages = { en, fr };

export type LanguageKey = keyof typeof languages;

export const loadLanguage = (lang: LanguageKey) => languages[lang];
```

**Guidelines**:

- Use a consistent key structure across language files
- Implement a type-safe translation function
- Consider using i18next for complex internationalization needs

#### `lib/`

**Purpose**: Store utility libraries and helper functions.

**Example**:

```typescript
// lib/dateUtils.ts
export const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

// lib/mathUtils.ts
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  // Haversine formula implementation
  // ...
};
```

**Guidelines**:

- Keep libraries generic and reusable
- Document complex algorithms or business logic
- Use TypeScript for type safety

#### `pages/`

**Purpose**: Define top-level page components.

**Example**:

```tsx
// pages/DashboardPage.tsx
import React from 'react';
import { DeliveryList } from '../components/specific/DeliveryList';
import { TrackingMap } from '../features/tracking';

export const DashboardPage: React.FC = () => {
  return (
    <div className="dashboard">
      <h1>Delivery Dashboard</h1>
      <div className="flex">
        <DeliveryList />
        <TrackingMap />
      </div>
    </div>
  );
};
```

**Guidelines**:

- Each file should correspond to a route in your application
- Keep page components thin, delegating to other components
- Handle data fetching and state management here

#### `routes/`

**Purpose**: Define routing logic.

**Example**:

```tsx
// routes/AppRoutes.tsx
import React from 'react';
import { BrowserRouter as Router, Route, Switch } from 'react-router-dom';
import { DashboardPage } from '../pages/DashboardPage';
import { DeliveryDetailPage } from '../pages/DeliveryDetailPage';

export const AppRoutes: React.FC = () => (
  <Router>
    <Switch>
      <Route exact path="/" component={DashboardPage} />
      <Route path="/delivery/:id" component={DeliveryDetailPage} />
    </Switch>
  </Router>
);
```

**Guidelines**:

- Use React Router for routing
- Implement lazy loading for better performance
- Handle route guards and redirects here

#### `services/`

**Purpose**: Implement core business logic.

**Example**:

```typescript
// services/deliveryService.ts
import { Delivery } from '../types';
import { calculateDistance } from '../lib/mathUtils';

export const estimateDeliveryTime = (delivery: Delivery): number => {
  const distance = calculateDistance(
    delivery.startLat,
    delivery.startLon,
    delivery.endLat,
    delivery.endLon
  );
  const averageSpeed = 50; // km/h
  return distance / averageSpeed;
};
```

**Guidelines**:

- Keep services independent of UI components
- Use dependency injection for better testability
- Implement error handling and logging

#### `styles/`

**Purpose**: Store global styles and themes.

**Example**:

```css
/* styles/global.css */
:root {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --text-color: #333333;
}

body {
  font-family: 'Arial', sans-serif;
  color: var(--text-color);
}

.button {
  background-color: var(--primary-color);
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
}
```

**Guidelines**:

- Use CSS variables for theming
- Keep global styles minimal
- Consider using a CSS-in-JS solution for component-specific styles

#### `types/`

**Purpose**: Define global TypeScript types and interfaces.

**Example**:

```typescript
// types/index.d.ts
export interface Delivery {
  id: string;
  status: 'pending' | 'inTransit' | 'delivered';
  startAddress: string;
  endAddress: string;
  startLat: number;
  startLon: number;
  endLat: number;
  endLon: number;
  estimatedDeliveryTime: Date;
}

export type DeliveryStatus = Delivery['status'];
```

**Guidelines**:

- Keep types and interfaces generic and reusable
- Use namespaces to organize related types
- Don't duplicate types that are specific to a feature or component

### Naming Conventions

1. **Files and Folders**: Use camelCase for files and folders, except for React components which should use PascalCase.

   - Good: `useDeliveryStatus.ts`, `ApiService.ts`
   - Bad: `use-delivery-status.ts`, `api-service.ts`

2. **Components**: Use PascalCase for component names and files.

   - Good: `DeliveryCard.tsx`, `UserProfile.tsx`
   - Bad: `deliveryCard.tsx`, `userProfile.tsx`

3. **Functions**: Use camelCase for function names.

   - Good: `calculateDeliveryTime()`, `formatAddress()`
   - Bad: `CalculateDeliveryTime()`, `FormatAddress()`

4. **Constants**: Use UPPER_SNAKE_CASE for constant values.

   - Good: `MAX_DELIVERY_DISTANCE`, `DEFAULT_TIMEOUT`
   - Bad: `maxDeliveryDistance`, `defaultTimeout`

5. **Interfaces and Types**: Prefix interfaces with 'I' and types with 'T'.

   - Good: `IDeliveryOptions`, `TAddress`
   - Bad: `DeliveryOptions`, `Address`

6. **Enums**: Use PascalCase for enum names and values.
   - Good: `enum DeliveryStatus { Pending, InTransit, Delivered }`
   - Bad: `enum delivery_status { pending, in_transit, delivered }`

### Best Practices

1. **Code Formatting**: Use Prettier for consistent code formatting.

2. **Linting**: Use ESLint with the provided configuration. Run `pnpm lint` before committing.

3. **Documentation**: Use JSDoc comments for functions, components, and complex logic.

4. **Performance**: Use React.memo for expensive components. Implement virtualization for long lists.

5. **Accessibility**: Ensure all components are accessible. Use semantic HTML and ARIA attributes where necessary.

6. **State Management**: Use React Context for global state. Consider Zustand or Jotai for complex state management needs.

7. **Code Splitting**: Utilize dynamic imports and React.lazy for code splitting.

8. **Error Handling**: Implement error boundaries for graceful error handling in components.

9. **Environmental Variables**: Use `.env` files for environment-specific variables. Never commit sensitive information.

## Getting Started

1. Clone the repository
2. Install dependencies: `pnpm install`
3. Start the development server: `pnpm dev`
4. Build for production: `pnpm build`
5. Preview production build: `pnpm preview`

## Scripts

- `pnpm dev`: Start the development server
- `pnpm build`: Build the project for production
- `pnpm preview`: Preview the production build locally
- `pnpm lint`: Run ESLint to check for code style issues
- `pnpm lint:fix`: Run ESLint and automatically fix issues where possible
