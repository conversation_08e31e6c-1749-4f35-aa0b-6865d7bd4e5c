definitions:
  caches:
    pnpm: $BITBUCKET_CLONE_DIR/.pnpm-stores

pipelines:
  pull-requests:
    '**':
      - step:
          name: Build & test (PNPM)
          image: node:20.17.0
          caches:
            - pnpm
          script:
            - corepack enable
            - corepack prepare pnpm@9.15.1 --activate
            - pnpm install
            - pnpm run build

  branches:
    develop:
      - step:
          name: Build for dev-release
          image: node:20.17.0
          caches:
            - pnpm
          script:
            - corepack enable
            - corepack prepare pnpm@9.15.1 --activate
            - pnpm install
            - pnpm run build
          artifacts:
            - dist/**
            - docker-compose.yml
            - nginx.conf
            - deploy.sh

      - step:
          name: Deploy to Server
          deployment: production
          image: atlassian/default-image:2
          script:
            # Ensure script stops on any failure
            - cd $BITBUCKET_CLONE_DIR
            - set -euxo pipefail

            # Make deploy script executable
            - chmod +x deploy.sh

            # Compress dist folder
            - echo ">>> Compressing dist folder..."
            - tar -czf dist.tar.gz dist

            # Copy all necessary files to the server
            - echo ">>> Copying artifacts to the server..."
            - scp -o StrictHostKeyChecking=no dist.tar.gz $SSH_USER@$SSH_HOST:/root/transportapp-ui
            - scp -o StrictHostKeyChecking=no docker-compose.yml $SSH_USER@$SSH_HOST:/root/transportapp-ui
            - scp -o StrictHostKeyChecking=no nginx.conf $SSH_USER@$SSH_HOST:/root/transportapp-ui
            - scp -o StrictHostKeyChecking=no deploy.sh $SSH_USER@$SSH_HOST:/root/transportapp-ui

            # SSH into the server and execute the deployment script
            - echo ">>> Executing remote deployment script..."
            - |
              ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST << 'EOF'
              set -euxo pipefail

              echo ">>> Making deploy.sh executable..."
              chmod +x /root/transportapp-ui/deploy.sh

              echo ">>> Running deploy.sh..."
              /root/transportapp-ui/deploy.sh | tee /root/transportapp-ui/deploy.log

              echo ">>> Deployment script executed. Logs saved in deploy.log."
              EOF
